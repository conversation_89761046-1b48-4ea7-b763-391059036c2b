import { Response, NextFunction } from 'express';
import User from '../models/User';
import { JWTUtils } from '../utils/jwt';
import { AuthenticatedRequest, ApiResponse } from '../types';

export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response<ApiResponse>,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    console.log(`🔐 Auth middleware - checking token for ${req.method} ${req.path}`);

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log(`❌ Auth middleware - no valid auth header`);
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!token) {
      console.log(`❌ Auth middleware - no token found`);
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }

    console.log(`🔍 Auth middleware - verifying token: ${token.substring(0, 20)}...`);

    // Verify the token
    const decoded = JWTUtils.verifyAccessToken(token);
    console.log(`✅ Auth middleware - token decoded successfully for user: ${decoded.userId}`);

    // Find the user
    const user = await User.findById(decoded.userId).select('+password');

    if (!user) {
      console.log(`❌ Auth middleware - user not found: ${decoded.userId}`);
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }

    if (!user.isActive) {
      console.log(`❌ Auth middleware - user account deactivated: ${decoded.userId}`);
      res.status(401).json({
        success: false,
        message: 'User not authenticated',
      });
      return;
    }

    console.log(`✅ Auth middleware - authentication successful for user: ${user.email}`);
    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    console.log(`❌ Auth middleware - token verification failed:`, error);
    res.status(401).json({
      success: false,
      message: 'User not authenticated',
      error: error instanceof Error ? error.message : 'Authentication failed',
    });
  }
};

export const authorize = (...roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response<ApiResponse>, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
      return;
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
      });
      return;
    }

    next();
  };
};

// Helper function to authorize any supplier role (admin or employee)
export const authorizeSupplier = () => {
  return (req: AuthenticatedRequest, res: Response<ApiResponse>, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
      return;
    }

    if (!['supplier-admin', 'supplier-employee'].includes(req.user.role)) {
      res.status(403).json({
        success: false,
        message: 'Access denied. Supplier access required.',
      });
      return;
    }

    next();
  };
};

export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      if (token) {
        try {
          const decoded = JWTUtils.verifyAccessToken(token);
          const user = await User.findById(decoded.userId);
          
          if (user && user.isActive) {
            req.user = user;
          }
        } catch (error) {
          // Ignore token errors for optional auth
        }
      }
    }
    
    next();
  } catch (error) {
    // Ignore errors for optional auth
    next();
  }
};
