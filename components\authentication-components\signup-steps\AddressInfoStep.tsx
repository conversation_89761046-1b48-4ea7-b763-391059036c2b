import React from 'react';
import { YStack, Text, H5, XStack } from 'tamagui';
import { useSignupStore } from '../useSignupStore';
import { CustomTextField } from '../../CustomTextField';
import { Ionicons } from '@expo/vector-icons';
import { Pressable } from 'react-native';
import { useTranslation } from 'react-i18next';

export const AddressInfoStep = () => {
  const { t } = useTranslation();
  const { signupData, updateSignupData } = useSignupStore();

  const userTypeOptions = [
    {
      value: 'customer',
      label: t('signup.addressInfo.customer', { defaultValue: 'Customer' }),
      icon: 'person',
      description: t('signup.addressInfo.customerDescription', { defaultValue: 'I want to order from suppliers' })
    },
    {
      value: 'supplier',
      label: t('signup.addressInfo.supplier', { defaultValue: 'Supplier' }),
      icon: 'storefront',
      description: t('signup.addressInfo.supplierDescription', { defaultValue: 'I want to sell products/services' })
    },
  ];

  return (
    <YStack gap="$4" paddingTop="$2">
      <YStack gap="$2" alignItems="center" marginBottom="$4">
        <Ionicons name="location" size={48} color="#7529B3" />
        <H5 textAlign="center" color="$primary"><Text>Address & Account Type</Text></H5>
        <Text textAlign="center" color="$gray10" fontSize="$3">
          Tell us where you're located and how you'll use the app
        </Text>
      </YStack>

      <YStack gap="$4">
        <CustomTextField
          label={t('signup.addressInfo.address', { defaultValue: 'Address' })}
          placeholder={t('signup.addressInfo.enterAddress', { defaultValue: 'Enter your full address' })}
          value={signupData.address}
          onChangeText={(text) => updateSignupData({ address: text })}
          icon="home"
          required
        />

        <CustomTextField
          label={t('signup.addressInfo.city', { defaultValue: 'City' })}
          placeholder={t('signup.addressInfo.enterCity', { defaultValue: 'Enter your city' })}
          value={signupData.city}
          onChangeText={(text) => updateSignupData({ city: text })}
          icon="business"
          required
        />

        <CustomTextField
          label={t('signup.addressInfo.country', { defaultValue: 'Country' })}
          placeholder={t('signup.addressInfo.enterCountry', { defaultValue: 'Enter your country' })}
          value={signupData.country}
          onChangeText={(text) => updateSignupData({ country: text })}
          icon="flag"
          required
        />
      </YStack>

      <YStack gap="$3" marginTop="$4">
        <Text fontSize="$4" fontWeight="600" color="$gray11">
          {t('signup.addressInfo.accountType', { defaultValue: 'Account Type' })} *
        </Text>
        <YStack gap="$3">
          {userTypeOptions.map((option) => (
            <Pressable
              key={option.value}
              onPress={() => updateSignupData({ userType: option.value as any })}
              style={{
                padding: 16,
                borderRadius: 12,
                borderWidth: 2,
                borderColor: signupData.userType === option.value ? '#7529B3' : '#e5e5e5',
                backgroundColor: signupData.userType === option.value ? '#f3f0ff' : '#fff',
              }}
            >
              <XStack alignItems="center" gap="$3">
                <Ionicons 
                  name={option.icon as any} 
                  size={24} 
                  color={signupData.userType === option.value ? '#7529B3' : '#666'} 
                />
                <YStack flex={1}>
                  <Text 
                    fontSize="$4" 
                    fontWeight="600"
                    color={signupData.userType === option.value ? '$primary' : '$gray11'}
                  >
                    {option.label}
                  </Text>
                  <Text 
                    fontSize="$3" 
                    color={signupData.userType === option.value ? '$primary' : '$gray9'}
                  >
                    {option.description}
                  </Text>
                </YStack>
                {signupData.userType === option.value && (
                  <Ionicons name="checkmark-circle" size={20} color="#7529B3" />
                )}
              </XStack>
            </Pressable>
          ))}
        </YStack>
      </YStack>

      <YStack gap="$2" marginTop="$3">
        <Text fontSize="$2" color="$gray9" textAlign="center">
          You can change your account type later in settings
        </Text>
      </YStack>
    </YStack>
  );
};
