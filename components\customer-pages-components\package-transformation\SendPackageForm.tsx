import { useState } from 'react';
import { <PERSON><PERSON>View, Alert } from 'react-native';
import { Button, Input, Label, Text, View, YStack, Card, Separator, XStack, H3, H4 } from 'tamagui';
import { useRouter } from 'expo-router';
import { <PERSON><PERSON><PERSON>iew } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useSendPackageStore, useSetSendPackage } from './useSendPackageStore';
import { createPackage } from '../../../services/apiService';
import { CustomTextField } from '../../CustomTextField';
import { useTranslation } from 'react-i18next';

export function SendPackageForm() {
  const router = useRouter();
  const { t } = useTranslation();
  const {
    pickup,
    dropoff,
    receiverName,
    receiverPhone,
    packageType,
    notes,
    updateField,
    setPickup,
    setDropoff,
    reset
  } = useSetSendPackage();

  // Add local state for sender information and package description
  const [senderName, setSenderName] = useState('');
  const [senderPhone, setSenderPhone] = useState('');
  const [packageDescription, setPackageDescription] = useState('');
  const { addSendRequest } = useSendPackageStore();

  const isFormValid = () =>
    pickup &&
    dropoff &&
    senderName.trim() &&
    senderPhone.trim() &&
    receiverName.trim() &&
    receiverPhone.trim() &&
    packageType.trim() &&
    packageDescription.trim().length >= 5;

  return (
    <>
      {/* Enhanced Professional Header */}
      <View
        width="100%"
        style={{
          paddingTop: 50,
          paddingBottom: 30,
          paddingHorizontal: 0,
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
          backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          backgroundColor: '#667eea',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
          <YStack gap="$3" alignItems="center">
            <View
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 20,
                padding: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="send" size={32} color="white" />
            </View>
            <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
              {t('sendPackage.title', { defaultValue: 'Send a Package' })}
            </Text>
            <Text fontSize="$4" color="rgba(255,255,255,0.9)" textAlign="center" maxWidth={280}>
              {t('sendPackage.subtitle', { defaultValue: 'Fast & reliable package delivery service' })}
            </Text>
          </YStack>
        </MotiView>
      </View>

      <ScrollView
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}
        style={{ backgroundColor: '#f8fafc', width: '100%' }}
      >
        <YStack gap="$5" px="$0" py="$4" width="100%">
          {/* Locations Section */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 200, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="location" size={20} color="#667eea" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('sendPackage.locations', { defaultValue: 'Delivery Locations' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('sendPackage.pickupAddress', { defaultValue: 'Pickup Address' })}<Text color="$red9">*</Text>
                    </Label>
                    <XStack gap="$3" ai="center">
                      <XStack
                        flex={1}
                        alignItems="center"
                        gap="$3"
                        padding="$3"
                        borderWidth={2}
                        borderColor="$gray6"
                        borderRadius="$6"
                        backgroundColor="$background"
                        pressStyle={{ borderColor: '$primary' }}
                      >
                        <Ionicons name="location-outline" size={20} color="#667eea" />
                        <Text
                          flex={1}
                          fontSize="$4"
                          color={pickup?.address ? '$gray12' : '$gray8'}
                        >
                          {pickup?.address || t('sendPackage.setOnMap', { defaultValue: 'Set location on map' })}
                        </Text>
                      </XStack>
                      <Button
                        size="$4"
                        bg="$primary"
                        color="white"
                        br="$6"
                        fontWeight="600"
                        pressStyle={{ bg: '$primaryPress' }}
                        onPress={() => router.push('/home/<USER>')}
                      >
                        {t('common.set', { defaultValue: 'Set' })}
                      </Button>
                    </XStack>
                  </YStack>

                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('sendPackage.deliveryAddress', { defaultValue: 'Delivery Address' })}<Text color="$red9">*</Text>
                    </Label>
                    <XStack gap="$3" ai="center">
                      <XStack
                        flex={1}
                        alignItems="center"
                        gap="$3"
                        padding="$3"
                        borderWidth={2}
                        borderColor="$gray6"
                        borderRadius="$6"
                        backgroundColor="$background"
                        pressStyle={{ borderColor: '$primary' }}
                      >
                        <Ionicons name="flag-outline" size={20} color="#764ba2" />
                        <Text
                          flex={1}
                          fontSize="$4"
                          color={dropoff?.address ? '$gray12' : '$gray8'}
                        >
                          {dropoff?.address || t('sendPackage.setOnMap', { defaultValue: 'Set location on map' })}
                        </Text>
                      </XStack>
                      <Button
                        size="$4"
                        bg="$primary"
                        color="white"
                        br="$6"
                        fontWeight="600"
                        pressStyle={{ bg: '$primaryPress' }}
                        onPress={() => router.push('/home/<USER>')}
                      >
                        {t('common.set', { defaultValue: 'Set' })}
                      </Button>
                    </XStack>
                  </YStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Sender Info */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 300, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="person-circle" size={20} color="#667eea" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('sendPackage.senderInfo', { defaultValue: 'Sender Information' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <CustomTextField
                    icon="person-outline"
                    label={t('sendPackage.senderName', { defaultValue: 'Your Full Name' })}
                    placeholder={t('sendPackage.senderNamePlaceholder', { defaultValue: 'e.g. Mohammad Ali' })}
                    value={senderName}
                    onChangeText={setSenderName}
                    required
                    autoCapitalize="words"
                  />

                  <CustomTextField
                    icon="call-outline"
                    label={t('sendPackage.senderPhone', { defaultValue: 'Your Phone Number' })}
                    placeholder={t('sendPackage.senderPhonePlaceholder', { defaultValue: 'e.g. 0599123456' })}
                    value={senderPhone}
                    onChangeText={setSenderPhone}
                    keyboardType="phone-pad"
                    required
                  />
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Receiver Info */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 500, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="person" size={20} color="#667eea" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('sendPackage.receiverInfo', { defaultValue: 'Receiver Information' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <CustomTextField
                    icon="person-outline"
                    label={t('sendPackage.fullName', { defaultValue: 'Full Name' })}
                    placeholder={t('sendPackage.fullNamePlaceholder', { defaultValue: 'e.g. Ahmad Jaber' })}
                    value={receiverName}
                    onChangeText={(text) => updateField('receiverName', text)}
                    required
                    autoCapitalize="words"
                  />

                  <CustomTextField
                    icon="call-outline"
                    label={t('sendPackage.phoneNumber', { defaultValue: 'Phone Number' })}
                    placeholder={t('sendPackage.phonePlaceholder', { defaultValue: 'e.g. 0599123456' })}
                    value={receiverPhone}
                    onChangeText={(text) => updateField('receiverPhone', text)}
                    keyboardType="phone-pad"
                    required
                  />
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Package Info */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 700, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="cube" size={20} color="#667eea" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('sendPackage.packageDetails', { defaultValue: 'Package Details' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <CustomTextField
                    icon="pricetag-outline"
                    label={t('sendPackage.packageType', { defaultValue: 'Package Type' })}
                    placeholder={t('sendPackage.packageTypePlaceholder', { defaultValue: 'e.g. Fragile, Electronics' })}
                    value={packageType}
                    onChangeText={(text) => updateField('packageType', text)}
                    required
                    autoCapitalize="words"
                  />

                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('sendPackage.packageDescription', { defaultValue: 'Package Description' })}<Text color="$red9">*</Text>
                    </Label>
                    <XStack
                      alignItems="flex-start"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor={packageDescription.trim().length >= 5 ? "$gray6" : "$red6"}
                      borderRadius="$6"
                      backgroundColor="$background"
                      minHeight={80}
                      focusStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="document-text-outline" size={20} color="#667eea" style={{ marginTop: 2 }} />
                      <Input
                        flex={1}
                        fontSize="$4"
                        placeholder={t('sendPackage.packageDescriptionPlaceholder', { defaultValue: 'Describe the package contents (minimum 5 characters)' })}
                        value={packageDescription}
                        onChangeText={setPackageDescription}
                        multiline
                        numberOfLines={3}
                        borderWidth={0}
                        backgroundColor="transparent"
                        focusStyle={{
                          borderWidth: 0,
                          backgroundColor: "transparent"
                        }}
                      />
                    </XStack>
                    {packageDescription.trim().length > 0 && packageDescription.trim().length < 5 && (
                      <Text color="$red9" fontSize="$3">
                        {t('sendPackage.descriptionMinLength', { defaultValue: 'Description must be at least 5 characters long' })}
                      </Text>
                    )}
                  </YStack>

                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('sendPackage.notes', { defaultValue: 'Special Instructions' })}
                      <Text color="$gray8" fontSize="$3"> ({t('common.optional', { defaultValue: 'optional' })})</Text>
                    </Label>
                    <XStack
                      alignItems="flex-start"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor="$gray6"
                      borderRadius="$6"
                      backgroundColor="$background"
                      minHeight={80}
                      focusStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="document-text-outline" size={20} color="#667eea" style={{ marginTop: 2 }} />
                      <Input
                        flex={1}
                        fontSize="$4"
                        placeholder={t('sendPackage.notesPlaceholder', { defaultValue: 'Optional instructions for the driver' })}
                        value={notes}
                        onChangeText={(text) => updateField('notes', text)}
                        multiline
                        numberOfLines={3}
                        borderWidth={0}
                        backgroundColor="transparent"
                        focusStyle={{
                          borderWidth: 0,
                          backgroundColor: "transparent"
                        }}
                      />
                    </XStack>
                  </YStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Confirm Button */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 900, type: 'spring', damping: 15 }}
          >
            <Button
              width="100%"
              height={60}
              size="$6"
              bg={isFormValid() ? "$primary" : "$gray8"}
              color="white"
              br="$8"
              fontSize="$5"
              fontWeight="700"
              icon={<Ionicons name="send" size={24} color="white" />}
              disabled={!isFormValid()}
              opacity={isFormValid() ? 1 : 0.6}
              pressStyle={{
                bg: isFormValid() ? "$primaryPress" : "$gray8",
                scale: 0.98
              }}
              style={{
                shadowColor: isFormValid() ? '#667eea' : '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: isFormValid() ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: 8,
              }}
            onPress={async () => {
              try {
                // Validate required fields
                if (!senderName.trim()) {
                  Alert.alert('Error', 'Please provide your full name');
                  return;
                }
                if (!senderPhone.trim()) {
                  Alert.alert('Error', 'Please provide your phone number');
                  return;
                }
                if (!pickup?.address?.trim()) {
                  Alert.alert('Error', 'Please provide a pickup address');
                  return;
                }
                if (!dropoff?.address?.trim()) {
                  Alert.alert('Error', 'Please provide a delivery address');
                  return;
                }
                if (!receiverName.trim()) {
                  Alert.alert('Error', 'Please provide recipient name');
                  return;
                }
                if (!receiverPhone.trim()) {
                  Alert.alert('Error', 'Please provide recipient phone number');
                  return;
                }
                if (!packageType.trim()) {
                  Alert.alert('Error', 'Please provide package type');
                  return;
                }
                if (!packageDescription.trim() || packageDescription.trim().length < 5) {
                  Alert.alert('Error', 'Please provide a package description (minimum 5 characters)');
                  return;
                }

                // Format phone numbers to ensure they start with +
                let formattedReceiverPhone = receiverPhone.trim();
                if (!formattedReceiverPhone.startsWith('+')) {
                  // Assume Palestinian number if no country code
                  if (formattedReceiverPhone.startsWith('0')) {
                    formattedReceiverPhone = '+970' + formattedReceiverPhone.substring(1);
                  } else {
                    formattedReceiverPhone = '+970' + formattedReceiverPhone;
                  }
                }

                let formattedSenderPhone = senderPhone.trim();
                if (!formattedSenderPhone.startsWith('+')) {
                  // Assume Palestinian number if no country code
                  if (formattedSenderPhone.startsWith('0')) {
                    formattedSenderPhone = '+970' + formattedSenderPhone.substring(1);
                  } else {
                    formattedSenderPhone = '+970' + formattedSenderPhone;
                  }
                }

                // Prepare package data for backend
                const packageData = {
                  senderName: senderName.trim(),
                  senderPhone: formattedSenderPhone,
                  recipientInfo: {
                    name: receiverName.trim(),
                    phone: formattedReceiverPhone
                  },
                  pickupAddress: {
                    street: pickup.address.trim(),
                    city: 'Nablus', // Default city
                    coordinates: {
                      lat: pickup.lat || 32.2211,
                      lng: pickup.lng || 35.2544
                    }
                  },
                  deliveryAddress: {
                    street: dropoff.address.trim(),
                    city: 'Nablus', // Default city
                    coordinates: {
                      lat: dropoff.lat || 32.2211,
                      lng: dropoff.lng || 35.2544
                    }
                  },
                  packageDetails: {
                    type: packageType.trim(),
                    description: packageDescription.trim(),
                    size: 'medium' as const, // Default size
                    weight: '1' // Default weight as string (backend expects string)
                  },
                  priority: 'standard' as const,
                  paymentMethod: 'cash' as const,
                  notes: notes?.trim() || ''
                };

                // Create package via backend API
                const createdPackage = await createPackage(packageData);

                // Add to local store for immediate UI update
                addSendRequest({
                  id: createdPackage.trackingNumber,
                  pickup,
                  dropoff,
                  receiverName,
                  receiverPhone,
                  packageType,
                  notes,
                  driverName: 'Ali Alaa', // temporary
                  driverPhone: '0595959595',
                  status: 'pending',
                  createdAt: new Date().toISOString(),
                  estimatedTime: '30-45 mins',
                  cost: createdPackage.cost
                });

                // Reset all form fields
                reset();
                setSenderName('');
                setSenderPhone('');
                setPackageDescription('');
                router.push('/home/<USER>');
              } catch (error) {
                console.error('Error creating package:', error);

                // Extract error message properly
                let errorMessage = 'Failed to create package delivery request. Please try again.';
                if (error && typeof error === 'object') {
                  if ('message' in error && typeof error.message === 'string') {
                    errorMessage = error.message;
                  } else if ('errors' in error && Array.isArray(error.errors) && error.errors.length > 0) {
                    errorMessage = error.errors[0].msg || errorMessage;
                  }
                }

                Alert.alert('Error', errorMessage);
              }
            }}
            >
              {t('sendPackage.confirmRequest', { defaultValue: 'Confirm Request' })}
            </Button>
          </MotiView>
        </YStack>
      </ScrollView>
    </>
  );
}