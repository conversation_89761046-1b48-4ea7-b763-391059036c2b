import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, User, Mail, MapPin, Users } from 'lucide-react';
import { useForm } from 'react-hook-form';
import LocationPicker from '../../../components/LocationPicker';

interface AddEmployeeModalProps {
  onClose: () => void;
  onSubmit: (data: any) => Promise<{ success: boolean; message?: string }>;
}

interface EmployeeFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  username: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female';
  address: string;
  city: string;
  country: string;
  location?: [number, number]; // [longitude, latitude]
  notifications: boolean;
}

const AddEmployeeModal: React.FC<AddEmployeeModalProps> = ({ onClose, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<[number, number] | undefined>();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<EmployeeFormData>({
    defaultValues: {
      notifications: true,
    },
  });

  const watchedPassword = watch('password');



  const onFormSubmit = async (data: EmployeeFormData) => {
    if (data.password !== data.confirmPassword) {
      setSubmitError('Passwords do not match');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      const { confirmPassword, ...submitData } = data;
      // Include location if selected
      if (selectedLocation) {
        submitData.location = selectedLocation;
      }
      const result = await onSubmit(submitData);
      
      if (!result.success) {
        setSubmitError(result.message || 'Failed to add employee');
      }
    } catch (error) {
      setSubmitError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="relative bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900 border border-white/20 rounded-3xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden"
        >
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

          {/* Scrollable Content */}
          <div className="max-h-[90vh] overflow-y-auto"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(255, 255, 255, 0.3) transparent'
            }}
          >
            {/* Enhanced Header */}
            <div className="relative p-8 border-b border-white/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="p-3 bg-gradient-to-r from-emerald-500 to-green-600 rounded-2xl"
                  >
                    <Users className="text-white" size={28} />
                  </motion.div>
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-1">Add New Team Member</h2>
                    <p className="text-white/70">Create a new employee account with full access control</p>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-3 bg-white/10 hover:bg-white/20 rounded-2xl text-white/80 hover:text-white transition-all duration-300"
                >
                  <X size={24} />
                </motion.button>
              </div>

              {/* Progress indicator */}
              <div className="mt-6">
                <div className="flex items-center gap-2 text-white/60 text-sm">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                  <span>Step 1 of 1 - Employee Information</span>
                </div>
              </div>
            </div>

            {/* Enhanced Form */}
            <form onSubmit={handleSubmit(onFormSubmit)} className="p-8 space-y-8">
              {submitError && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-500/20 border border-red-500/30 text-red-400 px-6 py-4 rounded-2xl backdrop-blur-sm"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    {submitError}
                  </div>
                </motion.div>
              )}

              {/* Basic Information Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-500/20 rounded-xl">
                    <User size={24} className="text-blue-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Basic Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      {...register('firstName', {
                        required: 'First name is required',
                        minLength: { value: 1, message: 'First name cannot be empty' },
                        maxLength: { value: 50, message: 'First name cannot exceed 50 characters' },
                        pattern: { value: /^[a-zA-Z\s]+$/, message: 'First name can only contain letters and spaces' }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter first name"
                    />
                    {errors.firstName && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.firstName.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      {...register('lastName', {
                        required: 'Last name is required',
                        minLength: { value: 1, message: 'Last name cannot be empty' },
                        maxLength: { value: 50, message: 'Last name cannot exceed 50 characters' },
                        pattern: { value: /^[a-zA-Z\s]+$/, message: 'Last name can only contain letters and spaces' }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter last name"
                    />
                    {errors.lastName && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.lastName.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Username *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        {...register('username', {
                          required: 'Username is required',
                          minLength: { value: 3, message: 'Username must be at least 3 characters' },
                          maxLength: { value: 30, message: 'Username cannot exceed 30 characters' },
                          pattern: {
                            value: /^[a-zA-Z0-9_]+$/,
                            message: 'Username can only contain letters, numbers, and underscores'
                          }
                        })}
                        className="w-full px-4 py-3 pl-8 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                        placeholder="Enter username"
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60">
                        @
                      </div>
                    </div>
                    {errors.username && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.username.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Gender
                    </label>
                    <select
                      {...register('gender')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 appearance-none cursor-pointer"
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.75rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em'
                      }}
                    >
                      <option value="" className="bg-slate-800 text-white">Select gender</option>
                      <option value="male" className="bg-slate-800 text-white">Male</option>
                      <option value="female" className="bg-slate-800 text-white">Female</option>
                    </select>
                  </div>
                </div>
              </motion.div>

              {/* Contact Information Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-emerald-500/20 rounded-xl">
                    <Mail size={24} className="text-emerald-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Contact Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      {...register('email', {
                        required: 'Email is required',
                        pattern: {
                          value: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                          message: 'Please enter a valid email address'
                        }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.email.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      {...register('phoneNumber', {
                        required: 'Phone number is required',
                        pattern: {
                          value: /^\+?[\d\s-()]+$/,
                          message: 'Please enter a valid phone number'
                        }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-300"
                      placeholder="+****************"
                    />
                    {errors.phoneNumber && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.phoneNumber.message}
                      </motion.p>
                    )}
                  </div>
                </div>
              </motion.div>

              {/* Security Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-red-500/20 rounded-xl">
                    <User size={24} className="text-red-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Security & Access</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Password *
                    </label>
                    <input
                      type="password"
                      {...register('password', {
                        required: 'Password is required',
                        minLength: {
                          value: 6,
                          message: 'Password must be at least 6 characters'
                        },
                        pattern: {
                          value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
                          message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
                        }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                      placeholder="Create secure password"
                    />
                    {errors.password && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.password.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Confirm Password *
                    </label>
                    <input
                      type="password"
                      {...register('confirmPassword', {
                        required: 'Please confirm password',
                        validate: value => value === watchedPassword || 'Passwords do not match'
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                      placeholder="Confirm password"
                    />
                    {errors.confirmPassword && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.confirmPassword.message}
                      </motion.p>
                    )}
                  </div>
                </div>
              </motion.div>

              {/* Additional Information Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-purple-500/20 rounded-xl">
                    <MapPin size={24} className="text-purple-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Additional Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Date of Birth
                    </label>
                    <input
                      type="date"
                      {...register('dateOfBirth')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      City
                    </label>
                    <input
                      type="text"
                      {...register('city', {
                        maxLength: { value: 50, message: 'City cannot exceed 50 characters' }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter city"
                    />
                    {errors.city && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.city.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Country
                    </label>
                    <input
                      type="text"
                      {...register('country', {
                        maxLength: { value: 50, message: 'Country cannot exceed 50 characters' }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter country"
                    />
                    {errors.country && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.country.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Address
                    </label>
                    <input
                      type="text"
                      {...register('address', {
                        maxLength: { value: 200, message: 'Address cannot exceed 200 characters' }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter full address"
                    />
                    {errors.address && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.address.message}
                      </motion.p>
                    )}
                  </div>
                </div>

                {/* Location Picker */}
                <div className="mt-6">
                  <LocationPicker
                    onLocationSelect={setSelectedLocation}
                    initialLocation={selectedLocation}
                  />
                </div>
              </motion.div>

              {/* Preferences Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-yellow-500/20 rounded-xl">
                    <User size={24} className="text-yellow-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Employee Preferences</h3>
                </div>

                <div className="p-6 bg-white/5 border border-white/20 rounded-2xl">
                  <label className="flex items-center gap-4 cursor-pointer">
                    <input
                      type="checkbox"
                      {...register('notifications')}
                      className="w-5 h-5 text-emerald-600 bg-white/10 border-white/20 rounded focus:ring-emerald-500 focus:ring-2"
                    />
                    <div>
                      <span className="text-white font-medium">Enable Notifications</span>
                      <p className="text-white/60 text-sm mt-1">
                        Allow this employee to receive order updates, system alerts, and important announcements
                      </p>
                    </div>
                  </label>
                </div>
              </motion.div>

              {/* Enhanced Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="flex gap-4 pt-8 border-t border-white/20"
              >
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onClose}
                  className="flex-1 px-6 py-4 bg-white/10 border border-white/20 text-white rounded-2xl font-medium hover:bg-white/20 transition-all duration-300"
                >
                  Cancel
                </motion.button>

                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  whileHover={!isSubmitting ? { scale: 1.02, y: -2 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                  className="flex-1 px-6 py-4 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-2xl font-bold hover:from-emerald-600 hover:to-green-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
                >
                  {!isSubmitting && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:animate-[shimmer_1s_ease-in-out] pointer-events-none" />
                  )}

                  <div className="flex items-center justify-center gap-2">
                    {isSubmitting ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                        />
                        Adding Employee...
                      </>
                    ) : (
                      <>
                        <Users size={20} />
                        Add Employee
                      </>
                    )}
                  </div>
                </motion.button>
              </motion.div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AddEmployeeModal;
