import { ScrollView, RefreshControl, Dimensions, StyleSheet, StatusBar, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useMyOrdersStore } from './useMyOrdersStore';
import { Text, View, YStack, XStack, Card, Separator, Button, Image, H3, H4, Progress, Input } from 'tamagui';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { router, useFocusEffect } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { apiService } from '../../../services/apiService';
import { useCurrentUserData } from '../../useCurrentUserData';
import { showErrorAlert } from '../../../utils/errorDisplay';
import { AuthCheck, AuthDebugInfo } from '../../common/AuthCheck';
import { LinearGradient } from 'expo-linear-gradient';

// Responsive design hook
const useResponsive = () => {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });
    return () => subscription?.remove();
  }, []);

  const isTablet = screenData.width >= 768;
  const isDesktop = screenData.width >= 1024;

  const getResponsiveValue = (mobile: number, tablet: number, desktop: number) => {
    if (isDesktop) return desktop;
    if (isTablet) return tablet;
    return mobile;
  };

  return { isTablet, isDesktop, getResponsiveValue, screenWidth: screenData.width };
};

export function CustomerOrdersGUI() {
  const { t } = useTranslation();
  const { orders, setOrders } = useMyOrdersStore();
  const { user, loadUserProfile, initializeUser } = useCurrentUserData();
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Responsive design
  const { isTablet, isDesktop, getResponsiveValue, screenWidth } = useResponsive();
  const insets = useSafeAreaInsets();

  // Ensure user is loaded when component mounts
  useEffect(() => {
    const initializeUserData = async () => {
      console.log('🔄 Orders page: Initializing user data...');
      if (!user) {
        console.log('🔄 Orders page: User not found, attempting to initialize...');
        try {
          await initializeUser();
        } catch (error) {
          console.log('❌ Orders page: Failed to initialize user:', error);
        }
      } else {
        console.log('✅ Orders page: User already loaded:', user.email);
      }
    };
    initializeUserData();
  }, [user, initializeUser]);

  // Load orders when screen comes into focus (when user navigates to this page)
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 Orders page: Screen focused, loading orders');
      if (user) {
        const params: any = {};

        // Add status filter if not 'All'
        if (selectedFilter !== 'All') {
          const statusMap: Record<string, string> = {
            'Pending': 'pending',
            'Preparing': 'preparing',
            'On the Way': 'out_for_delivery',
            'Delivered': 'delivered'
          };
          params.status = statusMap[selectedFilter] || selectedFilter.toLowerCase();
        }

        // Add search query if exists
        if (searchQuery.trim()) {
          params.search = searchQuery.trim();
        }

        fetchOrders(params);
      }
    }, [user, selectedFilter, searchQuery])
  );

  // Also fetch when filters change (for real-time filtering)
  useEffect(() => {
    if (user) {
      const params: any = {};

      // Add status filter if not 'All'
      if (selectedFilter !== 'All') {
        const statusMap: Record<string, string> = {
          'Pending': 'pending',
          'Preparing': 'preparing',
          'On the Way': 'out_for_delivery',
          'Delivered': 'delivered'
        };
        params.status = statusMap[selectedFilter] || selectedFilter.toLowerCase();
      }

      // Add search query if exists
      if (searchQuery.trim()) {
        params.search = searchQuery.trim();
      }

      fetchOrders(params);
    }
  }, [selectedFilter, searchQuery]); // Re-fetch when filter or search changes (but not user to avoid double loading)

  // Enhanced order statistics
  const orderStats = useMemo(() => {
    const stats = {
      total: orders.length,
      pending: orders.filter(o => o.status === 'Pending').length,
      preparing: orders.filter(o => o.status === 'Preparing').length,
      onTheWay: orders.filter(o => o.status === 'On the Way').length,
      delivered: orders.filter(o => o.status === 'Delivered').length,
      totalSpent: orders.reduce((sum, order) => sum + order.total, 0),
      averageOrderValue: orders.length > 0 ? orders.reduce((sum, order) => sum + order.total, 0) / orders.length : 0
    };
    return stats;
  }, [orders]);

  // Since backend now handles filtering, we use orders directly
  const filteredOrders = orders;

  // Pull to refresh functionality
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Re-fetch orders with current filters
      const params: any = {};

      // Add status filter if not 'All'
      if (selectedFilter !== 'All') {
        const statusMap: Record<string, string> = {
          'Pending': 'pending',
          'Preparing': 'preparing',
          'On the Way': 'out_for_delivery',
          'Delivered': 'delivered'
        };
        params.status = statusMap[selectedFilter] || selectedFilter.toLowerCase();
      }

      // Add search query if exists
      if (searchQuery.trim()) {
        params.search = searchQuery.trim();
      }

      await fetchOrders(params);
    } catch (error) {
      console.error('Error refreshing orders:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Extract fetchOrders function for reuse with enhanced backend integration
  const fetchOrders = async (params?: { page?: number; limit?: number; status?: string; search?: string }) => {
    try {
      setLoading(true);
      setAuthError(false);

      // Clear any existing orders first to ensure fresh data
      setOrders([]);

      // Check if user is authenticated
      if (!user) {
        console.log('User not authenticated, skipping orders fetch');
        setAuthError(true);
        setLoading(false);
        return;
      }

      console.log('Fetching orders from backend for user:', user.email, 'with params:', params);

      // Force reload tokens to ensure we have the latest ones
      await apiService.forceReloadTokens();

      // Check authentication status
      const isAuth = await apiService.isAuthenticated();
      const tokenInfo = await apiService.getTokenInfo();
      console.log('🔐 Authentication status:', { isAuth, tokenInfo });

      if (!isAuth) {
        console.log('❌ User not authenticated with valid tokens');
        setAuthError(true);
        setLoading(false);
        return;
      }

      // Use the enhanced getUserOrders with pagination and filtering support
      const ordersResult = await apiService.getUserOrders(params);

      const backendOrders = ordersResult.orders || [];
      console.log('Received orders from backend:', backendOrders);

      // Status mapping from backend to frontend
      const statusMap: Record<string, 'Pending' | 'Preparing' | 'On the Way' | 'Delivered'> = {
        'pending': 'Pending',
        'confirmed': 'Pending',
        'preparing': 'Preparing',
        'ready': 'Preparing',
        'out_for_delivery': 'On the Way',
        'delivered': 'Delivered',
        'cancelled': 'Delivered' // Show cancelled as delivered for now
      };

      // Convert backend orders to local store format
      const formattedOrders = backendOrders.map((order: any) => ({
        id: order.orderId,
        createdAt: order.createdAt,
        items: order.items.map((item: any) => ({
          id: item.productId,
          product: {
            id: item.productId,
            name: item.productName,
            price: item.price,
            image: item.productImage || '',
            category: item.category || ''
          },
          qty: item.quantity,
          finalPrice: item.subtotal || (item.price * item.quantity),
          supplierId: order.supplierId,
          supplierName: order.supplierName,
          supplierCategory: order.supplierCategory || 'Restaurant'
        })),
        supplierId: order.supplierId,
        total: order.totalAmount,
        subTotal: order.subtotal,
        deliveryFee: order.deliveryFee || 5,
        status: statusMap[order.status] || 'Pending', // Map backend status to frontend status
        supplierRecievedMoney: order.paymentStatus === 'paid',
        address: {
          address: order.deliveryAddress?.street || '',
          lat: order.deliveryAddress?.coordinates?.lat || 32.2211,
          lng: order.deliveryAddress?.coordinates?.lng || 35.2544
        },
        driverLocation: {
          address: 'Driver location not available',
          lat: 32.2211,
          lng: 35.2544
        },
        phone: order.customerPhone || '',
        note: order.notes || '',
        paymentMethod: order.paymentMethod as 'cash' | 'card',
        promo: order.promoCode || '',
        promoDiscount: order.promoDiscount || 0,
        estimatedTime: order.estimatedDeliveryTime || '30-45 mins',
        driverName: order.driverName || 'Driver',
        driverPhone: order.driverPhone || ''
      }));

      // Update the store with formatted orders
      setOrders(formattedOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('not authenticated')) {
        setAuthError(true);
      } else {
        showErrorAlert(error, t);
      }
    } finally {
      setLoading(false);
    }
  };

  // Show authentication required message
  if (authError || !user) {
    return (
      <AuthCheck>
        <YStack flex={1} alignItems="center" justifyContent="center" padding="$6" gap="$4">
          <AuthDebugInfo />
        </YStack>
      </AuthCheck>
    );
  }

  const filters = [
    { key: 'All', label: t('orders.filters.all', { defaultValue: 'All' }) },
    { key: 'Delivered', label: t('orders.filters.delivered', { defaultValue: 'Delivered' }) },
    { key: 'On the Way', label: t('orders.filters.onTheWay', { defaultValue: 'On the Way' }) },
    { key: 'Preparing', label: t('orders.filters.preparing', { defaultValue: 'Preparing' }) }
  ];

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      {/* Enhanced Professional Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          paddingTop: insets.top + getResponsiveValue(30, 35, 40), // Increased safe area + additional padding
          paddingBottom: getResponsiveValue(30, 40, 50),
          borderBottomLeftRadius: getResponsiveValue(25, 30, 35),
          borderBottomRightRadius: getResponsiveValue(25, 30, 35),
        }}
      >
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 800 }}
          style={{
            paddingHorizontal: getResponsiveValue(20, 30, 40),
          }}
        >
          {/* Header Title */}
          <XStack justifyContent="space-between" alignItems="center" marginBottom="$5" marginTop="$2">
            <YStack flex={1}>
              <H3 color="white" fontWeight="800" fontSize={getResponsiveValue(28, 32, 36)} marginBottom="$2">
                <Text>📋</Text>{t('orders.myOrders', { defaultValue: 'My Orders' })}
              </H3>
              <Text color="rgba(255,255,255,0.8)" fontSize="$3">
                {t('orders.trackYourOrders', { defaultValue: 'Track and manage your orders' })}
              </Text>
            </YStack>
            {showDebug && (
              <Button
                size="$2"
                variant="outlined"
                backgroundColor="rgba(255,255,255,0.15)"
                borderColor="rgba(255,255,255,0.3)"
                onPress={() => setShowDebug(!showDebug)}
              >
                <Text fontSize="$2" color="white">Debug</Text>
              </Button>
            )}
          </XStack>

          {/* Order Statistics Cards */}
          <XStack gap="$3" flexWrap="wrap" justifyContent="space-between">
            <MotiView
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'timing', duration: 600, delay: 200 }}
              style={{ flex: 1, minWidth: getResponsiveValue(140, 160, 180) }}
            >
              <Card
                backgroundColor="rgba(255,255,255,0.15)"
                borderColor="rgba(255,255,255,0.2)"
                borderRadius="$4"
                padding="$3"
                backdropFilter="blur(10px)"
              >
                <YStack alignItems="center" gap="$1">
                  <XStack alignItems="center" gap="$2">
                    <Ionicons name="receipt-outline" size={20} color="white" />
                    <Text color="white" fontSize="$2" fontWeight="600">
                      {t('orders.stats.total', { defaultValue: 'Total Orders' })}
                    </Text>
                  </XStack>
                  <Text color="white" fontSize="$6" fontWeight="800">
                    {orderStats.total}
                  </Text>
                </YStack>
              </Card>
            </MotiView>

            <MotiView
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'timing', duration: 600, delay: 300 }}
              style={{ flex: 1, minWidth: getResponsiveValue(140, 160, 180) }}
            >
              <Card
                backgroundColor="rgba(255,255,255,0.15)"
                borderColor="rgba(255,255,255,0.2)"
                borderRadius="$4"
                padding="$3"
                backdropFilter="blur(10px)"
              >
                <YStack alignItems="center" gap="$1">
                  <XStack alignItems="center" gap="$2">
                    <Ionicons name="cash-outline" size={20} color="white" />
                    <Text color="white" fontSize="$2" fontWeight="600">
                      {t('orders.stats.totalSpent', { defaultValue: 'Total Spent' })}
                    </Text>
                  </XStack>
                  <Text color="white" fontSize="$5" fontWeight="800">
                    ₪{orderStats.totalSpent.toFixed(0)}
                  </Text>
                </YStack>
              </Card>
            </MotiView>
          </XStack>

          {/* Active Orders Progress */}
          {(orderStats.pending + orderStats.preparing + orderStats.onTheWay) > 0 && (
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 600, delay: 400 }}
            >
              <Card
                backgroundColor="rgba(255,255,255,0.1)"
                borderColor="rgba(255,255,255,0.2)"
                borderRadius="$4"
                padding="$3"
                marginTop="$3"
              >
                <Text color="white" fontSize="$3" fontWeight="600" marginBottom="$2">
                  {t('orders.activeOrders', { defaultValue: 'Active Orders' })}
                </Text>
                <XStack gap="$3" alignItems="center">
                  {orderStats.pending > 0 && (
                    <XStack alignItems="center" gap="$1">
                      <View width={8} height={8} borderRadius={4} backgroundColor="#FFA500" />
                      <Text color="white" fontSize="$2">{orderStats.pending} Pending</Text>
                    </XStack>
                  )}
                  {orderStats.preparing > 0 && (
                    <XStack alignItems="center" gap="$1">
                      <View width={8} height={8} borderRadius={4} backgroundColor="#FF6B6B" />
                      <Text color="white" fontSize="$2">{orderStats.preparing} Preparing</Text>
                    </XStack>
                  )}
                  {orderStats.onTheWay > 0 && (
                    <XStack alignItems="center" gap="$1">
                      <View width={8} height={8} borderRadius={4} backgroundColor="#4ECDC4" />
                      <Text color="white" fontSize="$2">{orderStats.onTheWay} On the Way</Text>
                    </XStack>
                  )}
                </XStack>
              </Card>
            </MotiView>
          )}
        </MotiView>
      </LinearGradient>
      {/* Enhanced Main Content */}
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: getResponsiveValue(100, 120, 140),
          paddingTop: getResponsiveValue(20, 25, 30)
        }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#667eea', '#764ba2']}
            tintColor="#667eea"
          />
        }
      >
        <YStack
          padding={getResponsiveValue(16, 20, 24)}
          gap="$4"
          width={getResponsiveValue('100%', '95%', '90%')}
          alignSelf='center'
          maxWidth={isDesktop ? 1200 : undefined}
        >
          {/* Debug Info */}
          {showDebug && <AuthDebugInfo />}

          {/* Enhanced Search Bar */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 400 }}
          >
            <Card
              backgroundColor="$backgroundStrong"
              borderRadius="$6"
              padding="$4"
              shadowColor="#000"
              shadowOffset={{ width: 0, height: 2 }}
              shadowOpacity={0.1}
              shadowRadius={8}
              elevation={3}
              marginBottom="$4"
            >
              <Text fontSize="$4" fontWeight="700" color="$gray12" marginBottom="$3">
                {t('orders.searchOrders', { defaultValue: 'Search Orders' })}
              </Text>
              <XStack alignItems="center" gap="$3">
                <View flex={1}>
                  <Input
                    placeholder={t('orders.searchPlaceholder', { defaultValue: 'Search orders, restaurants, items...' })}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    size="$4"
                    borderRadius="$6"
                    backgroundColor="$color3"
                    borderColor="$color6"
                    borderWidth={1}
                    paddingHorizontal="$4"
                  />
                </View>
                {searchQuery.trim() && (
                  <Button
                    size="$3"
                    borderRadius="$6"
                    backgroundColor="$color6"
                    onPress={() => setSearchQuery('')}
                    pressStyle={{ scale: 0.95 }}
                  >
                    <Ionicons name="close" size={16} color="#667eea" />
                  </Button>
                )}
              </XStack>
            </Card>
          </MotiView>

          {/* Enhanced Filter Bar */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500 }}
          >
            <Card
              backgroundColor="$backgroundStrong"
              borderRadius="$6"
              padding="$3"
              shadowColor="#000"
              shadowOffset={{ width: 0, height: 2 }}
              shadowOpacity={0.1}
              shadowRadius={8}
              elevation={3}
            >
              <Text fontSize="$4" fontWeight="700" color="$gray12" marginBottom="$3">
                {t('orders.filterByStatus', { defaultValue: 'Filter by Status' })}
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <XStack gap="$2" paddingHorizontal="$2">
                  {filters.map((filter, index) => {
                    const isActive = selectedFilter === filter.key;
                    const count = filter.key === 'All' ? orderStats.total :
                                 filter.key === 'Delivered' ? orderStats.delivered :
                                 filter.key === 'On the Way' ? orderStats.onTheWay :
                                 filter.key === 'Preparing' ? orderStats.preparing :
                                 orderStats.pending;

                    return (
                      <MotiView
                        key={filter.key}
                        from={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ type: 'timing', duration: 300, delay: index * 50 }}
                      >
                        <Button
                          size="$3"
                          borderRadius="$8"
                          paddingHorizontal="$4"
                          paddingVertical="$2"
                          backgroundColor={isActive ? '#667eea' : '$color3'}
                          borderColor={isActive ? '#667eea' : '$color6'}
                          borderWidth={1}
                          onPress={() => setSelectedFilter(filter.key)}
                          pressStyle={{ scale: 0.95 }}
                        >
                          <XStack alignItems="center" gap="$2">
                            <Text
                              color={isActive ? 'white' : '$gray11'}
                              fontWeight={isActive ? "700" : "500"}
                              fontSize="$3"
                            >
                              {filter.label}
                            </Text>
                            {count > 0 && (
                              <View
                                backgroundColor={isActive ? 'rgba(255,255,255,0.3)' : '$color8'}
                                borderRadius="$10"
                                paddingHorizontal="$2"
                                paddingVertical="$0.5"
                                minWidth={20}
                                alignItems="center"
                              >
                                <Text
                                  color={isActive ? 'white' : '$gray11'}
                                  fontSize="$1"
                                  fontWeight="600"
                                >
                                  {count}
                                </Text>
                              </View>
                            )}
                          </XStack>
                        </Button>
                      </MotiView>
                    );
                  })}
                </XStack>
              </ScrollView>
            </Card>
          </MotiView>

          {/* Enhanced Empty State */}
          {filteredOrders.length === 0 && !loading && (
            <MotiView
              from={{ opacity: 0, translateY: 30 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 600 }}
            >
              <Card
                backgroundColor="$backgroundStrong"
                borderRadius="$6"
                padding={getResponsiveValue(32, 40, 48)}
                alignItems="center"
                shadowColor="#000"
                shadowOffset={{ width: 0, height: 4 }}
                shadowOpacity={0.1}
                shadowRadius={12}
                elevation={4}
                marginTop="$6"
              >
                <MotiView
                  from={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', damping: 15, stiffness: 200, delay: 200 }}
                >
                  <View
                    backgroundColor="$color4"
                    borderRadius="$12"
                    padding="$4"
                    marginBottom="$4"
                  >
                    <Ionicons
                      name={selectedFilter === 'All' ? "receipt-outline" : "search-outline"}
                      size={getResponsiveValue(48, 56, 64)}
                      color="#667eea"
                    />
                  </View>
                </MotiView>

                <H4 color="$gray12" textAlign="center" marginBottom="$2">
                  {selectedFilter === 'All'
                    ? t('orders.noOrdersYet', { defaultValue: 'No orders yet' })
                    : t('orders.noOrdersInFilter', { defaultValue: `No ${selectedFilter.toLowerCase()} orders` })
                  }
                </H4>

                <Text
                  color="$gray10"
                  textAlign="center"
                  fontSize="$3"
                  lineHeight="$4"
                  maxWidth={getResponsiveValue(280, 320, 360)}
                >
                  {selectedFilter === 'All'
                    ? t('orders.startOrderingMessage', { defaultValue: 'Start exploring restaurants and place your first order!' })
                    : t('orders.tryDifferentFilter', { defaultValue: 'Try selecting a different filter to see your orders.' })
                  }
                </Text>

                {selectedFilter === 'All' && (
                  <Button
                    marginTop="$4"
                    size="$4"
                    backgroundColor="#667eea"
                    borderRadius="$6"
                    onPress={() => router.push('/home')}
                    pressStyle={{ scale: 0.95 }}
                  >
                    <XStack alignItems="center" gap="$2">
                      <Ionicons name="restaurant-outline" size={20} color="white" />
                      <Text color="white" fontWeight="600">
                        {t('orders.exploreRestaurants', { defaultValue: 'Explore Restaurants' })}
                      </Text>
                    </XStack>
                  </Button>
                )}
              </Card>
            </MotiView>
          )}

          {/* Loading State */}
          {loading && (
            <MotiView
              from={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ type: 'timing', duration: 300 }}
            >
              <YStack alignItems="center" gap="$4" marginTop="$8">
                <MotiView
                  from={{ rotate: '0deg' }}
                  animate={{ rotate: '360deg' }}
                  transition={{ type: 'timing', duration: 1000, repeat: Infinity }}
                >
                  <Ionicons name="refresh-outline" size={32} color="#667eea" />
                </MotiView>
                <Text color="$gray10" fontSize="$4">
                  {t('orders.loadingOrders', { defaultValue: 'Loading your orders...' })}
                </Text>
              </YStack>
            </MotiView>
          )}

          {/* Enhanced Order Cards */}
          <YStack gap="$3">
            {filteredOrders.map((order, index) => {
              const statusConfig = {
                'Pending': { color: '#FFA500', icon: 'time-outline', bgColor: 'rgba(255, 165, 0, 0.1)' },
                'Preparing': { color: '#FF6B6B', icon: 'restaurant-outline', bgColor: 'rgba(255, 107, 107, 0.1)' },
                'On the Way': { color: '#4ECDC4', icon: 'car-outline', bgColor: 'rgba(78, 205, 196, 0.1)' },
                'Delivered': { color: '#45B7D1', icon: 'checkmark-circle-outline', bgColor: 'rgba(69, 183, 209, 0.1)' }
              };

              const config = statusConfig[order.status as keyof typeof statusConfig] || statusConfig['Pending'];
              const isActive = order.status !== 'Delivered';

              return (
                <MotiView
                  key={order.id}
                  from={{ opacity: 0, translateX: -20 }}
                  animate={{ opacity: 1, translateX: 0 }}
                  transition={{ type: 'timing', duration: 400, delay: index * 100 }}
                >
                  <Card
                    backgroundColor="$backgroundStrong"
                    borderRadius="$6"
                    padding={getResponsiveValue(16, 20, 24)}
                    shadowColor="#000"
                    shadowOffset={{ width: 0, height: 6 }}
                    shadowOpacity={0.12}
                    shadowRadius={16}
                    elevation={6}
                    borderWidth={isActive ? 2 : 1}
                    borderColor={isActive ? config.color : '$color6'}
                    pressStyle={{ scale: 0.98 }}
                    onPress={() => router.push({
                      pathname: "/orders/order-details",
                      params: { id: order.id }
                    })}
                  >
                    {/* Order Header */}
                    <XStack justifyContent="space-between" alignItems="flex-start" marginBottom="$3">
                      <YStack flex={1} marginRight="$3">
                        <XStack alignItems="center" gap="$2" marginBottom="$1">
                          <Text fontSize="$5" fontWeight="800" color="$gray12">
                            #{order.id}
                          </Text>
                        </XStack>
                        <Text color="$gray9" fontSize="$2">
                          {new Date(order.createdAt).toLocaleDateString('en-US', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </Text>
                      </YStack>

                      {/* Status Badge */}
                      <View
                        backgroundColor={isActive ? config.color : config.bgColor}
                        borderRadius="$6"
                        paddingHorizontal="$3"
                        paddingVertical="$2"
                        borderWidth={isActive ? 0 : 1}
                        borderColor={config.color}
                        shadowColor={isActive ? config.color : 'transparent'}
                        shadowOffset={{ width: 0, height: 2 }}
                        shadowOpacity={isActive ? 0.3 : 0}
                        shadowRadius={4}
                        elevation={isActive ? 3 : 0}
                        minWidth={80}
                        alignItems="center"
                      >
                        <XStack alignItems="center" gap="$1">
                          <Ionicons
                            name={config.icon as any}
                            size={16}
                            color={isActive ? 'white' : config.color}
                          />
                          <Text
                            color={isActive ? 'white' : config.color}
                            fontSize="$2"
                            fontWeight="700"
                          >
                            {t(`orders.statuses.${order.status.toLowerCase().replace(/\s+/g, '_')}`, {
                              defaultValue: order.status
                            })}
                          </Text>
                        </XStack>
                      </View>
                    </XStack>

                    <Separator backgroundColor="$color6" marginBottom="$3" />

                    {/* Order Details */}
                    <YStack gap="$2" marginBottom="$3">
                      {/* Items */}
                      <XStack alignItems="flex-start" gap="$3">
                        <View
                          backgroundColor="$color4"
                          borderRadius="$3"
                          padding="$2"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Ionicons name="bag-outline" size={16} color="#667eea" />
                        </View>
                        <YStack flex={1}>
                          <Text color="$gray11" fontSize="$2" fontWeight="600" marginBottom="$1">
                            {t('orders.items', { defaultValue: 'Items' })} ({order.items.length})
                          </Text>
                          <Text color="$gray10" fontSize="$3" numberOfLines={2}>
                            {order.items.slice(0, 3).map(i => i.product.name).join(', ')}
                            {order.items.length > 3 && ` +${order.items.length - 3} more`}
                          </Text>
                        </YStack>
                      </XStack>

                      {/* Total Amount */}
                      <XStack alignItems="center" gap="$3">
                        <View
                          backgroundColor="$color4"
                          borderRadius="$3"
                          padding="$2"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Ionicons name="cash-outline" size={16} color="#667eea" />
                        </View>
                        <YStack flex={1}>
                          <Text color="$gray11" fontSize="$2" fontWeight="600">
                            {t('orders.totalAmount', { defaultValue: 'Total Amount' })}
                          </Text>
                          <Text color="$gray12" fontSize="$4" fontWeight="700">
                            ₪{order.total.toFixed(2)}
                          </Text>
                        </YStack>
                      </XStack>
                    </YStack>

                    {/* Action Buttons */}
                    <XStack gap="$2" justifyContent="space-between">
                      <Button
                        flex={1}
                        size="$3"
                        backgroundColor="$color4"
                        borderColor="$color6"
                        borderWidth={1}
                        borderRadius="$4"
                        onPress={() => router.push({
                          pathname: "/orders/order-details",
                          params: { id: order.id }
                        })}
                        pressStyle={{ scale: 0.95 }}
                      >
                        <XStack alignItems="center" gap="$2">
                          <Ionicons name="eye-outline" size={16} color="#667eea" />
                          <Text color="#667eea" fontWeight="600" fontSize="$3">
                            {t('orders.viewDetails', { defaultValue: 'View Details' })}
                          </Text>
                        </XStack>
                      </Button>

                      {isActive && (
                        <Button
                          flex={1}
                          size="$3"
                          backgroundColor="#667eea"
                          borderRadius="$4"
                          onPress={() => router.push({
                            pathname: "/orders/order-tracking",
                            params: { id: order.id }
                          })}
                          pressStyle={{ scale: 0.95 }}
                        >
                          <XStack alignItems="center" gap="$2">
                            <Ionicons name="location-outline" size={16} color="white" />
                            <Text color="white" fontWeight="600" fontSize="$3">
                              {t('orders.trackOrder', { defaultValue: 'Track Order' })}
                            </Text>
                          </XStack>
                        </Button>
                      )}
                    </XStack>
                  </Card>
                </MotiView>
              );
            })}
          </YStack>
        </YStack>
      </ScrollView>
    </>
  );
}
