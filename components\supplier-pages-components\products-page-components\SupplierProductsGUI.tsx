import { Ionicons } from "@expo/vector-icons";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>rollView,
  XStack,
  Y<PERSON>tack,
  Text,
  View,
  Input,
  Sheet,
  Separator,
  H2,
  H3,
  H4,
  Spin<PERSON>
} from "tamagui";
import { Image, Dimensions, Alert } from "react-native";
import { useCurrentUserData } from "~/components/useCurrentUserData";
import { getSupplierById } from "~/services/apiService";
import { MotiView, AnimatePresence } from "moti";
import { LinearGradient } from "expo-linear-gradient";
import { useSupplierProducts } from "./useSupplierProducts";
import { useEffect, useState, useCallback } from "react";
import { router, useRouter } from "expo-router";
import { useSupplierCategories } from './useSupplierCategories';
import { useTranslation } from "react-i18next";

// Helper function to count options for a product
const getProductOptionsCount = (product: any) => {
  let count = 0;

  // Restaurant options
  if (product.restaurantOptions) {
    count += (product.restaurantOptions.additions?.length || 0);
    count += (product.restaurantOptions.without?.length || 0);
    count += (product.restaurantOptions.sides?.length || 0);
  }

  // Clothing options
  if (product.clothingOptions) {
    count += (product.clothingOptions.sizes?.length || 0);
    count += (product.clothingOptions.colors?.length || 0);
    count += (product.clothingOptions.gallery?.length || 0);
  }

  // Custom options
  if (product.customOptions) {
    count += product.customOptions.length;
  }

  return count;
};

// Helper function to get product options summary
const getProductOptionsSummary = (product: any) => {
  const summary: string[] = [];

  // Restaurant options
  if (product.restaurantOptions) {
    if (product.restaurantOptions.additions?.length) {
      summary.push(`${product.restaurantOptions.additions.length} additions`);
    }
    if (product.restaurantOptions.without?.length) {
      summary.push(`${product.restaurantOptions.without.length} without options`);
    }
    if (product.restaurantOptions.sides?.length) {
      summary.push(`${product.restaurantOptions.sides.length} sides`);
    }
  }

  // Clothing options
  if (product.clothingOptions) {
    if (product.clothingOptions.sizes?.length) {
      summary.push(`${product.clothingOptions.sizes.length} sizes`);
    }
    if (product.clothingOptions.colors?.length) {
      summary.push(`${product.clothingOptions.colors.length} colors`);
    }
    if (product.clothingOptions.gallery?.length) {
      summary.push(`${product.clothingOptions.gallery.length} images`);
    }
  }

  // Custom options
  if (product.customOptions?.length) {
    summary.push(`${product.customOptions.length} custom options`);
  }

  return summary;
};

export default function SupplierProductsGUI() {
  const { t } = useTranslation();
  const { user } = useCurrentUserData();
  const [supplier, setSupplier] = useState<any>(null);
  const [supplierLoading, setSupplierLoading] = useState(true);
  const windowWidth = Dimensions.get("window").width;

  const { products, setProducts, saveProducts, isSaving, lastSaved, setSupplierId, loadProducts, isLoading, error, deleteProduct, updateProduct } = useSupplierProducts();

  // Initialize supplier ID and load products
  useEffect(() => {
    if (user?.supplierId) {
      console.log('🏪 Initializing supplier products for:', user.supplierId);
      setSupplierId(user.supplierId);
      loadProducts();
    }
  }, [user?.supplierId, setSupplierId, loadProducts]);

  // Fetch supplier data from backend
  useEffect(() => {
    const fetchSupplier = async () => {
      if (!user?.supplierId) {
        setSupplierLoading(false);
        return;
      }

      try {
        setSupplierLoading(true);
        const supplierData = await getSupplierById(user.supplierId);
        setSupplier(supplierData);
      } catch (error) {
        console.error('Error fetching supplier:', error);
        setSupplier(null);
      } finally {
        setSupplierLoading(false);
      }
    };

    fetchSupplier();
  }, [user?.supplierId]);

  // Enhanced state management
  const [newCatModal, setNewCatModal] = useState(false);
  const [newCat, setNewCat] = useState('');
  const [editCat, setEditCat] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState('');
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showSummary, setShowSummary] = useState(false);

  useEffect(() => {
    if (supplier?.products) {
      setProducts(supplier.products);
    }
  }, [supplier]);

  const { categories, addCategory, deleteCategory, selectCategory, selectedCategory, renameCategory } = useSupplierCategories();

  // Save products function
  const handleSaveProducts = async () => {
    try {
      await saveProducts();

      // Show success message
      Alert.alert(
        'Success!',
        `Successfully saved ${products.length} products with all their options.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save products. Please try again.');
    }
  };

  // Get total options count across all products
  const getTotalOptionsCount = () => {
    return products.reduce((total, product) => total + getProductOptionsCount(product), 0);
  };

  const filteredCategories = products.reduce<string[]>((acc, product) =>
    acc.includes(product.category) ? acc : [...acc, product.category],
  []
  );

  useEffect(() => {
    // Add 'All' category if not present
    if (!categories.includes('All')) {
      addCategory('All');
    }
    // Add product categories if not present
    for (const category of filteredCategories) {
      if (!categories.includes(category)) {
        addCategory(category);
      }
    }
    // Select 'All' only if nothing is selected
    if (!selectedCategory) {
      selectCategory('All');
    }
    // eslint-disable-next-line
  }, [products, categories, addCategory, selectCategory]);

  const filteredProducts = (selectedCategory && selectedCategory !== 'All')
  ? products.filter(p => p.category === selectedCategory)
  : products;

  return (
    <>
      <Sheet modal open={newCatModal} onOpenChange={setNewCatModal} snapPoints={[50]} dismissOnSnapToBottom>
        <Sheet.Overlay bg="rgba(0,0,0,0.5)" />
        <Sheet.Frame p="$0" bg="white" br="$6">
          <YStack>
            <LinearGradient
              colors={["#7529B3", "#8F3DD2"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{
                borderTopLeftRadius: 24,
                borderTopRightRadius: 24,
                padding: 20,
              }}
            >
              <XStack ai="center" jc="space-between">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="add-circle-outline" size={24} color="white" />
                  </View>
                  <YStack>
                    <H3 color="white" fontWeight="800"><Text>Add New Category</Text></H3>
                    <Text color="white" opacity={0.8} fontSize="$3">
                      Create a new product category
                    </Text>
                  </YStack>
                </XStack>

                <Button
                  chromeless
                  icon={<Ionicons name="close" size={24} color="white" />}
                  onPress={() => setNewCatModal(false)}
                  circular
                  bg="rgba(255,255,255,0.2)"
                  hoverStyle={{ bg: "rgba(255,255,255,0.3)" }}
                  pressStyle={{ bg: "rgba(255,255,255,0.4)" }}
                />
              </XStack>
            </LinearGradient>

            <YStack p="$5" gap="$4">
              <YStack gap="$2">
                <Text fontSize="$4" fontWeight="600" color="$gray12">
                  Category Name
                </Text>
                <Input
                  placeholder="e.g., Beverages, Main Dishes, Desserts"
                  value={newCat}
                  onChangeText={setNewCat}
                  size="$4"
                  borderColor="$gray6"
                  focusStyle={{ borderColor: "$primary" }}
                  bg="$gray1"
                />
                <Text fontSize="$2" color="$gray9">
                  Choose a descriptive name for your product category
                </Text>
              </YStack>

              <XStack gap="$3" mt="$2">
                <Button
                  flex={1}
                  variant="outlined"
                  borderColor="$gray8"
                  color="$gray10"
                  size="$4"
                  br="$6"
                  onPress={() => {
                    setNewCat('');
                    setNewCatModal(false);
                  }}
                  hoverStyle={{ bg: "$gray2" }}
                  pressStyle={{ bg: "$gray3" }}
                >
                  <Text>Cancel</Text>
                </Button>
                <Button
                  flex={1}
                  bg="$primary"
                  color="white"
                  size="$4"
                  br="$6"
                  disabled={!newCat || !newCat.trim()}
                  opacity={!newCat || !newCat.trim() ? 0.5 : 1}
                  icon={<Ionicons name="checkmark-circle-outline" size={18} color="white" />}
                  onPress={() => {
                    const safe = (newCat ?? '').trim();
                    if (safe) {
                      addCategory(safe);
                      setNewCat('');
                      setNewCatModal(false);
                    }
                  }}
                  hoverStyle={{ bg: "$third" }}
                  pressStyle={{ bg: "$third", scale: 0.95 }}
                >
                  <Text>Add Category</Text>
                </Button>
              </XStack>
            </YStack>
          </YStack>
        </Sheet.Frame>
      </Sheet>

      <Sheet open={!!editCat} onOpenChange={() => setEditCat(null)}>
        <Sheet.Frame padding="$4" gap="$3">
          <Text fontSize="$6" fontWeight="600">{t('supplier.editCategory', { defaultValue: 'Edit Category' })}</Text>
          <Input value={editedValue} onChangeText={setEditedValue} />
          <Button mt="$3" size="$3" br="$6" borderColor={"$secondary"} color={"$secondary"} variant="outlined" 
            onPress={() => {
              if (editCat && editedValue.trim()) renameCategory(editCat, editedValue);
              setEditCat(null);
              setEditedValue('');
            }}
          >
            {t('common.save', { defaultValue: 'Save' })}
          </Button>
          <Button mt="$3" size="$3" br="$6" borderColor={"red"} color={"red"} variant="outlined"
            onPress={() => setNewCatModal(false)}
          >
            {t('common.cancel', { defaultValue: 'Cancel' })}
          </Button>
        </Sheet.Frame>
      </Sheet>
    <ScrollView
      padding={16}
      width={windowWidth}
      contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}
    >
      <LinearGradient
        colors={["#7529B3", "#8F3DD2"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{
          borderRadius: 24,
          padding: 24,
          marginBottom: 24,
        }}
      >
        <MotiView
          from={{ opacity: 0, translateY: -20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: "timing", duration: 600 }}
        >
          <YStack gap="$4">
            <YStack gap="$3">
              <XStack alignItems="center" gap="$3" flex={1}>
                <Image
                  source={{ uri: supplier?.logoUrl }}
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 12,
                    borderWidth: 2,
                    borderColor: "#fff",
                    backgroundColor: "#fff",
                    shadowColor: "#000",
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.3,
                    shadowRadius: 8,
                  }}
                />
                <YStack flex={1}>
                  <H2 color="#fff" fontWeight="800" numberOfLines={1}>
                    {supplier?.name || "My Store"}
                  </H2>
                  <Text color="#eee" fontSize={14} opacity={0.9} numberOfLines={1}>
                    Product Management Dashboard
                  </Text>
                  {lastSaved && (
                    <Text color="#eee" fontSize={11} opacity={0.7} mt="$1">
                      Last saved: {lastSaved.toLocaleTimeString()}
                    </Text>
                  )}
                </YStack>
              </XStack>

              <Button
                bg="rgba(255,255,255,0.2)"
                borderColor="rgba(255,255,255,0.3)"
                borderWidth={1}
                color="white"
                size="$3"
                br="$6"
                icon={isSaving ? <Spinner size="small" color="white" /> : <Ionicons name="cloud-upload-outline" size={18} color="white" />}
                onPress={handleSaveProducts}
                disabled={isSaving}
                hoverStyle={{ bg: "rgba(255,255,255,0.3)" }}
                pressStyle={{ bg: "rgba(255,255,255,0.4)" }}
                width="100%"
              >
                {isSaving ? 'Saving...' : 'Save All Products'}
              </Button>
            </YStack>

            <XStack gap="$3" jc="space-between">
              <MotiView
                from={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 200 }}
                style={{ flex: 1 }}
              >
                <Card bg="rgba(255,255,255,0.15)" p="$3" br="$6" borderWidth={0}>
                  <YStack ai="center" gap="$1">
                    <XStack ai="center" gap="$2">
                      <Ionicons name="cube-outline" size={24} color="white" />
                      <Text color="white" fontSize="$6" fontWeight="bold">
                        {products.length}
                      </Text>
                    </XStack>
                    
                    <Text color="white" fontSize="$3" opacity={0.8}>
                      Products
                    </Text>
                  </YStack>
                </Card>
              </MotiView>

              <MotiView
                from={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 300 }}
                style={{ flex: 1 }}
              >
                <Card bg="rgba(255,255,255,0.15)" p="$3" br="$6" borderWidth={0}>
                  <YStack ai="center" gap="$1">
                    <XStack ai="center" gap="$2">
                      <Ionicons name="options-outline" size={24} color="white" />
                      <Text color="white" fontSize="$6" fontWeight="bold">
                        {getTotalOptionsCount()}
                      </Text>
                    </XStack>
                    <Text color="white" fontSize="$3" opacity={0.8}>
                      Total Options
                    </Text>
                  </YStack>
                </Card>
              </MotiView>

              <MotiView
                from={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 400 }}
                style={{ flex: 1 }}
              >
                <Card bg="rgba(255,255,255,0.15)" p="$3" br="$6" borderWidth={0}>
                  <YStack ai="center" gap="$1">
                    <XStack ai="center" gap="$2">
                      <Ionicons name="pricetags-outline" size={24} color="white" />
                      <Text color="white" fontSize="$6" fontWeight="bold">
                        {categories.length - 1}
                      </Text>
                    </XStack>
                    <Text color="white" fontSize="$3" opacity={0.8}>
                      Categories
                    </Text>
                  </YStack>
                </Card>
              </MotiView>
            </XStack>

            <XStack gap="$3" jc="space-between">
              <Button
                flex={1}
                bg={showSummary ? "rgba(255,255,255,0.3)" : "rgba(255,255,255,0.15)"}
                borderColor="rgba(255,255,255,0.4)"
                borderWidth={1}
                color="white"
                size="$3"
                br="$6"
                icon={<Ionicons name="analytics-outline" size={16} color="white" />}
                onPress={() => setShowSummary(!showSummary)}
                hoverStyle={{ bg: "rgba(255,255,255,0.4)" }}
                pressStyle={{ bg: "rgba(255,255,255,0.5)", scale: 0.95 }}
              >
                {showSummary ? 'Hide Summary' : 'Show Summary'}
              </Button>

              <Button
                flex={1}
                bg={viewMode === 'grid' ? "rgba(255,255,255,0.3)" : "rgba(255,255,255,0.15)"}
                borderColor="rgba(255,255,255,0.4)"
                borderWidth={1}
                color="white"
                size="$3"
                br="$6"
                icon={<Ionicons name={viewMode === 'list' ? 'grid-outline' : 'list-outline'} size={16} color="white" />}
                onPress={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                hoverStyle={{ bg: "rgba(255,255,255,0.4)" }}
                pressStyle={{ bg: "rgba(255,255,255,0.5)", scale: 0.95 }}
              >
                {viewMode === 'list' ? 'Grid View' : 'List View'}
              </Button>
            </XStack>
          </YStack>
        </MotiView>
      </LinearGradient>

      {showSummary && (
        <MotiView
          from={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ type: 'timing', duration: 300 }}
        >
          <Card elevate p="$4" mb="$4" br="$8" borderWidth={1} borderColor="$primary">
            <H3 color="$primary" mb="$3" fontWeight="700"><Text>📊 Products Options Summary</Text></H3>

            {products.length === 0 ? (
              <Text color="$gray10" textAlign="center" py="$4">
                No products to summarize yet.
              </Text>
            ) : (
              <YStack gap="$3">
                {products.map((product, index) => {
                  const optionsCount = getProductOptionsCount(product);
                  const optionsSummary = getProductOptionsSummary(product);

                  return (
                    <MotiView
                      key={product.id}
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: index * 100 }}
                    >
                      <Card p="$3" bg="$gray1" br="$6" borderWidth={1} borderColor="$gray4">
                        <XStack jc="space-between" ai="flex-start">
                          <YStack flex={1}>
                            <XStack ai="center" gap="$2" mb="$1">
                              <Text fontWeight="600" fontSize="$4" color="$gray12">
                                {product.name}
                              </Text>
                              <Text color="$gray9" fontSize="$3">
                                ({product.category})
                              </Text>
                            </XStack>

                            {optionsCount > 0 ? (
                              <YStack gap="$1">
                                <Text color="$secondary" fontSize="$3" fontWeight="500">
                                  {optionsCount} total options configured
                                </Text>
                                <Text color="$gray10" fontSize="$2">
                                  {optionsSummary.join(' • ')}
                                </Text>
                              </YStack>
                            ) : (
                              <Text color="$orange10" fontSize="$3" fontStyle="italic">
                                ⚠️ No options configured yet
                              </Text>
                            )}
                          </YStack>

                          <Button
                            size="$3"
                            chromeless
                            icon={<Ionicons name="settings-outline" size={16} color="#7529B3" />}
                            onPress={() => {
                              router.push({
                                pathname: "/(supplier-pages)/products/manage-additions",
                                params: { id: product.id },
                              });
                            }}
                          >
                            <Text>Manage</Text>
                          </Button>
                        </XStack>
                      </Card>
                    </MotiView>
                  );
                })}

                <Separator my="$3" />
                <Card p="$4" bg="$primary" br="$6">
                  <YStack gap="$3">
                    <Text color="white" fontSize="$4" fontWeight="bold" textAlign="center">
                      📊 Summary Statistics
                    </Text>

                    <YStack gap="$3">
                      <XStack jc="space-between" ai="center" px="$2">
                        <Text color="white" fontSize="$3" opacity={0.9} flex={1}>
                          With Options:
                        </Text>
                        <Text color="white" fontSize="$4" fontWeight="bold">
                          {products.filter(p => getProductOptionsCount(p) > 0).length}
                        </Text>
                      </XStack>

                      <XStack jc="space-between" ai="center" px="$2">
                        <Text color="white" fontSize="$3" opacity={0.9} flex={1}>
                          Need Setup:
                        </Text>
                        <Text color="white" fontSize="$4" fontWeight="bold">
                          {products.filter(p => getProductOptionsCount(p) === 0).length}
                        </Text>
                      </XStack>

                      <XStack jc="space-between" ai="center" px="$2">
                        <Text color="white" fontSize="$3" opacity={0.9} flex={1}>
                          Completion:
                        </Text>
                        <Text color="white" fontSize="$4" fontWeight="bold">
                          {Math.round((products.filter(p => getProductOptionsCount(p) > 0).length / products.length) * 100) || 0}%
                        </Text>
                      </XStack>
                    </YStack>
                  </YStack>
                </Card>
              </YStack>
            )}
          </Card>
        </MotiView>
      )}

      <XStack ai="center" jc="space-between" mb="$4" gap="$2" height="$8">
          <ScrollView horizontal>
          {categories.map((cat, i) => (
            <YStack
              key={i}
              bg={selectedCategory === cat ? '$secondary' : '$gray5'}
              px="$3"
              py="$2"
              br="$4"
              ai="center"
              jc="center"
              mr="$2"
              height={"90%"}
            >
              <Text
                color={selectedCategory === cat ? 'white' : 'black'}
                fontWeight="600"
                onPress={() => selectCategory(cat)}
              >
                {cat}
              </Text>
              {cat === "All" ? (null) : (
              <XStack gap="$1" mt="$1">
                <Button
                  color={selectedCategory === cat ? 'white' : 'black'}
                  icon={<Ionicons name="create-outline" size={14} />}
                  size="$2"
                  chromeless
                  onPress={() => {
                    setEditCat(cat);
                    setEditedValue(cat);
                  }}
                />
                <Button
                  icon={<Ionicons name="trash-outline" size={14} color="#f00" />}
                  size="$2"
                  chromeless
                  onPress={() => deleteCategory(cat)}
                />
              </XStack>
              )}
            </YStack>
          ))}
          </ScrollView>
          <Button
              bg="$gray5"
              icon={<Ionicons name="add-circle-outline" size={20} />}
              size="$4"
              br="$12"
              onPress={() => setNewCatModal(true)}
          >
          </Button>
      </XStack>

      {filteredProducts.length === 0 ? (
        <YStack alignItems="center" jc="center" py="$10">
          <Ionicons name="cube-outline" size={80} color="#ccc" />
          <Text fontSize="$5" mt="$2" color="$gray9">
            No products yet.
          </Text>
        </YStack>
      ) : viewMode === 'list' ? (
        // List View
        <YStack gap="$2">
          {filteredProducts.map((product, i) => (
            <MotiView
              key={product.id}
              from={{ opacity: 0, translateY: 10 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ delay: i * 50 }}
            >
              <ProductCard product={product} />
            </MotiView>
          ))}
        </YStack>
      ) : (
        // Grid View
        <YStack gap="$3">
          {Array.from({ length: Math.ceil(filteredProducts.length / 2) }, (_, rowIndex) => (
            <XStack key={rowIndex} gap="$3" jc="space-between">
              {filteredProducts.slice(rowIndex * 2, rowIndex * 2 + 2).map((product, colIndex) => (
                <MotiView
                  key={product.id}
                  from={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: (rowIndex * 2 + colIndex) * 50 }}
                  style={{ flex: 1, maxWidth: '48%' }}
                >
                  <ProductCardGrid product={product} />
                </MotiView>
              ))}
              {filteredProducts.slice(rowIndex * 2, rowIndex * 2 + 2).length === 1 && (
                <View style={{ flex: 1, maxWidth: '48%' }} />
              )}
            </XStack>
          ))}
        </YStack>
      )}
    </ScrollView>
    <Button
        icon={<Ionicons name="add" size={24} color="white" />}
        position="absolute"
        bottom={20}
        right={20}
        circular
        size="$6"
        bg="$secondary"
        hoverStyle={{ bg: "$secondary_strong" }}
        pressStyle={{ bg: "$secondary_strong" }}
        zIndex={10}
        elevate
        onPress={() => router.push("/(supplier-pages)/products/add-product")}
      >
      </Button>
      </>
  );
}

type Product = {
  id: string;
  name: string;
  category: string;
  price: number;
  discountPrice?: number;
  image: string;
  description?: string;
  isPromotion?: boolean;
  restaurantOptions?: {
    additions?: { id: string; name: string; price: number }[];
    without?: string[];
    sides?: { id: string; name: string; price: number }[];
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
  customOptions?: {
    id: string;
    title: string;
    type: 'text' | 'number' | 'select' | 'multi-select';
    values?: string[];
  }[];
};

function ProductCard({ product }: { product: Product }) {
  const hasDiscount = product.discountPrice && product.discountPrice > 0;
  const optionsCount = getProductOptionsCount(product);
  const optionsSummary = getProductOptionsSummary(product);
  const router = useRouter();
  const { deleteProduct, loadProducts } = useSupplierProducts();

  return (
    <Card
      elevate
      br="$10"
      mb="$4"
      p="$5"
      borderWidth={2}
      borderColor={optionsCount > 0 ? "$secondary" : "$gray4"}
      bg="white"
      hoverStyle={{
        borderColor: "$primary",
        shadowColor: "$primary",
        shadowOpacity: 0.2,
        shadowRadius: 12,
        scale: 1.02
      }}
      pressStyle={{ scale: 0.98 }}
    >
        <XStack gap="$4">
          <View style={{ position: 'relative' }}>
            <Image
              source={{ uri: product.image }}
              style={{
                width: 90,
                height: 90,
                borderRadius: 12,
                borderWidth: 2,
                borderColor: optionsCount > 0 ? '#67B329' : '#e5e5e5'
              }}
            />
            {optionsCount > 0 && (
              <View
                style={{
                  position: 'absolute',
                  top: -8,
                  right: -8,
                  backgroundColor: '#67B329',
                  borderRadius: 12,
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderWidth: 2,
                  borderColor: 'white'
                }}
              >
                <Text color="white" fontSize="$2" fontWeight="bold">
                  {optionsCount}
                </Text>
              </View>
            )}
          </View>

          <YStack flex={1} jc="space-between">
            <YStack gap="$2">
              <XStack ai="center" jc="space-between">
                <H4 fontWeight="700" color="$gray12" numberOfLines={1}>
                  {product.name}
                </H4>
                {optionsCount === 0 && (
                  <View
                    style={{
                      backgroundColor: '#ff6b35',
                      borderRadius: 8,
                      paddingHorizontal: 6,
                      paddingVertical: 2
                    }}
                  >
                    <Text color="white" fontSize="$1" fontWeight="600">
                      NEEDS SETUP
                    </Text>
                  </View>
                )}
              </XStack>

              <Text color="$gray10" fontSize="$3" fontWeight="500">
                📂 {product.category}
              </Text>

              <XStack gap="$2" ai="center">
                <Text color={hasDiscount ? "$red10" : "$secondary"} fontWeight="700" fontSize="$5">
                  {`${product.price} ₪`}
                </Text>
                {hasDiscount && (
                  <Text color="$gray9" textDecorationLine="line-through" fontSize="$3">
                    {`${product.discountPrice} ₪`}
                  </Text>
                )}
              </XStack>

              {optionsCount > 0 ? (
                <YStack gap="$1">
                  <Text color="$secondary" fontSize="$3" fontWeight="600">
                    ✅ {optionsCount} options configured
                  </Text>
                  <Text color="$gray9" fontSize="$2" numberOfLines={2}>
                    {optionsSummary.join(' • ')}
                  </Text>
                </YStack>
              ) : (
                <Text color="$orange10" fontSize="$3" fontStyle="italic">
                  ⚠️ No options configured
                </Text>
              )}
            </YStack>

            {product.isPromotion && (
              <XStack ai="center" mt="$3" mb="$2">
                <Ionicons name="star" size={16} color="#F59E0B" style={{ marginRight: 8 }} />
                <Text fontSize="$4" fontWeight="600" color="$orange10">
                  Featured in Promotions
                </Text>
              </XStack>
            )}

            <XStack jc="flex-end" gap="$2" mt="$3">
              <Button
                size="$3"
                variant="outlined"
                borderColor="$primary"
                color="$primary"
                icon={<Ionicons name="settings-outline" size={16} />}
                br="$6"
                hoverStyle={{ bg: "$primary" }}
                onPress={() => {
                  router.push({
                    pathname: "/(supplier-pages)/products/manage-additions",
                    params: { id: product.id },
                  });
                }}
              >
                <Text>Options</Text>
              </Button>

              <Button
                size="$3"
                variant="outlined"
                borderColor="$gray8"
                color="$gray10"
                icon={<Ionicons name="create-outline" size={16} />}
                br="$6"
                hoverStyle={{ bg: "$gray8" }}
                onPress={() => {
                  router.push({
                    pathname: "/(supplier-pages)/products/edit-product",
                    params: { id: product.id },
                  });
                }}
              >
                <Text>Edit</Text>
              </Button>

              <Button
                size="$3"
                variant="outlined"
                borderColor="$red8"
                color="$red10"
                icon={<Ionicons name="trash-outline" size={16} />}
                br="$6"
                hoverStyle={{ bg: "$red8" }}
                onPress={() => {
                  Alert.alert(
                    'Delete Product',
                    `Are you sure you want to delete "${product.name}"? This action cannot be undone.`,
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: async () => {
                          try {
                            console.log('🗑️ Delete button pressed for product:', product.id, product.name);
                            await deleteProduct(product.id);
                            console.log('✅ Delete completed successfully');

                            // Refresh products list to ensure UI is updated
                            await loadProducts();
                            console.log('🔄 Products list refreshed');

                            Alert.alert('Success', 'Product deleted successfully');
                          } catch (error) {
                            console.error('❌ Delete failed in UI:', error);
                            Alert.alert('Error', 'Failed to delete product. Please try again.');
                          }
                        }
                      }
                    ]
                  );
                }}
              >
                <Text>Delete</Text>
              </Button>
            </XStack>
          </YStack>
        </XStack>
      </Card>
  );
}

// Grid View Product Card Component
function ProductCardGrid({ product }: { product: Product }) {
  const hasDiscount = product.discountPrice && product.discountPrice > 0;
  const optionsCount = getProductOptionsCount(product);
  const router = useRouter();
  const { deleteProduct, loadProducts } = useSupplierProducts();

  return (
    <Card
      elevate
      br="$8"
      p="$4"
      borderWidth={2}
      borderColor={optionsCount > 0 ? "$secondary" : "$gray4"}
      bg="white"
      hoverStyle={{
        borderColor: "$primary",
        shadowColor: "$primary",
        shadowOpacity: 0.2,
        shadowRadius: 8,
        scale: 1.05
      }}
      pressStyle={{ scale: 0.95 }}
      height={280}
    >
      <YStack gap="$3" height="100%">
        <View style={{ position: 'relative', alignItems: 'center' }}>
          <Image
            source={{ uri: product.image }}
            style={{
              width: 80,
              height: 80,
              borderRadius: 10,
              borderWidth: 2,
              borderColor: optionsCount > 0 ? '#67B329' : '#e5e5e5'
            }}
          />
          {optionsCount > 0 && (
            <View
              style={{
                position: 'absolute',
                top: -6,
                right: -6,
                backgroundColor: '#67B329',
                borderRadius: 10,
                paddingHorizontal: 6,
                paddingVertical: 2,
                borderWidth: 2,
                borderColor: 'white'
              }}
            >
              <Text color="white" fontSize="$1" fontWeight="bold">
                {optionsCount}
              </Text>
            </View>
          )}
        </View>


        <YStack gap="$2" flex={1} jc="space-between">
          <YStack gap="$1">
            <H4 fontWeight="700" color="$gray12" numberOfLines={2} textAlign="center" fontSize="$4">
              {product.name}
            </H4>

            <Text color="$gray10" fontSize="$2" textAlign="center" numberOfLines={1}>
              📂 {product.category}
            </Text>

            <XStack gap="$1" ai="center" jc="center">
              <Text color={hasDiscount ? "$red10" : "$secondary"} fontWeight="700" fontSize="$4">
                {`${product.price} ₪`}
              </Text>
              {hasDiscount && (
                <Text color="$gray9" textDecorationLine="line-through" fontSize="$2">
                  {`${product.discountPrice} ₪`}
                </Text>
              )}
            </XStack>

            {optionsCount > 0 ? (
              <Text color="$secondary" fontSize="$2" fontWeight="600" textAlign="center">
                ✅ {optionsCount} options
              </Text>
            ) : (
              <Text color="$orange10" fontSize="$2" textAlign="center">
                ⚠️ No options
              </Text>
            )}
          </YStack>

          <YStack gap="$2">

            <Button
              size="$2"
              bg="$primary"
              color="white"
              icon={<Ionicons name="settings-outline" size={14} />}
              br="$4"
              onPress={() => {
                console.log('Manage options for:', product.id);
                router.push({
                  pathname: "/(supplier-pages)/products/manage-additions",
                  params: { id: product.id },
                });
              }}
            >
              <Text>Options</Text>
            </Button>

            <XStack gap="$2">
              <Button
                flex={1}
                size="$2"
                variant="outlined"
                borderColor="$gray8"
                color="$gray10"
                icon={<Ionicons name="create-outline" size={12} />}
                br="$4"
                onPress={() => {
                  router.push({
                    pathname: "/(supplier-pages)/products/edit-product",
                    params: { id: product.id },
                  });
                }}
              >
                <Text>Edit</Text>
              </Button>

              <Button
                flex={1}
                size="$2"
                variant="outlined"
                borderColor="$red8"
                color="$red10"
                icon={<Ionicons name="trash-outline" size={12} />}
                br="$4"
                onPress={() => {
                  Alert.alert(
                    'Delete Product',
                    `Are you sure you want to delete "${product.name}"? This action cannot be undone.`,
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: async () => {
                          try {
                            console.log('🗑️ Delete button pressed for product:', product.id, product.name);
                            await deleteProduct(product.id);
                            console.log('✅ Delete completed successfully');

                            // Refresh products list to ensure UI is updated
                            await loadProducts();
                            console.log('🔄 Products list refreshed');

                            Alert.alert('Success', 'Product deleted successfully');
                          } catch (error) {
                            console.error('❌ Delete failed in UI:', error);
                            Alert.alert('Error', 'Failed to delete product. Please try again.');
                          }
                        }
                      }
                    ]
                  );
                }}
              >
                <Text>Delete</Text>
              </Button>
            </XStack>
          </YStack>
        </YStack>
      </YStack>
    </Card>
  );
}
