import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import * as parser from "@babel/parser";
import * as babelTraverse from "@babel/traverse";

const traverse = babelTraverse.default.default;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const visitedFiles = new Set();

function parseFile(filePath) {
  if (!fs.existsSync(filePath) || visitedFiles.has(filePath)) return;
  visitedFiles.add(filePath);

  const code = fs.readFileSync(filePath, "utf8");

  let ast;
  try {
    ast = parser.parse(code, {
      sourceType: "module",
      plugins: ["jsx", "typescript"],
      locations: true,
    });
  } catch (err) {
    console.error(`Failed to parse ${filePath}:`, err.message);
    return;
  }

  traverse(ast, {
    JSXText({ node, parent }) {
      const text = node.value.trim();
      if (text && parent.openingElement?.name?.name !== "Text") {
        const line = node.loc.start.line;
        console.log(`⚠️ Text not inside <Text> in ${filePath} (line ${line}): "${text}"`);
      }
    },
    JSXExpressionContainer({ node, parent }) {
      if (node.expression.type === "StringLiteral") {
        const isInsideProp = parent.type === "JSXAttribute";
        const isInsideTextTag = parent.openingElement?.name?.name === "Text";

        if (!isInsideProp && !isInsideTextTag) {
          const line = node.loc.start.line;
          console.log(`⚠️ Text not inside <Text> in ${filePath} (line ${line}): "${node.expression.value}"`);
        }
      }
    },
  });

  const importRegex = /import\s+([A-Z][A-Za-z0-9]*)\s+from\s+['"](.+)['"]/g;
  let imp;
  while ((imp = importRegex.exec(code)) !== null) {
    let importPath = imp[2];
    if (!importPath.startsWith(".")) continue;

    let newFilePath = path.resolve(path.dirname(filePath), importPath);
    const extensions = [".tsx", ".ts", ".jsx", ".js"];
    for (const ext of extensions) {
      if (fs.existsSync(newFilePath + ext)) {
        parseFile(newFilePath + ext);
        break;
      }
    }
  }
}

parseFile(path.resolve(__dirname, "c:/Users/<USER>/OneDrive/Documents/wasel/components/supplier-pages-components/products-page-components/useSupplierCategories.tsx"));
