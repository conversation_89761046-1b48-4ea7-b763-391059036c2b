import React, { useState } from 'react';
import { ImageIcon } from 'lucide-react';

interface SafeImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackIcon?: React.ReactNode;
  onError?: () => void;
}

/**
 * SafeImage component that handles various image URL formats and provides fallbacks
 * - Handles base64 data URLs (data:image/...)
 * - Handles HTTP/HTTPS URLs
 * - Provides fallback for file:// URLs (which can't be displayed in browsers)
 * - Shows placeholder icon when image fails to load
 */
export const SafeImage: React.FC<SafeImageProps> = ({ 
  src, 
  alt, 
  className = '', 
  fallbackIcon,
  onError 
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check if the image source is a file:// URL (which browsers can't display)
  const isFileUrl = src?.startsWith('file://');
  
  // Check if the image source is valid
  const isValidImageUrl = src && (
    src.startsWith('data:image/') || 
    src.startsWith('http://') || 
    src.startsWith('https://') ||
    src.startsWith('/')
  );

  const handleImageError = () => {
    setHasError(true);
    setIsLoading(false);
    onError?.();
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  // If it's a file:// URL or invalid URL, show fallback immediately
  if (isFileUrl || !isValidImageUrl || hasError) {
    return (
      <div className={`flex items-center justify-center bg-gray-200 ${className}`}>
        {fallbackIcon || (
          <div className="flex flex-col items-center justify-center text-gray-400 p-4">
            <ImageIcon size={32} />
            <span className="text-xs mt-2 text-center">
              {isFileUrl ? 'Local image not accessible' : 'Image not available'}
            </span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 animate-pulse">
          <div className="text-gray-400">Loading...</div>
        </div>
      )}
      <img
        src={src}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        style={{ display: hasError ? 'none' : 'block' }}
      />
    </div>
  );
};

export default SafeImage;
