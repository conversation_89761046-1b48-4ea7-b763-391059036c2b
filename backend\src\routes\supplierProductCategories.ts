import express from 'express';
import { SupplierProductCategoryController } from '../controllers/supplierProductCategoryController';
import { authenticate } from '../middleware/auth';
import { body, param } from 'express-validator';

const router = express.Router();

// Validation rules for supplier product category operations
const categoryValidation = [
  body('name')
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category name must be a string between 1 and 50 characters'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Description must be a string with maximum 200 characters'),
  body('color')
    .optional()
    .isString()
    .trim()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color code'),
  body('icon')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Icon must be a string between 1 and 50 characters')
];

const updateCategoryValidation = [
  body('name')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category name must be a string between 1 and 50 characters'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Description must be a string with maximum 200 characters'),
  body('color')
    .optional()
    .isString()
    .trim()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color code'),
  body('icon')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Icon must be a string between 1 and 50 characters')
];

const paramValidation = [
  param('supplierId')
    .isString()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Supplier ID must be a valid string'),
  param('categoryId')
    .isMongoId()
    .withMessage('Category ID must be a valid MongoDB ObjectId')
];

// Get all categories for a specific supplier
router.get('/:supplierId', paramValidation.slice(0, 1), SupplierProductCategoryController.getSupplierCategories);

// Create a new category for a supplier
router.post('/:supplierId', 
  authenticate, 
  paramValidation.slice(0, 1), 
  categoryValidation, 
  SupplierProductCategoryController.createSupplierCategory
);

// Update a supplier category
router.put('/:categoryId', 
  authenticate, 
  paramValidation.slice(1), 
  updateCategoryValidation, 
  SupplierProductCategoryController.updateSupplierCategory
);

// Delete a supplier category (soft delete)
router.delete('/:categoryId', 
  authenticate, 
  paramValidation.slice(1), 
  SupplierProductCategoryController.deleteSupplierCategory
);

// Toggle category status
router.patch('/:categoryId/toggle-status', 
  authenticate, 
  paramValidation.slice(1), 
  SupplierProductCategoryController.toggleSupplierCategoryStatus
);

export default router;
