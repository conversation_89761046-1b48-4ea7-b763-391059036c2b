import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, User, Mail, MapPin, Users, Edit } from 'lucide-react';
import { useForm } from 'react-hook-form';

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  username: string;
  dateOfBirth?: string;
  gender?: string;
  address: string;
  city: string;
  country: string;
  isActive: boolean;
  createdAt: string;
}

interface EditEmployeeModalProps {
  employee: Employee;
  onClose: () => void;
  onSubmit: (data: any) => Promise<{ success: boolean; message?: string }>;
}

interface EmployeeFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password?: string;
  confirmPassword?: string;
  username: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female';
  address?: string;
  city?: string;
  country?: string;
  notifications: boolean;
}

const EditEmployeeModal: React.FC<EditEmployeeModalProps> = ({ employee, onClose, onSubmit }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [changePassword, setChangePassword] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<EmployeeFormData>({
    defaultValues: {
      firstName: employee.firstName,
      lastName: employee.lastName,
      email: employee.email,
      phoneNumber: employee.phoneNumber,
      username: employee.username,
      dateOfBirth: employee.dateOfBirth,
      gender: employee.gender as any,
      address: employee.address,
      city: employee.city,
      country: employee.country,
      notifications: true, // Default to true since we don't have this data
    },
  });

  const watchedPassword = watch('password');

  const onFormSubmit = async (data: EmployeeFormData) => {
    if (changePassword && data.password !== data.confirmPassword) {
      setSubmitError('Passwords do not match');
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      const { confirmPassword, ...submitData } = data;
      
      // Only include password if changing it
      if (!changePassword) {
        delete submitData.password;
      }
      
      const result = await onSubmit(submitData);
      
      if (!result.success) {
        setSubmitError(result.message || 'Failed to update employee');
      }
    } catch (error) {
      setSubmitError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="relative bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900 border border-white/20 rounded-3xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden"
        >
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

          {/* Scrollable Content */}
          <div className="max-h-[90vh] overflow-y-auto"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(255, 255, 255, 0.3) transparent'
            }}
          >
            {/* Enhanced Header */}
            <div className="relative p-8 border-b border-white/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl"
                  >
                    <Users className="text-white" size={28} />
                  </motion.div>
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-1">Edit Team Member</h2>
                    <p className="text-white/70">Update employee information and permissions</p>
                    <div className="flex items-center gap-2 mt-2 text-blue-400 text-sm">
                      <User size={14} />
                      <span>{employee.firstName} {employee.lastName}</span>
                    </div>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-3 bg-white/10 hover:bg-white/20 rounded-2xl text-white/80 hover:text-white transition-all duration-300"
                >
                  <X size={24} />
                </motion.button>
              </div>
            </div>

            {/* Enhanced Form */}
            <form onSubmit={handleSubmit(onFormSubmit)} className="p-8 space-y-8">
              {submitError && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-500/20 border border-red-500/30 text-red-400 px-6 py-4 rounded-2xl backdrop-blur-sm"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    {submitError}
                  </div>
                </motion.div>
              )}

              {/* Basic Information Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-500/20 rounded-xl">
                    <User size={24} className="text-blue-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Basic Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      {...register('firstName', { required: 'First name is required' })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter first name"
                    />
                    {errors.firstName && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.firstName.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      {...register('lastName', { required: 'Last name is required' })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter last name"
                    />
                    {errors.lastName && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.lastName.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Username *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        {...register('username', {
                          required: 'Username is required',
                          pattern: {
                            value: /^[a-zA-Z0-9_]+$/,
                            message: 'Username can only contain letters, numbers, and underscores'
                          }
                        })}
                        className="w-full px-4 py-3 pl-8 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                        placeholder="Enter username"
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60">
                        @
                      </div>
                    </div>
                    {errors.username && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.username.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Gender
                    </label>
                    <select
                      {...register('gender')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 appearance-none cursor-pointer"
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.75rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em'
                      }}
                    >
                      <option value="" className="bg-slate-800 text-white">Select gender</option>
                      <option value="male" className="bg-slate-800 text-white">Male</option>
                      <option value="female" className="bg-slate-800 text-white">Female</option>
                      <option value="other" className="bg-slate-800 text-white">Other</option>
                      <option value="prefer-not-to-say" className="bg-slate-800 text-white">Prefer not to say</option>
                    </select>
                  </div>
                </div>
              </motion.div>

              {/* Contact Information Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-emerald-500/20 rounded-xl">
                    <Mail size={24} className="text-emerald-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Contact Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      {...register('email', {
                        required: 'Email is required',
                        pattern: {
                          value: /^\S+@\S+$/i,
                          message: 'Invalid email address'
                        }
                      })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.email.message}
                      </motion.p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      {...register('phoneNumber', { required: 'Phone number is required' })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-300"
                      placeholder="+****************"
                    />
                    {errors.phoneNumber && (
                      <motion.p
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-red-400 text-sm mt-1 flex items-center gap-2"
                      >
                        <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                        {errors.phoneNumber.message}
                      </motion.p>
                    )}
                  </div>
                </div>
              </motion.div>

              {/* Password Change Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="space-y-6"
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-red-500/20 rounded-xl">
                      <User size={24} className="text-red-400" />
                    </div>
                    <h3 className="text-xl font-bold text-white">Security & Access</h3>
                  </div>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={changePassword}
                      onChange={(e) => setChangePassword(e.target.checked)}
                      className="w-5 h-5 text-red-600 bg-white/10 border-white/20 rounded focus:ring-red-500 focus:ring-2"
                    />
                    <span className="text-sm font-medium text-white/80">
                      Change password
                    </span>
                  </label>
                </div>

                {changePassword && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="grid grid-cols-1 md:grid-cols-2 gap-6"
                  >
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-white/80 mb-2">
                        New Password *
                      </label>
                      <input
                        type="password"
                        {...register('password', {
                          required: changePassword ? 'Password is required' : false,
                          minLength: {
                            value: 6,
                            message: 'Password must be at least 6 characters'
                          }
                        })}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                        placeholder="Create new password"
                      />
                      {errors.password && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-red-400 text-sm mt-1 flex items-center gap-2"
                        >
                          <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                          {errors.password.message}
                        </motion.p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-white/80 mb-2">
                        Confirm New Password *
                      </label>
                      <input
                        type="password"
                        {...register('confirmPassword', {
                          required: changePassword ? 'Please confirm password' : false,
                          validate: value => !changePassword || value === watchedPassword || 'Passwords do not match'
                        })}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-300"
                        placeholder="Confirm new password"
                      />
                      {errors.confirmPassword && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-red-400 text-sm mt-1 flex items-center gap-2"
                        >
                          <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                          {errors.confirmPassword.message}
                        </motion.p>
                      )}
                    </div>
                  </motion.div>
                )}
              </motion.div>

              {/* Additional Information Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-purple-500/20 rounded-xl">
                    <MapPin size={24} className="text-purple-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white">Additional Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Date of Birth
                    </label>
                    <input
                      type="date"
                      {...register('dateOfBirth')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      City
                    </label>
                    <input
                      type="text"
                      {...register('city')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter city"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Country
                    </label>
                    <input
                      type="text"
                      {...register('country')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter country"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-white/80 mb-2">
                      Address
                    </label>
                    <input
                      type="text"
                      {...register('address')}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      placeholder="Enter full address"
                    />
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Action Buttons */}
              <div className="flex gap-4 pt-8 border-t border-white/20">
                <motion.button
                  type="button"
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onClose}
                  className="flex-1 px-6 py-4 bg-white/10 border border-white/20 text-white rounded-2xl font-medium hover:bg-white/20 transition-all duration-300"
                >
                  Cancel
                </motion.button>

                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  whileHover={!isSubmitting ? { scale: 1.02, y: -2 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                  className="flex-1 px-6 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
                >
                  <div className="flex items-center justify-center gap-2">
                    {isSubmitting ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                        />
                        Updating Employee...
                      </>
                    ) : (
                      <>
                        <Edit size={20} />
                        Update Employee
                      </>
                    )}
                  </div>
                </motion.button>
              </div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default EditEmployeeModal;
