import mongoose, { Document, Schema } from 'mongoose';

export interface ISupplierProductCategory extends Document {
  supplierId: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const SupplierProductCategorySchema: Schema = new Schema({
  supplierId: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    minlength: 1,
    maxlength: 50
  },
  description: {
    type: String,
    trim: true,
    maxlength: 200
  },
  color: {
    type: String,
    trim: true,
    default: '#3B82F6' // Default blue color
  },
  icon: {
    type: String,
    trim: true,
    default: 'tag' // Default icon
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound index to ensure unique category names per supplier
SupplierProductCategorySchema.index({ supplierId: 1, name: 1 }, { unique: true });

// Index for efficient queries
SupplierProductCategorySchema.index({ supplierId: 1, isActive: 1 });

export const SupplierProductCategory = mongoose.model<ISupplierProductCategory>('SupplierProductCategory', SupplierProductCategorySchema);
