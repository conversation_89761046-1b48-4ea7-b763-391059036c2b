import { create } from 'zustand';

type CategoryStore = {
  categories: string[];
  selectedCategory: string | null;
  addCategory: (cat: string) => void;
  deleteCategory: (cat: string) => void;
  selectCategory: (cat: string | null) => void;
  renameCategory: (oldCat: string, newCat: string) => void;
}

export const useSupplierCategories = create<CategoryStore>((set) => ({
  categories: [],
  selectedCategory: null,
  addCategory: (cat) =>
    set((s) => {
      const name = (cat ?? '').trim();
      if (!name || s.categories.includes(name)) return s;
      return { categories: [...s.categories, name] };
    }),
  deleteCategory: (cat) =>
    set((s) => ({
      categories: s.categories.filter((c) => c !== cat),
      selectedCategory: s.selectedCategory === cat ? null : s.selectedCategory,
    })),
  selectCategory: (cat) => set(() => ({ selectedCategory: cat })),
  renameCategory: (oldCat, newCat) =>
    set((s) => {
      const name = (newCat ?? '').trim();
      if (!name) return s;
      return {
        categories: s.categories.map((c) => (c === oldCat ? name : c)),
      };
    }),
}));
