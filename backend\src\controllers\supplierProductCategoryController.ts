import { Request, Response } from 'express';
import { SupplierProductCategory } from '../models/SupplierProductCategory';
import { Supplier } from '../models/Supplier';
import { validationResult } from 'express-validator';
import { AuthenticatedRequest } from '../types';

export class SupplierProductCategoryController {
  // Get all categories for a specific supplier
  static async getSupplierCategories(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId } = req.params;

      const categories = await SupplierProductCategory.find({ 
        supplierId, 
        isActive: true 
      })
        .sort({ createdAt: 1 })
        .select('-__v');

      res.json({
        success: true,
        data: categories
      });
    } catch (error) {
      console.error('Error fetching supplier categories:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch supplier categories',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Create a new category for a supplier
  static async createSupplierCategory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { supplierId } = req.params;
      const { name, description, color, icon } = req.body;

      // Check if category with this name already exists for this supplier
      const existingCategory = await SupplierProductCategory.findOne({ 
        supplierId, 
        name: name.trim() 
      });
      
      if (existingCategory) {
        res.status(409).json({
          success: false,
          message: 'Category with this name already exists for this supplier'
        });
        return;
      }

      const category = new SupplierProductCategory({
        supplierId,
        name: name.trim(),
        description: description?.trim(),
        color: color || '#3B82F6',
        icon: icon || 'tag'
      });

      await category.save();

      res.status(201).json({
        success: true,
        message: 'Category created successfully',
        data: category
      });
    } catch (error) {
      console.error('Error creating supplier category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create category',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update a supplier category
  static async updateSupplierCategory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { categoryId } = req.params;
      const { name, description, color, icon } = req.body;

      const category = await SupplierProductCategory.findById(categoryId);
      
      if (!category) {
        res.status(404).json({
          success: false,
          message: 'Category not found'
        });
        return;
      }

      // Store old name before updating
      const oldName = category.name;

      // If name is being changed, check for duplicates
      if (name && name.trim() !== category.name) {
        const existingCategory = await SupplierProductCategory.findOne({ 
          supplierId: category.supplierId, 
          name: name.trim(),
          _id: { $ne: categoryId }
        });
        
        if (existingCategory) {
          res.status(409).json({
            success: false,
            message: 'Category with this name already exists for this supplier'
          });
          return;
        }
      }

      // Update fields
      if (name) category.name = name.trim();
      if (description !== undefined) category.description = description?.trim();
      if (color) category.color = color;
      if (icon) category.icon = icon;

      await category.save();

      // Update products in all suppliers that use this category
      // Use aggregation pipeline to find all products, then update individually
      if (name && name.trim() !== oldName) {
        const newName = name.trim();
        
        try {
          // Use aggregation to find all products with this categoryId
          const productsToUpdate = await Supplier.aggregate([
            { $unwind: '$products' },
            { $match: { 'products.categoryId': category._id } },
            { 
              $project: { 
                supplierId: '$_id', 
                productId: '$products._id',
                categoryId: '$products.categoryId'
              } 
            }
          ]);

          let totalUpdated = 0;
          
          // Update each product individually using the exact IDs
          for (const productInfo of productsToUpdate) {
            const updateResult = await Supplier.updateOne(
              { 
                _id: productInfo.supplierId,
                'products._id': productInfo.productId
              },
              { 
                $set: { 'products.$.categoryName': newName } 
              }
            );
            
            if (updateResult.modifiedCount > 0) {
              totalUpdated++;
            }
          }

          console.log(`Updated ${totalUpdated} products with category name change from "${oldName}" to "${newName}"`);
        } catch (updateError) {
          console.error('Error updating products with new category name:', updateError);
          // Don't fail the entire operation if product update fails
          // The category was still updated successfully
        }
      }

      res.json({
        success: true,
        message: 'Category updated successfully',
        data: category
      });
    } catch (error) {
      console.error('Error updating supplier category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update category',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Delete a supplier category (soft delete by setting isActive to false)
  static async deleteSupplierCategory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { categoryId } = req.params;

      const category = await SupplierProductCategory.findById(categoryId);
      
      if (!category) {
        res.status(404).json({
          success: false,
          message: 'Category not found'
        });
        return;
      }

      // Soft delete by setting isActive to false
      category.isActive = false;
      await category.save();

      res.json({
        success: true,
        message: 'Category deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting supplier category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete category',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Toggle category status
  static async toggleSupplierCategoryStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { categoryId } = req.params;

      const category = await SupplierProductCategory.findById(categoryId);
      
      if (!category) {
        res.status(404).json({
          success: false,
          message: 'Category not found'
        });
        return;
      }

      category.isActive = !category.isActive;
      await category.save();

      res.json({
        success: true,
        message: `Category ${category.isActive ? 'activated' : 'deactivated'} successfully`,
        data: category
      });
    } catch (error) {
      console.error('Error toggling supplier category status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to toggle category status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
