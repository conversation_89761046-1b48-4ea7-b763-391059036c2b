import { ScrollView } from 'react-native'
import { View, Text, Button, XStack, YStack } from 'tamagui'
import { useRouter } from 'expo-router'
import { useLastOrderStore } from './useLastOrderStore'
import { MotiView } from 'moti'
import { Ionicons } from '@expo/vector-icons'

export function OrderConfirmation() {
  const router = useRouter();
  const lastOrderGroup = useLastOrderStore((s) => s.lastOrderGroup);

  if (!lastOrderGroup || lastOrderGroup.length === 0) {
    router.replace('/home');
    return null;
  }

  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}>
      {/* Header */}
      <View
        style={{
            paddingVertical: 40,
            paddingHorizontal: 24,
            borderBottomLeftRadius: 32,
            borderBottomRightRadius: 32,
            backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
            backgroundColor: '#7529B3', // fallback for platforms without gradient support
        }}
        >
        <MotiView
            from={{ opacity: 0, translateY: -20 }}
            animate={{ opacity: 1, translateY: 0 }}
        >
            <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
            Order Placed Successfully, Thank You!
            </Text>
            <Text fontSize="$5" color="white" textAlign="center" mt="$2">
            We are doing it for you now
            </Text>
        </MotiView>
      </View>

      <YStack px="$4" py="$6" ai="center" gap="$5">
        {/* Success Icon */}
        <MotiView
          from={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
        >
          <View
            bg="$secondary"
            width={80}
            height={80}
            br="$10"
            jc="center"
            ai="center"
            shadowColor="#000"
            shadowOffset={{ width: 0, height: 4 }}
            shadowOpacity={0.1}
            shadowRadius={6}
          >
            <Ionicons name="checkmark" size={40} color="white" />
          </View>
        </MotiView>

        {/* Order Cards */}
        {lastOrderGroup.map((order) => (
          <View
            bg="$backgroundStrong"
            key={order.id}
            width="100%"
            maxWidth={380}
            p="$4"
            br="$6"
            borderWidth={1}
            borderColor="$colorTransparent"
          >
            <Text fontWeight="600" fontSize="$5">Order ID 📦:</Text>
            <Text mb="$3">{order.id}</Text>

            <Text fontWeight="600" fontSize="$5">Possible Time ⏱:</Text>
            <Text mb="$3">{order.estimatedTime}</Text>

            <Text fontWeight="600" fontSize="$5">Total Price 💵:</Text>
            <Text mb="$1">₪{order.total.toFixed(2)}</Text>

            <Text fontWeight="600" fontSize="$5">Delivery Status 🚚:</Text>
            <Text mb="$3">{order.orderStatus || 'Pending'}</Text>

            {order.driverName && (
              <>
                <Text fontWeight="600" fontSize="$5">Driver 👤:</Text>
                <Text mb="$3">{order.driverName}</Text>
              </>
            )}
          </View>
        ))}

        {/* Buttons */}
        <YStack gap="$3" width="100%" maxWidth={380}>
          <Button size="$5" bg="$primary" color="white" hoverStyle={{ bg: "$third" }} pressStyle={{ bg: "$third" }} onPress={() => router.push('/orders')}>
            <Text>Track Order</Text>
          </Button>
          <Button size="$5" bg="$gray6" onPress={() => router.replace('/home')}>
            <Text>Go Back Home</Text>
          </Button>
        </YStack>
      </YStack>
    </ScrollView>
  )
}
