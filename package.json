{"name": "wasel", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start --dev-client", "ios": "expo run:ios", "android": "expo run:android", "build:dev": "eas build --profile development", "build:preview": "eas build --profile preview", "build:prod": "eas build --profile production", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/native": "^7.0.3", "@tamagui/animations-react-native": "^1.131.2", "@tamagui/avatar": "^1.131.2", "@tamagui/config": "^1.131.2", "@tamagui/core": "^1.131.2", "@tamagui/font-inter": "^1.131.2", "@tamagui/get-token": "^1.131.2", "@tamagui/lucide-icons": "^1.131.2", "@tamagui/portal": "^1.131.2", "@tamagui/react-native-media-driver": "^1.131.2", "@tamagui/shorthands": "^1.131.2", "@tamagui/themes": "^1.131.2", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "expo": "^53.0.16", "expo-auth-session": "^6.2.1", "expo-constants": "~17.1.4", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.0", "expo-dev-launcher": "^5.0.17", "expo-font": "~13.3.1", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.4", "expo-localization": "~16.1.6", "expo-location": "~18.1.5", "expo-router": "~5.1.0", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "firebase": "^10.5.2", "helmet": "^8.1.0", "i18next": "^25.3.1", "jsonwebtoken": "^9.0.2", "lottie-react-native": "7.2.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "moti": "^0.30.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.4", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-i18next": "^15.6.0", "react-native": "^0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-uuid": "^2.0.3", "react-native-web": "^0.20.0", "react-native-web-maps": "^0.3.0", "react-native-webview": "13.13.5", "tamagui": "^1.131.2", "uuid": "^11.1.0", "zustand": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "latest", "@tamagui/babel-plugin": "^1.131.2", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^0.0.48", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.10", "@types/nodemailer": "^6.4.17", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/uuid": "^10.0.0", "ajv": "^8.12.0", "eslint": "^9.33.0", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-native": "^5.0.0", "nodemon": "^3.1.10", "prettier": "^3.2.5", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "private": true, "description": "delivery application", "repository": {"type": "git", "url": "git+https://github.com/OmarJarbou/wasel.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/OmarJarbou/wasel/issues"}, "homepage": "https://github.com/OmarJarbou/wasel#readme"}