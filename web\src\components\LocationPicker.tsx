import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import { LatLng } from 'leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface LocationPickerProps {
  onLocationSelect: (location: [number, number]) => void;
  initialLocation?: [number, number];
  className?: string;
}

interface LocationMarkerProps {
  position: LatLng | null;
  setPosition: (position: LatLng) => void;
}

const LocationMarker: React.FC<LocationMarkerProps> = ({ position, setPosition }) => {
  useMapEvents({
    click(e) {
      setPosition(e.latlng);
    },
  });

  return position === null ? null : (
    <Marker position={position} />
  );
};

const LocationPicker: React.FC<LocationPickerProps> = ({
  onLocationSelect,
  initialLocation,
  className = ''
}) => {
  // Default to Nablus, Palestine
  const defaultCenter: [number, number] = [32.2211, 35.2544];
  const [position, setPosition] = useState<LatLng | null>(
    initialLocation ? new LatLng(initialLocation[0], initialLocation[1]) : null
  );

  useEffect(() => {
    if (position) {
      onLocationSelect([position.lat, position.lng]);
    }
  }, [position, onLocationSelect]);

  const handleGetCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newPos = new LatLng(latitude, longitude);
          setPosition(newPos);
        },
        (error) => {
          console.error('Error getting location:', error);
          // Fallback to default location
          const newPos = new LatLng(defaultCenter[0], defaultCenter[1]);
          setPosition(newPos);
        }
      );
    } else {
      // Fallback to default location
      const newPos = new LatLng(defaultCenter[0], defaultCenter[1]);
      setPosition(newPos);
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-white/80">
          Location (Optional)
        </label>
        <button
          type="button"
          onClick={handleGetCurrentLocation}
          className="px-3 py-1 text-xs bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors"
        >
          Use Current Location
        </button>
      </div>
      
      <div className="h-64 rounded-xl overflow-hidden border border-white/20">
        <MapContainer
          center={initialLocation || defaultCenter}
          zoom={13}
          style={{ height: '100%', width: '100%' }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          <LocationMarker position={position} setPosition={setPosition} />
        </MapContainer>
      </div>
      
      {position && (
        <div className="text-sm text-white/60">
          Selected: {position.lat.toFixed(4)}, {position.lng.toFixed(4)}
        </div>
      )}
      
      <p className="text-xs text-white/50">
        Click on the map to select the employee's location
      </p>
    </div>
  );
};

export default LocationPicker;
