import { Ionicons } from "@expo/vector-icons"
import { Dimensions, Image, Alert, Pressable, ActionSheetIOS, Platform } from "react-native"
import {
  Button,
  Card,
  Input,
  Label,
  ScrollView,
  Text,
  YStack,
  XStack,
  Select,
  View,
  H2,
  H4,
  Separator,
  Progress,
  Spinner
} from "tamagui"
import * as ImagePicker from "expo-image-picker"
import { useRouter, useLocalSearchParams } from "expo-router"
import { useSupplierProducts } from "./useSupplierProducts"
import { useSupplierCategories } from "./useSupplierCategories"
import { useEffect, useState, useCallback } from "react";
import { AnimatePresence, MotiView } from "moti"
import { LinearGradient } from "expo-linear-gradient"

export default function EditProductScreen() {
  const { updateProduct, products } = useSupplierProducts();
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const width = Dimensions.get("window").width;

  // Find the product to edit
  const existingProduct = products.find(p => p.id === id);

  // Enhanced state management - Initialize with existing product data
  const [product, setProduct] = useState({
    name: "",
    price: 0,
    discountPrice: 0,
    image: "",
    category: "",
    description: "",
    isAvailable: true,
    isPromotion: false,
    restaurantOptions: {
      additions: [],
      without: [],
      sides: [],
    },
    clothingOptions: {
      sizes: [],
      colors: [],
      gallery: [],
    },
    customOptions: [],
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);

  // Load existing product data when component mounts
  useEffect(() => {
    if (existingProduct) {
      setProduct({
        name: existingProduct.name || "",
        price: existingProduct.price || 0,
        discountPrice: existingProduct.discountPrice || 0,
        image: existingProduct.image || "",
        category: existingProduct.category || "",
        description: existingProduct.description || "",
        isAvailable: existingProduct.isAvailable ?? true,
        isPromotion: existingProduct.isPromotion ?? false,
        restaurantOptions: existingProduct.restaurantOptions || {
          additions: [],
          without: [],
          sides: [],
        },
        clothingOptions: existingProduct.clothingOptions || {
          sizes: [],
          colors: [],
          gallery: [],
        },
        customOptions: existingProduct.customOptions || [],
      });
    }
  }, [existingProduct]);

  // Form validation
  const validateField = useCallback((field: string, value: any) => {
    const newErrors = { ...errors };

    switch (field) {
      case 'name':
        if (!value || value.trim().length < 2) {
          newErrors.name = 'Product name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          newErrors.name = 'Product name must be less than 50 characters';
        } else {
          delete newErrors.name;
        }
        break;
      case 'price':
        if (!value || isNaN(value) || value <= 0) {
          newErrors.price = 'Please enter a valid price';
        } else if (value > 10000) {
          newErrors.price = 'Price seems too high (max: 10,000)';
        } else {
          delete newErrors.price;
        }
        break;
      case 'category':
        if (!value) {
          newErrors.category = 'Please select a category';
        } else {
          delete newErrors.category;
        }
        break;
      case 'image':
        if (!value) {
          newErrors.image = 'Please upload a product image';
        } else {
          delete newErrors.image;
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [errors]);

  // Enhanced form validation
  const validateForm = useCallback(() => {
    const fields = ['name', 'price', 'category', 'image'];
    let isValid = true;

    fields.forEach(field => {
      const fieldValue = field === 'price' ? product.price : product[field as keyof typeof product];
      if (!validateField(field, fieldValue)) {
        isValid = false;
      }
    });

    return isValid;
  }, [product, validateField]);

  // Enhanced image picker with better error handling
  const pickImage = useCallback(async () => {
    console.log('🖼️ pickImage called');
    try {
      setImageLoading(true);

      console.log('📱 Requesting media library permissions...');
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log('📱 Permission result:', permissionResult);

      if (permissionResult.granted === false) {
        console.log('❌ Permission denied');
        Alert.alert(
          "Permission Required",
          "Please allow access to your photo library to upload product images.",
          [{ text: "OK" }]
        );
        return;
      }

      console.log('🚀 Launching image library...');
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      console.log('📸 Image picker result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        console.log('✅ Image selected:', imageUri);

        // Convert local file URI to base64 data URL for cross-platform compatibility
        try {
          const response = await fetch(imageUri);
          const blob = await response.blob();
          const reader = new FileReader();

          reader.onload = () => {
            const base64DataUrl = reader.result as string;
            console.log('✅ Image converted to base64');
            setProduct(prev => ({ ...prev, image: base64DataUrl }));
            validateField('image', base64DataUrl);
            setImageLoading(false);
          };

          reader.onerror = () => {
            console.log('❌ Failed to convert image to base64');
            Alert.alert('Error', 'Failed to process image. Please try again.');
            setImageLoading(false);
          };

          reader.readAsDataURL(blob);
          return; // Don't set loading to false here, let the reader callbacks handle it
        } catch (error) {
          console.log('❌ Error converting image:', error);
          Alert.alert('Error', 'Failed to process image. Please try again.');
        }
      } else {
        console.log('❌ Image selection canceled or failed');
      }
    } catch (error) {
      console.error('❌ Error picking image:', error);
      Alert.alert("Error", "Failed to pick image. Please try again.");
    } finally {
      setImageLoading(false);
    }
  }, [validateField]);

  // Camera picker function
  const pickImageFromCamera = useCallback(async () => {
    console.log('📷 pickImageFromCamera called');
    try {
      setImageLoading(true);

      console.log('📱 Requesting camera permissions...');
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      console.log('📱 Camera permission result:', permissionResult);

      if (permissionResult.granted === false) {
        console.log('❌ Camera permission denied');
        Alert.alert(
          "Permission Required",
          "Please allow access to your camera to take product photos.",
          [{ text: "OK" }]
        );
        return;
      }

      console.log('🚀 Launching camera...');
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      console.log('📸 Camera result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        console.log('✅ Photo taken:', imageUri);

        // Convert local file URI to base64 data URL for cross-platform compatibility
        try {
          const response = await fetch(imageUri);
          const blob = await response.blob();
          const reader = new FileReader();

          reader.onload = () => {
            const base64DataUrl = reader.result as string;
            console.log('✅ Photo converted to base64');
            setProduct(prev => ({ ...prev, image: base64DataUrl }));
            validateField('image', base64DataUrl);
            setImageLoading(false);
          };

          reader.onerror = () => {
            console.log('❌ Failed to convert photo to base64');
            Alert.alert('Error', 'Failed to process photo. Please try again.');
            setImageLoading(false);
          };

          reader.readAsDataURL(blob);
          return; // Don't set loading to false here, let the reader callbacks handle it
        } catch (error) {
          console.log('❌ Error converting photo:', error);
          Alert.alert('Error', 'Failed to process photo. Please try again.');
        }
      } else {
        console.log('❌ Photo capture canceled or failed');
      }
    } catch (error) {
      console.error('❌ Error taking photo:', error);
      Alert.alert("Error", "Failed to take photo. Please try again.");
    } finally {
      setImageLoading(false);
    }
  }, [validateField]);

  // Show image picker options
  const showImagePicker = useCallback(() => {
    console.log('🔥 showImagePicker called');

    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['Cancel', 'Take Photo', 'Choose from Library'],
          cancelButtonIndex: 0,
        },
        (buttonIndex) => {
          console.log('📱 ActionSheet button pressed:', buttonIndex);
          if (buttonIndex === 1) {
            pickImageFromCamera();
          } else if (buttonIndex === 2) {
            pickImage();
          }
        }
      );
    } else {
      // For Android, show a simple alert
      Alert.alert(
        'Select Image',
        'Choose an option',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Take Photo', onPress: pickImageFromCamera },
          { text: 'Choose from Library', onPress: pickImage },
        ]
      );
    }
  }, [pickImage, pickImageFromCamera]);

  // Enhanced submit handler
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      Alert.alert(
        "Validation Error",
        "Please fix the errors in the form before submitting.",
        [{ text: "OK" }]
      );
      return;
    }

    if (!existingProduct) {
      Alert.alert("Error", "Product not found");
      return;
    }

    setIsLoading(true);

    try {
      console.log('📦 Updating product:', product);

      // Update product using backend API
      await updateProduct(existingProduct.id, product);

      // Show success message
      Alert.alert(
        'Success!',
        'Product updated successfully!',
        [
          {
            text: 'View Products',
            onPress: () => {
              router.push("/(supplier-pages)/products");
            }
          }
        ]
      );
    } catch (error: any) {
      console.error('❌ Error updating product:', error);
      Alert.alert(
        'Error',
        error?.message || 'Failed to update product. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  }, [product, validateForm, updateProduct, existingProduct, router]);

  // Enhanced categories with better organization
  const { categories } = useSupplierCategories();
  const filteredCategories = categories.filter((cat) => cat !== "All");

  if (!existingProduct) {
    return (
      <YStack flex={1} justifyContent="center" alignItems="center" gap="$4" padding="$4">
        <Ionicons name="alert-circle-outline" size={80} color="#ccc" />
        <Text fontSize="$5" color="$gray10">Product not found</Text>
        <Text fontSize="$3" color="$gray8" textAlign="center">
          The product you're trying to edit could not be found.
        </Text>
        <Button onPress={() => router.back()}>Go Back</Button>
      </YStack>
    );
  }

  return (
    <LinearGradient
      colors={['#f8fafc', '#e2e8f0', '#cbd5e1']}
      style={{ flex: 1 }}
    >
      <ScrollView
        contentContainerStyle={{
          paddingBottom: 100,
          paddingHorizontal: 16,
          paddingTop: 20
        }}
        showsVerticalScrollIndicator={false}
      >
        <YStack gap="$6" width="100%">
          {/* Enhanced Header */}
          <MotiView
            from={{ opacity: 0, translateY: -20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 100, duration: 600 }}
          >
            <Card
              elevate
              p="$5"
              br="$10"
              bg="white"
              borderWidth={2}
              borderColor="$primary"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.15}
              shadowRadius={16}
            >
              <XStack ai="center" gap="$4">
                <View
                  style={{
                    backgroundColor: '#f3f4f6',
                    borderRadius: 16,
                    padding: 12,
                  }}
                >
                  <Ionicons name="create-outline" size={32} color="#7529B3" />
                </View>
                <YStack flex={1}>
                  <H2 color="$primary" fontWeight="800" fontSize="$8">
                    Edit Product
                  </H2>
                  <Text color="$gray10" fontSize="$4" mt="$1">
                    Update your product information
                  </Text>
                </YStack>
              </XStack>
            </Card>
          </MotiView>

          {/* Enhanced Form Card */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 300, duration: 500 }}
          >
            <Card
              elevate
              p="$6"
              br="$10"
              bg="white"
              borderWidth={2}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
            >
              <YStack gap="$5">
                {/* Product Name Section */}
                <YStack gap="$3">
                  <XStack ai="center" gap="$2">
                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 8,
                        padding: 6,
                      }}
                    >
                      <Ionicons name="pricetag-outline" size={18} color="#7529B3" />
                    </View>
                    <H4 color="$gray12" fontWeight="700">Product Name</H4>
                    <Text color="$red10" fontSize="$3">*</Text>
                  </XStack>
                  <Input
                    placeholder="Enter product name..."
                    value={product.name}
                    onChangeText={(text) => {
                      setProduct(prev => ({ ...prev, name: text }));
                      validateField('name', text);
                    }}
                    size="$5"
                    br="$6"
                    borderColor={errors.name ? "$red8" : "$gray6"}
                    focusStyle={{ borderColor: "$primary" }}
                    bg="$gray1"
                  />
                  {errors.name && (
                    <Text color="$red10" fontSize="$3" mt="$1">
                      {errors.name}
                    </Text>
                  )}
                </YStack>

                {/* Price Section */}
                <YStack gap="$3">
                  <XStack ai="center" gap="$2">
                    <View
                      style={{
                        backgroundColor: '#dcfce7',
                        borderRadius: 8,
                        padding: 6,
                      }}
                    >
                      <Ionicons name="cash-outline" size={18} color="#16a34a" />
                    </View>
                    <H4 color="$gray12" fontWeight="700">Price</H4>
                    <Text color="$red10" fontSize="$3">*</Text>
                  </XStack>
                  <XStack gap="$3">
                    <YStack flex={1}>
                      <Label htmlFor="price" color="$gray11" fontSize="$3" mb="$2">
                        Regular Price (₪)
                      </Label>
                      <Input
                        id="price"
                        placeholder="0.00"
                        value={product.price.toString()}
                        onChangeText={(text) => {
                          const price = parseFloat(text) || 0;
                          setProduct(prev => ({ ...prev, price }));
                          validateField('price', price);
                        }}
                        keyboardType="numeric"
                        size="$4"
                        br="$5"
                        borderColor={errors.price ? "$red8" : "$gray6"}
                        focusStyle={{ borderColor: "$primary" }}
                        bg="$gray1"
                      />
                      {errors.price && (
                        <Text color="$red10" fontSize="$3" mt="$1">
                          {errors.price}
                        </Text>
                      )}
                    </YStack>
                    <YStack flex={1}>
                      <Label htmlFor="discountPrice" color="$gray11" fontSize="$3" mb="$2">
                        Discount Price (₪)
                      </Label>
                      <Input
                        id="discountPrice"
                        placeholder="0.00"
                        value={product.discountPrice.toString()}
                        onChangeText={(text) => {
                          const discountPrice = parseFloat(text) || 0;
                          setProduct(prev => ({ ...prev, discountPrice }));
                        }}
                        keyboardType="numeric"
                        size="$4"
                        br="$5"
                        borderColor="$gray6"
                        focusStyle={{ borderColor: "$primary" }}
                        bg="$gray1"
                      />
                    </YStack>
                  </XStack>
                </YStack>

                {/* Category Section */}
                <YStack gap="$3">
                  <XStack ai="center" gap="$2">
                    <View
                      style={{
                        backgroundColor: '#fef3c7',
                        borderRadius: 8,
                        padding: 6,
                      }}
                    >
                      <Ionicons name="grid-outline" size={18} color="#d97706" />
                    </View>
                    <H4 color="$gray12" fontWeight="700">Category</H4>
                    <Text color="$red10" fontSize="$3">*</Text>
                  </XStack>
                  <Select
                    value={product.category}
                    onValueChange={(value) => {
                      setProduct(prev => ({ ...prev, category: value }));
                      validateField('category', value);
                    }}
                    size="$5"
                  >
                    <Select.Trigger
                      br="$6"
                      borderColor={errors.category ? "$red8" : "$gray6"}
                      bg="$gray1"
                    >
                      <Select.Value placeholder="Select a category..." />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.ScrollUpButton />
                      <Select.Viewport>
                        {filteredCategories.map((cat, idx) => (
                          <Select.Item key={cat} index={idx} value={cat}>
                            <Select.ItemText>{cat}</Select.ItemText>
                          </Select.Item>
                        ))}
                      </Select.Viewport>
                      <Select.ScrollDownButton />
                    </Select.Content>
                  </Select>
                  {errors.category && (
                    <Text color="$red10" fontSize="$3" mt="$1">
                      {errors.category}
                    </Text>
                  )}
                </YStack>

                {/* Promotion Toggle Section */}
                <YStack gap="$3">
                  <XStack ai="center" gap="$2">
                    <View
                      style={{
                        backgroundColor: '#fef3c7',
                        borderRadius: 8,
                        padding: 6,
                      }}
                    >
                      <Ionicons name="star-outline" size={18} color="#f59e0b" />
                    </View>
                    <H4 color="$gray12" fontWeight="700">Promotion Settings</H4>
                  </XStack>

                  <Card
                    p="$4"
                    br="$6"
                    borderWidth={1}
                    borderColor="$gray6"
                    bg="$gray1"
                  >
                    <XStack ai="center" jc="space-between">
                      <YStack gap="$1">
                        <Text fontSize="$4" fontWeight="600" color="$gray12">
                          Feature in Promotions
                        </Text>
                        <Text fontSize="$3" color="$gray10">
                          Show this product in the customer promotions section
                        </Text>
                      </YStack>
                      <Button
                        size="$3"
                        variant={product.isPromotion ? "solid" : "outlined"}
                        bg={product.isPromotion ? "$orange6" : "transparent"}
                        borderColor={product.isPromotion ? "$orange6" : "$orange8"}
                        color={product.isPromotion ? "white" : "$orange10"}
                        icon={<Ionicons name={product.isPromotion ? "star" : "star-outline"} size={16} />}
                        br="$6"
                        hoverStyle={{
                          bg: product.isPromotion ? "$orange7" : "$orange6",
                          color: "white"
                        }}
                        onPress={() => {
                          setProduct(prev => ({
                            ...prev,
                            isPromotion: !prev.isPromotion
                          }));
                        }}
                      >
                        {product.isPromotion ? 'Featured' : 'Feature'}
                      </Button>
                    </XStack>
                  </Card>
                </YStack>

                {/* Image Section */}
                <YStack gap="$3">
                  <XStack ai="center" gap="$2">
                    <View
                      style={{
                        backgroundColor: '#e0e7ff',
                        borderRadius: 8,
                        padding: 6,
                      }}
                    >
                      <Ionicons name="image-outline" size={18} color="#4f46e5" />
                    </View>
                    <H4 color="$gray12" fontWeight="700">Product Image</H4>
                    <Text color="$red10" fontSize="$3">*</Text>
                  </XStack>

                  <Pressable
                    onPress={() => {
                      console.log('🔥 Pressable onPress triggered');
                      showImagePicker();
                    }}
                    disabled={imageLoading}
                    style={({ pressed }) => ({
                      opacity: pressed ? 0.8 : 1,
                    })}
                  >
                    <Card
                      p="$4"
                      br="$6"
                      borderWidth={2}
                      borderColor={errors.image ? "$red8" : "$gray6"}
                      borderStyle="dashed"
                      bg="$gray1"
                      hoverStyle={{ borderColor: "$primary" }}
                      pressStyle={{ scale: 0.98 }}
                      minHeight={200}
                      ai="center"
                      jc="center"
                    >
                      {imageLoading ? (
                        <YStack ai="center" gap="$2">
                          <Spinner size="large" color="$primary" />
                          <Text color="$gray10">Uploading image...</Text>
                        </YStack>
                      ) : product.image ? (
                        <YStack ai="center" gap="$3" width="100%">
                          <Image
                            source={{ uri: product.image }}
                            style={{
                              width: 150,
                              height: 150,
                              borderRadius: 12,
                            }}
                            resizeMode="cover"
                          />
                          <Text color="$primary" fontSize="$4" fontWeight="600">
                            Tap to change image
                          </Text>
                          <Button
                            size="$3"
                            bg="$primary"
                            color="white"
                            mt="$2"
                            onPress={() => {
                              console.log('🔥 Button onPress triggered');
                              showImagePicker();
                            }}
                          >
                            Change Photo
                          </Button>
                        </YStack>
                      ) : (
                        <YStack ai="center" gap="$3">
                          <Ionicons name="cloud-upload-outline" size={48} color="#9CA3AF" />
                          <Text color="$gray10" fontSize="$4" textAlign="center">
                            Tap to upload product image
                          </Text>
                          <Text color="$gray8" fontSize="$3" textAlign="center">
                            Recommended: Square image, at least 400x400px
                          </Text>
                        </YStack>
                      )}
                    </Card>
                  </Pressable>
                  {errors.image && (
                    <Text color="$red10" fontSize="$3" mt="$1">
                      {errors.image}
                    </Text>
                  )}
                </YStack>

                {/* Description Section */}
                <YStack gap="$3">
                  <XStack ai="center" gap="$2">
                    <View
                      style={{
                        backgroundColor: '#f3e8ff',
                        borderRadius: 8,
                        padding: 6,
                      }}
                    >
                      <Ionicons name="document-text-outline" size={18} color="#9333ea" />
                    </View>
                    <H4 color="$gray12" fontWeight="700">Description</H4>
                  </XStack>
                  <Input
                    placeholder="Enter product description (optional)..."
                    value={product.description}
                    onChangeText={(text) => setProduct(prev => ({ ...prev, description: text }))}
                    size="$5"
                    br="$6"
                    borderColor="$gray6"
                    focusStyle={{ borderColor: "$primary" }}
                    bg="$gray1"
                    multiline
                    numberOfLines={3}
                  />
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Action Buttons */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 500, duration: 400 }}
          >
            <XStack gap="$4" jc="center">
              <Button
                flex={1}
                size="$5"
                br="$6"
                bg="$gray6"
                color="$gray12"
                fontWeight="600"
                onPress={() => router.back()}
                disabled={isLoading}
                hoverStyle={{ bg: "$gray7" }}
                pressStyle={{ bg: "$gray8" }}
              >
                Cancel
              </Button>
              <Button
                flex={2}
                size="$5"
                br="$6"
                bg="$primary"
                color="white"
                fontWeight="700"
                onPress={handleSubmit}
                disabled={isLoading}
                icon={isLoading ? <Spinner size="small" color="white" /> : <Ionicons name="checkmark" size={20} color="white" />}
                hoverStyle={{ bg: "$primary" }}
                pressStyle={{ bg: "$primary" }}
              >
                {isLoading ? 'Updating...' : 'Update Product'}
              </Button>
            </XStack>
          </MotiView>
        </YStack>
      </ScrollView>
    </LinearGradient>
  );
}
