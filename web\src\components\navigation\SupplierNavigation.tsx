import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, BarChart3, Bell, Package, User, LogOut, Users, Menu, X, Crown, Shield } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import Logo from '../common/Logo';
import { motion, AnimatePresence } from 'framer-motion';

const SupplierNavigation: React.FC = () => {
  const location = useLocation();
  const { logout, user } = useAuth();
  const { t } = useTranslation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Define navigation items based on user role
  const getNavItems = () => {
    const baseItems = [
      { path: '/supplier/home', icon: Home, label: t('navigation.home') },
      { path: '/supplier/notifications', icon: Bell, label: t('navigation.notifications') },
    ];

    // Only supplier admins can access these features
    if (user?.role === 'supplier-admin') {
      return [
        ...baseItems,
        { path: '/supplier/analytics', icon: BarChart3, label: t('navigation.analytics') },
        { path: '/supplier/products', icon: Package, label: t('navigation.products') },
        { path: '/supplier/employees', icon: Users, label: 'Employees' },
        { path: '/supplier/profile', icon: User, label: t('navigation.profile') },
      ];
    }

    // Supplier employees only get basic access
    return baseItems;
  };

  const navItems = getNavItems();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const handleLogout = async () => {
    await logout();
    setIsMobileMenuOpen(false);
  };

  // Get role display info
  const getRoleInfo = () => {
    if (user?.role === 'supplier-admin') {
      return {
        icon: Crown,
        label: 'Admin',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100'
      };
    }
    return {
      icon: Shield,
      label: 'Employee',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    };
  };

  const roleInfo = getRoleInfo();

  return (
    <>
      {/* Main Navigation Header */}
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50'
            : 'bg-white shadow-md'
        }`}
      >
        <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
          <div className="flex justify-between items-center h-14 sm:h-16 lg:h-18">

            {/* Logo Section */}
            <motion.div
              className="flex items-center flex-shrink-0"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link to="/supplier/home" className="flex items-center">
                <Logo size="sm" showText={false} />
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1 xl:space-x-2">
              {navItems.map((item, index) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;

                return (
                  <motion.div
                    key={item.path}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      to={item.path}
                      className={`relative flex items-center space-x-2 px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 group ${
                        isActive
                          ? 'text-primary-600 bg-primary-50 shadow-sm'
                          : 'text-gray-600 hover:text-primary-600 hover:bg-gray-50'
                      }`}
                    >
                      <Icon size={18} className={`transition-transform duration-200 ${isActive ? 'scale-110' : 'group-hover:scale-110'}`} />
                      <span className="whitespace-nowrap">{item.label}</span>
                      {isActive && (
                        <motion.div
                          layoutId="activeSupplierTab"
                          className="absolute inset-0 bg-primary-100 rounded-xl -z-10"
                          initial={false}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                    </Link>
                  </motion.div>
                );
              })}
            </div>

            {/* Desktop User Menu & Logout */}
            <div className="hidden lg:flex items-center space-x-3">
              {/* User Info with Role Badge */}
              <div className="hidden xl:flex items-center space-x-3 px-3 py-2 bg-gray-50 rounded-xl">
                <div className="relative">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <User size={16} className="text-primary-600" />
                  </div>
                  <div className={`absolute -top-1 -right-1 w-4 h-4 ${roleInfo.bgColor} rounded-full flex items-center justify-center`}>
                    <roleInfo.icon size={10} className={roleInfo.color} />
                  </div>
                </div>
                <div className="text-sm">
                  <div className="flex items-center space-x-2">
                    <p className="font-medium text-gray-900">{user?.firstName || 'Supplier'}</p>
                    <span className={`px-2 py-0.5 text-xs font-medium ${roleInfo.color} ${roleInfo.bgColor} rounded-full`}>
                      {roleInfo.label}
                    </span>
                  </div>
                  <p className="text-gray-500 text-xs">{user?.email}</p>
                </div>
              </div>

              {/* Logout Button */}
              <motion.button
                onClick={handleLogout}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium text-gray-600 hover:text-red-600 hover:bg-red-50 transition-all duration-200 border border-gray-200 hover:border-red-200"
              >
                <LogOut size={18} />
                <span className="hidden xl:inline">{t('auth.logout')}</span>
              </motion.button>
            </div>

            {/* Mobile Menu Button */}
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="lg:hidden flex items-center justify-center w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
              aria-label="Toggle mobile menu"
            >
              <AnimatePresence mode="wait">
                {isMobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X size={20} className="text-gray-600" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu size={20} className="text-gray-600" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Mobile Menu Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 lg:hidden overflow-y-auto"
            >
              {/* Mobile Menu Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <User size={20} className="text-primary-600" />
                    </div>
                    <div className={`absolute -top-1 -right-1 w-5 h-5 ${roleInfo.bgColor} rounded-full flex items-center justify-center`}>
                      <roleInfo.icon size={12} className={roleInfo.color} />
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="font-semibold text-gray-900">{user?.firstName || 'Supplier'}</p>
                      <span className={`px-2 py-0.5 text-xs font-medium ${roleInfo.color} ${roleInfo.bgColor} rounded-full`}>
                        {roleInfo.label}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500">{user?.email}</p>
                  </div>
                </div>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <X size={20} className="text-gray-500" />
                </button>
              </div>

              {/* Mobile Navigation Items */}
              <div className="py-4">
                {navItems.map((item, index) => {
                  const Icon = item.icon;
                  const isActive = location.pathname === item.path;

                  return (
                    <motion.div
                      key={item.path}
                      initial={{ opacity: 0, x: 50 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Link
                        to={item.path}
                        className={`flex items-center space-x-4 px-6 py-4 text-base font-medium transition-all duration-200 ${
                          isActive
                            ? 'text-primary-600 bg-primary-50 border-r-4 border-primary-600'
                            : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <Icon size={24} className={`${isActive ? 'text-primary-600' : 'text-gray-400'}`} />
                        <span>{item.label}</span>
                        {isActive && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="ml-auto w-2 h-2 bg-primary-600 rounded-full"
                          />
                        )}
                      </Link>
                    </motion.div>
                  );
                })}
              </div>

              {/* Mobile Menu Footer */}
              <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
                <motion.button
                  onClick={handleLogout}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full flex items-center justify-center space-x-3 px-4 py-3 bg-red-50 hover:bg-red-100 text-red-600 rounded-xl font-medium transition-all duration-200 border border-red-200"
                >
                  <LogOut size={20} />
                  <span>{t('auth.logout')}</span>
                </motion.button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default SupplierNavigation;
