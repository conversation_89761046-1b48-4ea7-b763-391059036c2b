import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Settings,
  Save,
  ArrowLeft,
  Plus,
  Trash2,
  Edit3,
  Package,
  Utensils,
  Shirt,
  Palette,
  Hash,
  Type,
  ToggleLeft,
  ToggleRight,
  Image as ImageIcon,
  CheckCircle2,
  AlertCircle,
  Zap,
  Sparkles,
  Crown,
  Award,
  Target,
  ChevronRight,
  DollarSign,
  Tag,
  Layers,
  Coffee,
  ShoppingBag,
  Star,
  TrendingUp
} from 'lucide-react';
import { useSupplierProductsStore } from '../../../stores/supplierProductsStore';
import Input from '../../../components/common/Input';
import { LanguageIndicator } from '../../../components/common/LanguageIndicator';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';
import { detectErrorType, getErrorMessage, handleFetchError } from '../../../utils/errorHandler';
import { EnhancedErrorDisplay } from '../../../components/common/EnhancedErrorDisplay';
import type { ErrorInfo } from '../../../utils/errorHandler';

// Modern Glass Card Component
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Floating Orb Component
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06,
      zIndex: -1,
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Enhanced Option Card Component
const OptionCard: React.FC<{
  children: React.ReactNode;
  type: 'restaurant' | 'clothing' | 'custom';
  className?: string;
}> = ({ children, type, className = '' }) => {
  const getGradient = () => {
    switch (type) {
      case 'restaurant': return 'from-orange-500/20 to-red-500/20';
      case 'clothing': return 'from-blue-500/20 to-purple-500/20';
      case 'custom': return 'from-emerald-500/20 to-green-500/20';
      default: return 'from-white/10 to-white/5';
    }
  };

  const getBorderColor = () => {
    switch (type) {
      case 'restaurant': return 'border-orange-400/30';
      case 'clothing': return 'border-blue-400/30';
      case 'custom': return 'border-emerald-400/30';
      default: return 'border-white/20';
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className={`relative bg-gradient-to-br ${getGradient()} border ${getBorderColor()} rounded-2xl p-4 shadow-lg overflow-hidden ${className}`}
    >
      {/* Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
};

const ManageOptions: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { getProduct, updateProduct, setSupplierId, loadProducts } = useSupplierProductsStore();
  const { user } = useCurrentUserData();

  // get supplier category
  const supplierCategory = user?.businessType;
  
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [newValueInputs, setNewValueInputs] = useState<{[key: number]: {name: string; price: number}}>({});
  const [valueErrors, setValueErrors] = useState<{[key: number]: string}>({});

  // Enhanced error handling state
  const [loadError, setLoadError] = useState<ErrorInfo | null>(null);
  const [saveError, setSaveError] = useState<ErrorInfo | null>(null);
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false);
  const [retryAttempts, setRetryAttempts] = useState(0);

  useEffect(() => {
    const loadProductData = async () => {
      // Clear previous errors
      setLoadError(null);

      if (!user?.id || !productId || !user?.supplierId) {
        setLoading(false);
        if (!user?.supplierId) {
          setLoadError({
            type: 'auth',
            message: 'User account is not properly linked to a supplier. Please contact support.',
            userFriendlyMessage: 'Your account is not linked to a supplier. Please contact support.',
            statusCode: 403
          });
        }
        return;
      }

      // Set supplier ID (use supplierId from user, not user.id)
      if (user.supplierId) {
        setSupplierId(user.supplierId);
      } else {
        console.error('❌ User does not have a supplierId:', user);
        setLoadError({
          type: 'auth',
          message: 'User account is not properly linked to a supplier. Please contact support.',
          userFriendlyMessage: 'Your account is not linked to a supplier. Please contact support.',
          statusCode: 403
        });
        setLoading(false);
        return;
      }

      try {
        // First try to get product from current store
        let foundProduct = getProduct(productId);

        if (!foundProduct) {
          // If not found, load products from backend
          await loadProducts();
          foundProduct = getProduct(productId);
        }

        if (!foundProduct) {
          // If still not found, try localStorage as fallback
          try {
            const stored = localStorage.getItem('supplier-products');
            if (stored) {
              const parsedData = JSON.parse(stored);
              foundProduct = parsedData.products?.find((p: any) => p.id === productId);
            }
          } catch (error) {
            console.error('Failed to load product from localStorage:', error);
          }
        }

        if (foundProduct) {
          // Clean up any negative prices that might exist in the data
          const cleanedProduct = {
            ...foundProduct,
            restaurantOptions: foundProduct.restaurantOptions ? {
              ...foundProduct.restaurantOptions,
              additions: foundProduct.restaurantOptions.additions?.map((addition: any) => ({
                ...addition,
                price: Math.max(0, Number(addition.price) || 0)
              })),
              sides: foundProduct.restaurantOptions.sides?.map((side: any) => ({
                ...side,
                price: Math.max(0, Number(side.price) || 0)
              }))
            } : {},
            customOptions: foundProduct.customOptions?.map((option: any) => ({
              ...option,
              values: option.values?.map((value: any) => ({
                ...value,
                price: Math.max(0, Number(value.price) || 0)
              }))
            }))
          };
          setProduct(cleanedProduct);
        } else {
          // Product not found
          setLoadError({
            type: 'notFound',
            message: 'Product not found',
            userFriendlyMessage: 'The product you\'re looking for doesn\'t exist or has been removed.',
            statusCode: 404
          });
        }
      } catch (error: any) {
        console.error('Failed to load product:', error);
        const errorInfo = handleFetchError(error, t);
        setLoadError(errorInfo);
      } finally {
        setLoading(false);
      }
    };

    loadProductData();
  }, [productId, getProduct, loadProducts, setSupplierId, user?.supplierId, t]);



  const validateProductOptions = () => {
    const errors: string[] = [];

    if (!product) return errors;

    // Validate restaurant options
    if (product.restaurantOptions) {
      // Check additions
      if (product.restaurantOptions.additions) {
        product.restaurantOptions.additions.forEach((addition: any) => {
          if (addition.price < 0) {
            errors.push(`Addition "${addition.name}" has negative price (${addition.price})`);
          }
          if (addition.price > 1000) {
            errors.push(`Addition "${addition.name}" price is too high (${addition.price}). Maximum 1000 allowed.`);
          }
        });
      }

      // Check sides
      if (product.restaurantOptions.sides) {
        product.restaurantOptions.sides.forEach((side: any) => {
          if (side.price < 0) {
            errors.push(`Side "${side.name}" has negative price (${side.price})`);
          }
          if (side.price > 1000) {
            errors.push(`Side "${side.name}" price is too high (${side.price}). Maximum 1000 allowed.`);
          }
        });
      }
    }

    // Validate custom options
    if (product.customOptions) {
      product.customOptions.forEach((option: any) => {
        if (option.values) {
          option.values.forEach((value: any) => {
            if (value.price < 0) {
              errors.push(`Custom option "${option.title}" value "${value.name}" has negative price (${value.price})`);
            }
            if (value.price > 1000) {
              errors.push(`Custom option "${option.title}" value "${value.name}" price is too high (${value.price}). Maximum 1000 allowed.`);
            }
          });
        }
      });
    }

    return errors;
  };

  const handleSave = async () => {
    if (!product || !productId) {
      setSaveError({
        type: 'validation',
        message: 'No product data to save',
        userFriendlyMessage: 'No product data available. Please refresh the page and try again.',
        statusCode: 400
      });
      return;
    }

    try {
      setLoading(true);
      setSaveError(null);
      setSaveSuccess(false);

      // Validate options before saving
      const validationErrors = validateProductOptions();
      if (validationErrors.length > 0) {
        setSaveError({
          type: 'validation',
          message: validationErrors.join('. '),
          userFriendlyMessage: 'Please fix the validation errors below.',
          statusCode: 400,
          validationErrors: validationErrors.map(error => ({
            field: 'options',
            message: error
          }))
        });
        setLoading(false);
        return;
      }

      // Debug: Log the product data being sent
      console.log('🔍 Saving product with data:', {
        restaurantOptions: product.restaurantOptions,
        customOptions: product.customOptions
      });

      // Final cleanup: Ensure no negative prices in the data being sent
      const cleanedProduct = {
        ...product,
        restaurantOptions: product.restaurantOptions ? {
          ...product.restaurantOptions,
          additions: product.restaurantOptions.additions?.map((addition: any) => ({
            ...addition,
            price: Math.max(0, Number(addition.price) || 0)
          })),
          sides: product.restaurantOptions.sides?.map((side: any) => ({
            ...side,
            price: Math.max(0, Number(side.price) || 0)
          }))
        } : {},
        customOptions: product.customOptions?.map((option: any) => ({
          ...option,
          values: option.values?.map((value: any) => ({
            ...value,
            price: Math.max(0, Number(value.price) || 0)
          }))
        }))
      };

      console.log('🔍 Final cleaned product data:', {
        restaurantOptions: cleanedProduct.restaurantOptions,
        customOptions: cleanedProduct.customOptions
      });

      await updateProduct(productId, cleanedProduct);
      setSaveSuccess(true);

      // Show success message briefly, then navigate
      setTimeout(() => {
        navigate('/supplier/products');
      }, 1500);
    } catch (error: any) {
      console.error('Failed to save product options:', error);

      // Enhanced error handling with backend message extraction
      let errorInfo: ErrorInfo;

      if (error.response?.data) {
        const response = error.response.data;
        const statusCode = error.response.status;
        const errorType = detectErrorType(error, statusCode);

        // Extract backend message
        let backendMessage = '';
        if (response.message) {
          backendMessage = response.message;
        } else if (response.error) {
          backendMessage = typeof response.error === 'string' ? response.error : response.error.message;
        }

        // Extract validation errors
        let validationErrors: Array<{ field: string; message: string }> | undefined;
        if (response.errors && Array.isArray(response.errors)) {
          validationErrors = response.errors.map((err: any) => ({
            field: err.field || err.param || 'unknown',
            message: err.message || err.msg || 'Validation error'
          }));
        }

        errorInfo = {
          type: errorType,
          message: backendMessage || 'Failed to save product options',
          userFriendlyMessage: getErrorMessage(errorType, t),
          statusCode,
          backendMessage,
          validationErrors
        };
      } else {
        errorInfo = handleFetchError(error, t);
      }

      setSaveError(errorInfo);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/supplier/products');
  };

  // Retry functions
  const retryLoadProduct = () => {
    setRetryAttempts(prev => prev + 1);
    setLoadError(null);
    setLoading(true);
    // Trigger useEffect to reload
    window.location.reload();
  };

  const retrySaveProduct = () => {
    setSaveError(null);
    handleSave();
  };

  // Clear errors when user makes changes
  const clearSaveError = () => {
    if (saveError) {
      setSaveError(null);
    }
  };

  const addRestaurantOption = (type: 'additions' | 'without' | 'sides') => {
    if (!product) return;

    if (type === 'without') {
      // For 'without' options, just add an empty string
      setProduct({
        ...product,
        restaurantOptions: {
          ...product.restaurantOptions,
          [type]: [...(product.restaurantOptions?.[type] || []), '']
        }
      });
    } else {
      // For 'additions' and 'sides', add an object with id, name, and price
      const newOption = {
        id: Date.now().toString(),
        name: '',
        price: 0
      };

      setProduct({
        ...product,
        restaurantOptions: {
          ...product.restaurantOptions,
          [type]: [...(product.restaurantOptions?.[type] || []), newOption]
        }
      });
    }
  };

  const updateRestaurantOption = (type: 'additions' | 'without' | 'sides', index: number, field: string, value: any) => {
    if (!product) return;

    const updatedOptions = [...(product.restaurantOptions?.[type] || [])];

    if (type === 'without') {
      // For 'without' options, directly update the string value
      updatedOptions[index] = value;
    } else {
      // For 'additions' and 'sides', update the object field
      let processedValue = value;

      // Ensure price is never negative
      if (field === 'price') {
        processedValue = Math.max(0, Number(value) || 0);
        console.log(`🔧 Setting ${type} ${field} to:`, processedValue, 'from:', value);
      }

      updatedOptions[index] = { ...updatedOptions[index], [field]: processedValue };
    }

    setProduct({
      ...product,
      restaurantOptions: {
        ...product.restaurantOptions,
        [type]: updatedOptions
      }
    });

    // Clear save errors when user makes changes
    clearSaveError();
  };

  const removeRestaurantOption = (type: 'additions' | 'without' | 'sides', index: number) => {
    if (!product) return;
    
    const updatedOptions = (product.restaurantOptions?.[type] || []).filter((_: any, i: number) => i !== index);
    
    setProduct({
      ...product,
      restaurantOptions: {
        ...product.restaurantOptions,
        [type]: updatedOptions
      }
    });
  };

  const addClothingOption = (type: 'sizes' | 'colors' | 'gallery') => {
    if (!product) return;
    
    const newOption = type === 'gallery' ? '' : '';
    
    setProduct({
      ...product,
      clothingOptions: {
        ...product.clothingOptions,
        [type]: [...(product.clothingOptions?.[type] || []), newOption]
      }
    });
  };

  const updateClothingOption = (type: 'sizes' | 'colors' | 'gallery', index: number, value: string) => {
    if (!product) return;
    
    const updatedOptions = [...(product.clothingOptions?.[type] || [])];
    updatedOptions[index] = value;
    
    setProduct({
      ...product,
      clothingOptions: {
        ...product.clothingOptions,
        [type]: updatedOptions
      }
    });
  };

  const removeClothingOption = (type: 'sizes' | 'colors' | 'gallery', index: number) => {
    if (!product) return;
    
    const updatedOptions = (product.clothingOptions?.[type] || []).filter((_: any, i: number) => i !== index);
    
    setProduct({
      ...product,
      clothingOptions: {
        ...product.clothingOptions,
        [type]: updatedOptions
      }
    });
  };

  const addCustomOption = () => {
    if (!product) return;

    const newOption = {
      id: Date.now().toString(),
      title: '',
      type: 'text' as const,
      values: [] as Array<{
        id: string;
        name: string;
        price?: number;
      }>
    };

    setProduct({
      ...product,
      customOptions: [...(product.customOptions || []), newOption]
    });
  };

  const updateCustomOption = (index: number, field: string, value: any) => {
    if (!product) return;

    const updatedOptions = [...(product.customOptions || [])];
    updatedOptions[index] = { ...updatedOptions[index], [field]: value };

    setProduct({
      ...product,
      customOptions: updatedOptions
    });

    // Clear save errors when user makes changes
    clearSaveError();
  };

  const addCustomOptionValue = (optionIndex: number, name: string, price: number = 0) => {
    if (!product || !name.trim()) return;

    const updatedOptions = [...(product.customOptions || [])];
    const currentValues = updatedOptions[optionIndex].values || [];
    const trimmedName = name.trim();

    // Clear any previous errors
    setValueErrors(prev => ({ ...prev, [optionIndex]: '' }));

    // Check if value already exists (case-insensitive)
    if (currentValues.some((v: any) => v.name?.toLowerCase() === trimmedName.toLowerCase())) {
      setValueErrors(prev => ({ ...prev, [optionIndex]: `"${trimmedName}" already exists` }));
      return; // Don't add duplicate values
    }

    // Limit to maximum 50 values per option
    if (currentValues.length >= 50) {
      setValueErrors(prev => ({ ...prev, [optionIndex]: 'Maximum 50 values allowed per option' }));
      return;
    }

    const newValue = {
      id: Date.now().toString() + Math.random(),
      name: trimmedName,
      price: Math.max(0, price)
    };

    updatedOptions[optionIndex] = {
      ...updatedOptions[optionIndex],
      values: [...currentValues, newValue]
    };

    setProduct({
      ...product,
      customOptions: updatedOptions
    });
  };

  const updateCustomOptionValue = (optionIndex: number, valueIndex: number, field: string, value: any) => {
    if (!product) return;

    const updatedOptions = [...(product.customOptions || [])];
    const currentValues = [...(updatedOptions[optionIndex].values || [])];

    currentValues[valueIndex] = {
      ...currentValues[valueIndex],
      [field]: field === 'price' ? Math.max(0, Number(value) || 0) : value
    };

    updatedOptions[optionIndex] = {
      ...updatedOptions[optionIndex],
      values: currentValues
    };

    setProduct({
      ...product,
      customOptions: updatedOptions
    });

    // Clear save errors when user makes changes
    clearSaveError();
  };

  const removeCustomOptionValue = (optionIndex: number, valueIndex: number) => {
    if (!product) return;

    const updatedOptions = [...(product.customOptions || [])];
    const currentValues = [...(updatedOptions[optionIndex].values || [])];
    currentValues.splice(valueIndex, 1);

    updatedOptions[optionIndex] = {
      ...updatedOptions[optionIndex],
      values: currentValues
    };

    setProduct({
      ...product,
      customOptions: updatedOptions
    });
  };

  const removeCustomOption = (index: number) => {
    if (!product) return;

    const updatedOptions = (product.customOptions || []).filter((_: any, i: number) => i !== index);

    setProduct({
      ...product,
      customOptions: updatedOptions
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full mx-auto mb-6"
            />
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-white/80 text-lg font-semibold"
            >
              Loading product options...
            </motion.p>
            {retryAttempts > 0 && (
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-white/60 text-sm mt-2"
              >
                Retry attempt {retryAttempts}...
              </motion.p>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show load error if there's an error loading the product
  if (loadError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
        <div className="flex items-center justify-center min-h-screen p-4">
          <div className="max-w-md w-full">
            <EnhancedErrorDisplay
              error={loadError}
              onRetry={retryLoadProduct}
              variant="floating"
              showBackendDetails={true}
              showValidationErrors={true}
            />
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <AlertCircle size={40} className="text-red-400" />
            </motion.div>
            <h2 className="text-2xl font-bold text-white mb-4">Product Not Found</h2>
            <p className="text-white/60 mb-6">The product you're looking for doesn't exist.</p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleBack}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-2xl font-semibold shadow-lg"
            >
              Back to Products
            </motion.button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
      `}</style>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="w-full space-y-8">

            {/* Enhanced Header */}
            <GlassCard gradient="from-white/25 to-white/15" className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-6">
                  {/* Enhanced Product Icon */}
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                    className="relative"
                  >
                    <motion.div
                      className="relative p-4 bg-white/25 border border-white/40 rounded-3xl"
                      whileHover={{ scale: 1.05, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                    >
                      {supplierCategory === 'restaurant' ? (
                        <Utensils size={32} className="text-white" />
                      ) : supplierCategory === 'clothing' ? (
                        <Shirt size={32} className="text-white" />
                      ) : (
                        <Package size={32} className="text-white" />
                      )}

                      {/* Enhanced Glow Effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-400/40 to-blue-400/40 rounded-3xl blur-2xl"
                        animate={{ opacity: [0.3, 0.6, 0.3] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </motion.div>
                  </motion.div>

                  {/* Enhanced Title */}
                  <div>
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-white text-3xl font-black mb-2 bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent"
                    >
                      {t('productOptions.manageOptions') || 'Manage Options'}
                    </motion.h1>
                    <motion.p
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-white/80 text-lg font-medium"
                    >
                      {t('productOptions.manageProductOptions') || 'Configure options for'} {product.name}
                    </motion.p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <LanguageIndicator />
                  
                  <motion.button
                    whileHover={{ scale: 1.05, x: -3 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleBack}
                    className="flex items-center gap-2 px-4 py-3 bg-white/15 hover:bg-white/25 text-white rounded-2xl border border-white/30 transition-all font-semibold"
                  >
                    <ArrowLeft size={18} />
                    {t('back') || 'Back'}
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleSave}
                    className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-2xl border border-white/30 transition-all font-bold shadow-lg"
                  >
                    <Save size={18} />
                    {t('save') || 'Save Changes'}
                  </motion.button>
                </div>
              </div>
            </GlassCard>

            {/* Product Info */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <motion.div
                  className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: -10 }}
                >
                  <Package size={28} className="text-white" />
                </motion.div>
                <h2 className="text-white text-2xl font-black bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  {t('productInfo') || 'Product Information'}
                </h2>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-white/90 mb-3">
                    {t('productName') || 'Product Name'}
                  </label>
                  <Input
                    value={product.name || ''}
                    onChange={(e) => {
                      setProduct({ ...product, name: e.target.value });
                      clearSaveError();
                    }}
                    placeholder={t('enterProductName') || 'Enter product name'}
                    className="w-full bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-white/90 mb-3">
                    {t('price') || 'Price'}
                  </label>
                  <Input
                    type="number"
                    value={product.price === 0 ? '' : product.price || ''}
                    onChange={(e) => {
                      setProduct({ ...product, price: parseFloat(e.target.value) || 0 });
                      clearSaveError();
                    }}
                    onFocus={(e) => e.target.select()}
                    placeholder="0.00"
                    className="w-full bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
            </GlassCard>

            {/* Restaurant Options */}
            {supplierCategory === 'restaurant' && (
              <GlassCard gradient="from-orange-500/20 to-red-500/20" className="p-8">
                <div className="flex items-center gap-4 mb-6">
                  <motion.div
                    className="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg"
                    whileHover={{ scale: 1.1, rotate: 10 }}
                  >
                    <Coffee size={28} className="text-white" />
                  </motion.div>
                  <h2 className="text-white text-2xl font-black bg-gradient-to-r from-white to-orange-200 bg-clip-text text-transparent">
                    {t('restaurantOptions') || 'Restaurant Options'}
                  </h2>
                  <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
                </div>
                
                {/* Additions */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white text-lg font-bold flex items-center gap-2">
                      <Plus size={20} className="text-orange-400" />
                      {t('additions') || 'Additions'}
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => addRestaurantOption('additions')}
                      className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-4 py-2 rounded-xl font-semibold shadow-lg"
                    >
                      {t('add') || 'Add'}
                    </motion.button>
                  </div>
                  <div className="space-y-4">
                    {(product.restaurantOptions?.additions || []).map((option: any, index: number) => (
                      <OptionCard key={option.id} type="restaurant">
                        <div className="flex items-center gap-4">
                          <Input
                            value={option.name}
                            onChange={(e) => updateRestaurantOption('additions', index, 'name', e.target.value)}
                            placeholder={t('additionName') || 'Addition name'}
                            className="flex-1 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          />
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={option.price === 0 ? '' : option.price}
                            onChange={(e) => {
                              const value = Math.max(0, parseFloat(e.target.value) || 0);
                              updateRestaurantOption('additions', index, 'price', value);
                            }}
                            onFocus={(e) => e.target.select()}
                            placeholder="0.00"
                            className="w-32 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          />
                          <motion.button
                            whileHover={{ scale: 1.1, rotate: 90 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => removeRestaurantOption('additions', index)}
                            className="p-3 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-xl border border-red-400/30 transition-all"
                          >
                            <Trash2 size={18} />
                          </motion.button>
                        </div>
                      </OptionCard>
                    ))}
                  </div>
                </div>

                {/* Without Options */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white text-lg font-bold flex items-center gap-2">
                      <ToggleLeft size={20} className="text-orange-400" />
                      {t('withoutOptions') || 'Without Options'}
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => addRestaurantOption('without')}
                      className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-4 py-2 rounded-xl font-semibold shadow-lg"
                    >
                      {t('add') || 'Add'}
                    </motion.button>
                  </div>
                  <div className="space-y-4">
                    {(product.restaurantOptions?.without || []).map((option: string, index: number) => (
                      <OptionCard key={index} type="restaurant">
                        <div className="flex items-center gap-4">
                          <Input
                            value={option}
                            onChange={(e) => updateRestaurantOption('without', index, '', e.target.value)}
                            placeholder={t('withoutOption') || 'Without option'}
                            className="flex-1 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          />
                          <motion.button
                            whileHover={{ scale: 1.1, rotate: 90 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => removeRestaurantOption('without', index)}
                            className="p-3 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-xl border border-red-400/30 transition-all"
                          >
                            <Trash2 size={18} />
                          </motion.button>
                        </div>
                      </OptionCard>
                    ))}
                  </div>
                </div>

                {/* Sides */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white text-lg font-bold flex items-center gap-2">
                      <Layers size={20} className="text-orange-400" />
                      {t('sides') || 'Sides'}
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => addRestaurantOption('sides')}
                      className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-4 py-2 rounded-xl font-semibold shadow-lg"
                    >
                      {t('add') || 'Add'}
                    </motion.button>
                  </div>
                  <div className="space-y-4">
                    {(product.restaurantOptions?.sides || []).map((option: any, index: number) => (
                      <OptionCard key={option.id} type="restaurant">
                        <div className="flex items-center gap-4">
                          <Input
                            value={option.name}
                            onChange={(e) => updateRestaurantOption('sides', index, 'name', e.target.value)}
                            placeholder={t('sideName') || 'Side name'}
                            className="flex-1 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          />
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={option.price === 0 ? '' : option.price}
                            onChange={(e) => {
                              const value = Math.max(0, parseFloat(e.target.value) || 0);
                              updateRestaurantOption('sides', index, 'price', value);
                            }}
                            onFocus={(e) => e.target.select()}
                            placeholder="0.00"
                            className="w-32 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          />
                          <motion.button
                            whileHover={{ scale: 1.1, rotate: 90 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => removeRestaurantOption('sides', index)}
                            className="p-3 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-xl border border-red-400/30 transition-all"
                          >
                            <Trash2 size={18} />
                          </motion.button>
                        </div>
                      </OptionCard>
                    ))}
                  </div>
                </div>
              </GlassCard>
            )}

            {/* Clothing Options */}
            {supplierCategory === 'clothing' && (
              <GlassCard gradient="from-blue-500/20 to-purple-500/20" className="p-8">
                <div className="flex items-center gap-4 mb-6">
                  <motion.div
                    className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg"
                    whileHover={{ scale: 1.1, rotate: -10 }}
                  >
                    <Shirt size={28} className="text-white" />
                  </motion.div>
                  <h2 className="text-white text-2xl font-black bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                    {t('clothingOptions') || 'Clothing Options'}
                  </h2>
                  <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
                </div>
                
                {/* Sizes */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white text-lg font-bold flex items-center gap-2">
                      <Hash size={20} className="text-blue-400" />
                      {t('sizes') || 'Sizes'}
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => addClothingOption('sizes')}
                      className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-4 py-2 rounded-xl font-semibold shadow-lg"
                    >
                      {t('add') || 'Add'}
                    </motion.button>
                  </div>
                  <div className="space-y-4">
                    {(product.clothingOptions?.sizes || []).map((size: string, index: number) => (
                      <OptionCard key={index} type="clothing">
                        <div className="flex items-center gap-4">
                          <Input
                            value={size}
                            onChange={(e) => updateClothingOption('sizes', index, e.target.value)}
                            placeholder={t('size') || 'Size'}
                            className="flex-1 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <motion.button
                            whileHover={{ scale: 1.1, rotate: 90 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => removeClothingOption('sizes', index)}
                            className="p-3 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-xl border border-red-400/30 transition-all"
                          >
                            <Trash2 size={18} />
                          </motion.button>
                        </div>
                      </OptionCard>
                    ))}
                  </div>
                </div>

                {/* Colors */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white text-lg font-bold flex items-center gap-2">
                      <Palette size={20} className="text-blue-400" />
                      {t('colors') || 'Colors'}
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => addClothingOption('colors')}
                      className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-4 py-2 rounded-xl font-semibold shadow-lg"
                    >
                      {t('add') || 'Add'}
                    </motion.button>
                  </div>
                  <div className="space-y-4">
                    {(product.clothingOptions?.colors || []).map((color: string, index: number) => (
                      <OptionCard key={index} type="clothing">
                        <div className="flex items-center gap-4">
                          <Input
                            value={color}
                            onChange={(e) => updateClothingOption('colors', index, e.target.value)}
                            placeholder={t('color') || 'Color'}
                            className="flex-1 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <motion.button
                            whileHover={{ scale: 1.1, rotate: 90 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => removeClothingOption('colors', index)}
                            className="p-3 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-xl border border-red-400/30 transition-all"
                          >
                            <Trash2 size={18} />
                          </motion.button>
                        </div>
                      </OptionCard>
                    ))}
                  </div>
                </div>

                {/* Gallery */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white text-lg font-bold flex items-center gap-2">
                      <ImageIcon size={20} className="text-blue-400" />
                      {t('gallery') || 'Gallery'}
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => addClothingOption('gallery')}
                      className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white px-4 py-2 rounded-xl font-semibold shadow-lg"
                    >
                      {t('add') || 'Add'}
                    </motion.button>
                  </div>
                  <div className="space-y-4">
                    {(product.clothingOptions?.gallery || []).map((image: string, index: number) => (
                      <OptionCard key={index} type="clothing">
                        <div className="flex items-center gap-4">
                          <Input
                            value={image}
                            onChange={(e) => updateClothingOption('gallery', index, e.target.value)}
                            placeholder={t('imageUrl') || 'Image URL'}
                            className="flex-1 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <motion.button
                            whileHover={{ scale: 1.1, rotate: 90 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => removeClothingOption('gallery', index)}
                            className="p-3 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-xl border border-red-400/30 transition-all"
                          >
                            <Trash2 size={18} />
                          </motion.button>
                        </div>
                      </OptionCard>
                    ))}
                  </div>
                </div>
              </GlassCard>
            )}

            {/* Custom Options */}
            <GlassCard gradient="from-emerald-500/20 to-green-500/20" className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <motion.div
                  className="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 10 }}
                >
                  <Settings size={28} className="text-white" />
                </motion.div>
                <h2 className="text-white text-2xl font-black bg-gradient-to-r from-white to-emerald-200 bg-clip-text text-transparent">
                  {t('customOptions') || 'Custom Options'}
                </h2>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
              </div>

              <div className="flex items-center justify-between mb-6">
                <p className="text-white/80 text-base">
                  Create custom options for your customers to personalize their orders
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={addCustomOption}
                  className="bg-gradient-to-r from-emerald-500 to-green-500 hover:from-emerald-600 hover:to-green-600 text-white px-6 py-3 rounded-xl font-semibold shadow-lg flex items-center gap-2"
                >
                  <Plus size={18} />
                  {t('addCustomOption') || 'Add Custom Option'}
                </motion.button>
              </div>
              
              <div className="space-y-6">
                {(product.customOptions || []).map((option: any, index: number) => (
                  <OptionCard key={option.id} type="custom" className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-white/90 mb-3">
                          {t('optionTitle') || 'Option Title'}
                        </label>
                        <Input
                          value={option.title}
                          onChange={(e) => updateCustomOption(index, 'title', e.target.value)}
                          placeholder={t('enterOptionTitle') || 'Enter option title'}
                          className="w-full bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-white/90 mb-3">
                          {t('optionType') || 'Option Type'}
                        </label>
                        <div className="relative group">
                          <select
                            value={option.type}
                            onChange={(e) => updateCustomOption(index, 'type', e.target.value)}
                            className="w-full px-4 py-3 bg-white/10 border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-emerald-500 focus:border-transparent outline-none transition-all appearance-none cursor-pointer hover:bg-white/15 hover:border-emerald-400/50 group-hover:border-emerald-400/50"
                          >
                            <option value="text" className="bg-slate-800 text-white py-2">{t('text') || 'Text'}</option>
                            <option value="number" className="bg-slate-800 text-white py-2">{t('number') || 'Number'}</option>
                            <option value="select" className="bg-slate-800 text-white py-2">{t('select') || 'Select'}</option>
                            <option value="multi-select" className="bg-slate-800 text-white py-2">{t('multiSelect') || 'Multi-Select'}</option>
                          </select>
                          
                          {/* Custom Dropdown Arrow */}
                          <motion.div
                            className="absolute right-4 top-1/3 -translate-y-1/3 pointer-events-none"
                            animate={{ rotate: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronRight size={20} className="text-emerald-400 group-hover:text-emerald-300 transition-colors" />
                          </motion.div>
                          
                          {/* Enhanced Focus Ring */}
                          <div className="absolute inset-0 rounded-xl ring-0 ring-emerald-500/0 group-focus-within:ring-2 group-focus-within:ring-emerald-500/50 transition-all duration-200 pointer-events-none" />
                          
                          {/* Hover Glow Effect */}
                          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-emerald-500/0 via-emerald-500/5 to-emerald-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                        </div>
                        
                        {/* Option Type Description */}
                        <div className="mt-2 text-xs text-white/60">
                          {option.type === 'text' && (
                            <span className="flex items-center gap-1">
                              <Type size={12} className="text-emerald-400" />
                              Free text input for customer
                            </span>
                          )}
                          {option.type === 'number' && (
                            <span className="flex items-center gap-1">
                              <Hash size={12} className="text-emerald-400" />
                              Numeric input with validation
                            </span>
                          )}
                          {option.type === 'select' && (
                            <span className="flex items-center gap-1">
                              <ToggleLeft size={12} className="text-emerald-400" />
                              Single choice from predefined options
                            </span>
                          )}
                          {option.type === 'multi-select' && (
                            <span className="flex items-center gap-1">
                              <Layers size={12} className="text-emerald-400" />
                              Multiple choices from predefined options
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-end">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => removeCustomOption(index)}
                          className="w-full p-3 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-xl border border-red-400/30 transition-all font-semibold"
                        >
                          <Trash2 size={18} className="inline mr-2" />
                          {t('remove') || 'Remove'}
                        </motion.button>
                      </div>
                    </div>
                    
                    {(option.type === 'select' || option.type === 'multi-select') && (
                      <div>
                        <div className="flex items-center justify-between mb-3">
                          <label className="block text-sm font-medium text-white/90">
                            {t('optionValues') || 'Option Values'}
                          </label>
                          <span className="text-xs text-white/60">
                            {option.values?.length || 0} values
                          </span>
                        </div>

                        {/* Add New Value Input */}
                        <div className="mb-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                            <Input
                              value={newValueInputs[index]?.name || ''}
                              onChange={(e) => {
                                setNewValueInputs(prev => ({
                                  ...prev,
                                  [index]: {
                                    name: e.target.value,
                                    price: prev[index]?.price || 0
                                  }
                                }));
                                // Clear error when user starts typing
                                if (valueErrors[index]) {
                                  setValueErrors(prev => ({ ...prev, [index]: '' }));
                                }
                              }}
                              placeholder={t('enterValueName') || 'Value name (e.g., Extra Salt)'}
                              className="md:col-span-2 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  const currentInput = newValueInputs[index];
                                  if (currentInput?.name?.trim()) {
                                    addCustomOptionValue(index, currentInput.name, currentInput.price || 0);
                                    setNewValueInputs(prev => ({ ...prev, [index]: { name: '', price: 0 } }));
                                  }
                                }
                              }}
                            />
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              value={newValueInputs[index]?.price === 0 ? '' : newValueInputs[index]?.price || ''}
                              onChange={(e) => {
                                setNewValueInputs(prev => ({
                                  ...prev,
                                  [index]: {
                                    name: prev[index]?.name || '',
                                    price: Number(e.target.value) || 0
                                  }
                                }));
                              }}
                              onFocus={(e) => e.target.select()}
                              placeholder={t('price') || 'Price'}
                              className="bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                            />
                          </div>
                          <div className="flex justify-between items-center mt-2">
                            <div className="text-xs text-white/60">
                              {t('addValueWithPriceHint') || 'Enter value name and optional price, then press Enter or click Add'}
                            </div>
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => {
                                const currentInput = newValueInputs[index];
                                if (currentInput?.name?.trim()) {
                                  addCustomOptionValue(index, currentInput.name, currentInput.price || 0);
                                  setNewValueInputs(prev => ({ ...prev, [index]: { name: '', price: 0 } }));
                                }
                              }}
                              className="px-4 py-2 bg-emerald-500 hover:bg-emerald-600 text-white rounded-xl font-semibold shadow-lg flex items-center gap-2"
                            >
                              <Plus size={16} />
                              {t('add') || 'Add'}
                            </motion.button>
                          </div>
                          {valueErrors[index] && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-xs text-red-400 bg-red-500/10 border border-red-400/30 rounded-lg p-2"
                            >
                              {valueErrors[index]}
                            </motion.div>
                          )}
                        </div>

                        {/* Display Existing Values */}
                        {option.values && option.values.length > 0 && (
                          <div className="space-y-2">
                            <div className="text-xs text-white/70 mb-2">
                              {t('currentValues') || 'Current Values:'}
                            </div>
                            <div className="max-h-60 overflow-y-auto space-y-2">
                              {option.values.map((value: any, valueIndex: number) => (
                                <motion.div
                                  key={value.id || valueIndex}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  exit={{ opacity: 0, x: 20 }}
                                  className="bg-white/5 border border-white/20 rounded-xl p-3"
                                >
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 items-center">
                                    <Input
                                      value={value.name || value}
                                      onChange={(e) => updateCustomOptionValue(index, valueIndex, 'name', e.target.value)}
                                      className="md:col-span-2 bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
                                      placeholder={t('valueName') || 'Value name'}
                                    />
                                    <div className="flex items-center gap-2">
                                      <Input
                                        type="number"
                                        min="0"
                                        step="0.01"
                                        value={value.price === 0 ? '' : value.price || ''}
                                        onChange={(e) => updateCustomOptionValue(index, valueIndex, 'price', e.target.value)}
                                        onFocus={(e) => e.target.select()}
                                        className="bg-white/10 border-white/30 text-white placeholder-white/50 focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
                                        placeholder={t('price') || 'Price'}
                                      />
                                      <motion.button
                                        whileHover={{ scale: 1.1, rotate: 90 }}
                                        whileTap={{ scale: 0.9 }}
                                        onClick={() => removeCustomOptionValue(index, valueIndex)}
                                        className="p-2 bg-red-500/20 hover:bg-red-500/30 text-red-300 rounded-lg border border-red-400/30 transition-all"
                                      >
                                        <Trash2 size={14} />
                                      </motion.button>
                                    </div>
                                  </div>
                                  {value.price > 0 && (
                                    <div className="mt-2 text-xs text-emerald-400">
                                      +${value.price.toFixed(2)} additional cost
                                    </div>
                                  )}
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Empty State */}
                        {(!option.values || option.values.length === 0) && (
                          <div className="text-center py-6 text-white/50">
                            <Layers size={32} className="mx-auto mb-2 opacity-50" />
                            <p className="text-sm">
                              {t('noValuesYet') || 'No values added yet'}
                            </p>
                            <p className="text-xs mt-1">
                              {t('addValuesHint') || 'Add values that customers can choose from'}
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </OptionCard>
                ))}
              </div>
            </GlassCard>

            {/* Save Button Section */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-8">
              {/* Enhanced Error Display */}
              {saveError && (
                <div className="mb-6">
                  <EnhancedErrorDisplay
                    error={saveError}
                    onRetry={retrySaveProduct}
                    onDismiss={() => setSaveError(null)}
                    variant="floating"
                    showBackendDetails={true}
                    showValidationErrors={true}
                  />
                </div>
              )}

              {saveSuccess && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-4 p-4 bg-green-500/10 border border-green-400/30 rounded-xl text-green-400"
                >
                  <div className="flex items-center gap-2">
                    <CheckCircle2 size={20} />
                    <div>
                      <div className="font-semibold">Options saved successfully!</div>
                      <div className="text-sm mt-1">Redirecting to products page...</div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div className="flex justify-end space-x-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleBack}
                  className="px-8 py-4 bg-white/15 hover:bg-white/25 text-white rounded-2xl border border-white/30 transition-all font-semibold text-lg"
                >
                  {t('cancel') || 'Cancel'}
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSave}
                  disabled={loading}
                  className="px-10 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-2xl border border-white/30 transition-all font-bold text-lg shadow-xl flex items-center gap-3"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      {t('saving') || 'Saving...'}
                    </>
                  ) : (
                    <>
                      <Save size={20} />
                      {t('saveChanges') || 'Save Changes'}
                    </>
                  )}
                </motion.button>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </>
  );
};

export default ManageOptions;
