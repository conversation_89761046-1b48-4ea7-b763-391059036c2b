{"name": "boltalab-web", "private": true, "version": "1.0.0", "type": "module", "description": "BolTalab Delivery Application - Web Version", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@types/node": "^24.0.13", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "framer-motion": "^12.23.3", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "leaflet": "^1.9.4", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.1", "react-leaflet": "^5.0.0", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.6.3", "tailwindcss": "^3.4.17", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/leaflet": "^1.9.20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@testing-library/jest-dom": "^6.4.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.1.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}