import { useState, useEffect } from 'react';
import { Di<PERSON><PERSON>, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  YStack,
  XStack,
  Text,
  H2,
  H3,
  H4,
  Card,
  Button,
  Select,
  Progress,
  Separator,
  View,
} from 'tamagui';
import { MotiView } from 'moti';
import { LinearGradient } from 'expo-linear-gradient';
import { useCurrentUserData } from '~/components/useCurrentUserData';
import { useSupplierAnalytics, AnalyticsTimeRange } from './useSupplierAnalytics';
import { suppliersData } from '~/temp-data/suppliersData';

export default function SupplierAnalyticsGUI() {
  const { user } = useCurrentUserData();
  const supplier = suppliersData.find((s) => s.id === user?.supplierId);
  const windowWidth = Dimensions.get('window').width;
  
  const { timeRange, setTimeRange, getAnalyticsData, isLoading, setLoading } = useSupplierAnalytics();
  const [analyticsData, setAnalyticsData] = useState(() => {
    // Safe initialization with fallback
    try {
      return getAnalyticsData(user?.supplierId || '');
    } catch (error) {
      console.error('Error initializing analytics data:', error);
      return {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        completionRate: 0,
        dailyRevenue: [],
        topProducts: [],
        peakHours: [],
        categoryBreakdown: []
      };
    }
  });
  const [selectOpen, setSelectOpen] = useState(false);

  useEffect(() => {
    if (user?.supplierId) {
      setLoading(true);

      // Simulate loading delay for better UX with cleanup
      const timeoutId = setTimeout(() => {
        try {
          setAnalyticsData(getAnalyticsData(user.supplierId || ''));
        } catch (error) {
          console.error('Error loading analytics data:', error);
          // Set default empty data on error
          setAnalyticsData({
            totalRevenue: 0,
            totalOrders: 0,
            averageOrderValue: 0,
            completionRate: 0,
            dailyRevenue: [],
            topProducts: [],
            peakHours: [],
            categoryBreakdown: []
          });
        } finally {
          setLoading(false);
        }
      }, 500);

      // Cleanup function to prevent memory leaks
      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [timeRange, user?.supplierId, getAnalyticsData, setLoading]);

  const formatCurrency = (amount: number) => {
    try {
      if (typeof amount !== 'number' || isNaN(amount)) return '₪0.00';
      return `₪${amount.toFixed(2)}`;
    } catch (error) {
      console.error('Error formatting currency:', error);
      return '₪0.00';
    }
  };

  const formatPercentage = (value: number) => {
    try {
      if (typeof value !== 'number' || isNaN(value)) return '0.0%';
      return `${value.toFixed(1)}%`;
    } catch (error) {
      console.error('Error formatting percentage:', error);
      return '0.0%';
    }
  };

  const timeRangeOptions = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
  ];

  // Show loading state
  if (isLoading) {
    return (
      <View f={1} ai="center" jc="center" p="$6">
        <YStack ai="center" gap="$4">
          <MotiView
            from={{ rotate: '0deg' }}
            animate={{ rotate: '360deg' }}
            transition={{ type: 'timing', duration: 1000, loop: true }}
          >
            <Ionicons name="analytics" size={48} color="#6366f1" />
          </MotiView>
          <YStack ai="center" gap="$2">
            <H3 color="$gray12" fontWeight="700"><Text>Loading Analytics</Text></H3>
            <Text color="$gray9" fontSize="$3" textAlign="center">
              Preparing your business insights...
            </Text>
          </YStack>
        </YStack>
      </View>
    );
  }

  // Show error state if no user data
  if (!user?.supplierId) {
    return (
      <View f={1} ai="center" jc="center" p="$6">
        <YStack ai="center" gap="$4">
          <View
            style={{
              backgroundColor: '#fef2f2',
              borderRadius: 20,
              padding: 16,
            }}
          >
            <Ionicons name="warning-outline" size={48} color="#ef4444" />
          </View>
          <YStack ai="center" gap="$2">
            <H3 color="$gray12" fontWeight="700"><Text>No Data Available</Text></H3>
            <Text color="$gray9" fontSize="$3" textAlign="center">
              Unable to load analytics data. Please check your account settings.
            </Text>
          </YStack>
        </YStack>
      </View>
    );
  }

  return (
    <ScrollView 
      contentContainerStyle={{ padding: 16, paddingBottom: 120, width: windowWidth }} 
      
      showsVerticalScrollIndicator={false}
    >
      <YStack gap="$5">
        {/* Ultra Enhanced Header */}
        <MotiView
          from={{ opacity: 0, translateY: -40, scale: 0.9 }}
          animate={{ opacity: 1, translateY: 0, scale: 1 }}
          transition={{ type: 'spring', damping: 20, stiffness: 300 }}
        >
          <Card
            elevate
            br="$10"
            overflow="hidden"
            borderWidth={0}
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 12 }}
            shadowOpacity={0.4}
            shadowRadius={24}
          >
            <LinearGradient
              colors={["#6366f1", "#8b5cf6", "#a855f7"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{
                padding: 24,
                width: '100%',
                maxWidth: windowWidth - 32, // Account for outer padding
              }}
            >
              {/* Decorative Background Elements */}
              <View
                style={{
                  position: 'absolute',
                  top: -50,
                  right: -50,
                  width: 120,
                  height: 120,
                  borderRadius: 60,
                  backgroundColor: 'rgba(255,255,255,0.1)',
                }}
              />
              <View
                style={{
                  position: 'absolute',
                  bottom: -30,
                  left: -30,
                  width: 80,
                  height: 80,
                  borderRadius: 40,
                  backgroundColor: 'rgba(255,255,255,0.08)',
                }}
              />

              <YStack gap="$5">
                {/* Main Header Content */}
                <XStack ai="center" gap="$3" width="100%">
                  <MotiView
                    from={{ scale: 0, rotate: '-180deg' }}
                    animate={{ scale: 1, rotate: '0deg' }}
                    transition={{ delay: 400, type: 'spring', damping: 15 }}
                  >
                    <LinearGradient
                      colors={['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']}
                      style={{
                        borderRadius: 20,
                        padding: 16,
                        borderWidth: 2,
                        borderColor: 'rgba(255,255,255,0.2)',
                      }}
                    >
                      <Ionicons name="analytics" size={36} color="white" />
                    </LinearGradient>
                  </MotiView>

                  <YStack flex={1} gap="$2">
                    <MotiView
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 600, duration: 600 }}
                    >
                      <H2 color="white" fontWeight="900" fontSize="$8">
                        <Text>Business Analytics</Text>
                      </H2>
                    </MotiView>

                    <MotiView
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 700, duration: 600 }}
                    >
                      <YStack gap="$1">
                        <XStack ai="center" gap="$2">
                          <View
                            style={{
                              backgroundColor: 'rgba(255,255,255,0.2)',
                              borderRadius: 8,
                              paddingHorizontal: 8,
                              paddingVertical: 4,
                            }}
                          >
                            <Text color="white" fontSize="$2" fontWeight="600">
                              LIVE
                            </Text>
                          </View>
                        </XStack>
                        <Text
                          color="white"
                          opacity={0.9}
                          fontSize="$3"
                          fontWeight="500"
                          numberOfLines={2}
                          flexShrink={1}
                        >
                          {supplier?.name || 'Your Business'} Performance Dashboard
                        </Text>
                      </YStack>
                    </MotiView>
                  </YStack>
                </XStack>

                {/* Enhanced Stats Preview */}
                <MotiView
                  from={{ opacity: 0, translateY: 20 }}
                  animate={{ opacity: 1, translateY: 0 }}
                  transition={{ delay: 800, duration: 600 }}
                >
                  <XStack gap="$2" jc="space-between" flexWrap="wrap">
                    <YStack ai="center" gap="$1" flex={1} minWidth={80}>
                      <Text
                        color="white"
                        opacity={0.8}
                        fontSize="$1"
                        fontWeight="500"
                        textAlign="center"
                        numberOfLines={1}
                      >
                        TODAY'S REVENUE
                      </Text>
                      <Text
                        color="white"
                        fontSize="$4"
                        fontWeight="800"
                        numberOfLines={1}
                        adjustsFontSizeToFit
                      >
                        {formatCurrency(analyticsData.dailyRevenue?.length > 0 ? analyticsData.dailyRevenue[analyticsData.dailyRevenue.length - 1]?.revenue || 0 : 0)}
                      </Text>
                    </YStack>

                    <View
                      style={{
                        width: 1,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                        minHeight: 40,
                      }}
                    />

                    <YStack ai="center" gap="$1" flex={1} minWidth={80}>
                      <Text
                        color="white"
                        opacity={0.8}
                        fontSize="$1"
                        fontWeight="500"
                        textAlign="center"
                        numberOfLines={1}
                      >
                        ORDERS TODAY
                      </Text>
                      <Text
                        color="white"
                        fontSize="$4"
                        fontWeight="800"
                        numberOfLines={1}
                      >
                        {analyticsData.dailyRevenue?.length > 0 ? analyticsData.dailyRevenue[analyticsData.dailyRevenue.length - 1]?.orders || 0 : 0}
                      </Text>
                    </YStack>

                    <View
                      style={{
                        width: 1,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                        minHeight: 40,
                      }}
                    />

                    <YStack ai="center" gap="$1" flex={1} minWidth={80}>
                      <Text
                        color="white"
                        opacity={0.8}
                        fontSize="$1"
                        fontWeight="500"
                        textAlign="center"
                        numberOfLines={1}
                      >
                        SUCCESS RATE
                      </Text>
                      <Text
                        color="white"
                        fontSize="$4"
                        fontWeight="800"
                        numberOfLines={1}
                      >
                        {formatPercentage(analyticsData.completionRate)}
                      </Text>
                    </YStack>
                  </XStack>
                </MotiView>

                {/* Enhanced Time Range Selector */}
                <MotiView
                  from={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 900, duration: 500 }}
                >
                  <YStack gap="$3">
                    <YStack gap="$1">
                      <Text color="white" fontSize="$4" fontWeight="700">
                        Analysis Period
                      </Text>
                      <Text color="white" opacity={0.7} fontSize="$2">
                        Select timeframe for insights
                      </Text>
                    </YStack>

                    <Select
                      open={selectOpen}
                      onOpenChange={setSelectOpen}
                      value={timeRange}
                      onValueChange={(value) => setTimeRange(value as AnalyticsTimeRange)}
                    >
                      <Select.Trigger
                        iconAfter={<Ionicons name="chevron-down" size={18} color="white" />}
                        style={{
                          backgroundColor: 'rgba(255,255,255,0.15)',
                          borderColor: 'rgba(255,255,255,0.3)',
                          borderWidth: 2,
                          borderRadius: 16,
                          paddingVertical: 14,
                          paddingHorizontal: 20,
                          width: '100%',
                          shadowColor: 'rgba(0,0,0,0.3)',
                          shadowOffset: { width: 0, height: 4 },
                          shadowOpacity: 0.3,
                          shadowRadius: 8,
                        }}
                      >
                        <Text
                          color="white"
                          fontSize="$3"
                          fontWeight="700"
                          numberOfLines={1}
                          flexShrink={1}
                        >
                          {timeRangeOptions.find(opt => opt.value === timeRange)?.label}
                        </Text>
                      </Select.Trigger>

                      <Select.Content zIndex={100}>
                        <Card
                          bg="white"
                          br="$6"
                          p="$3"
                          elevate
                          shadowColor="$gray8"
                          shadowOffset={{ width: 0, height: 8 }}
                          shadowOpacity={0.15}
                          shadowRadius={16}
                        >
                          {timeRangeOptions.map((option, index) => (
                            <Select.Item
                              key={option.value}
                              index={index}
                              value={option.value}
                              style={{
                                backgroundColor: timeRange === option.value ? "#6366f1" : "transparent",
                                paddingVertical: 14,
                                paddingHorizontal: 18,
                                borderRadius: 12,
                                marginBottom: 6,
                              }}
                            >
                              <Select.ItemText
                                style={{
                                  color: timeRange === option.value ? "white" : "#374151",
                                  fontWeight: timeRange === option.value ? "700" : "500",
                                  fontSize: 16,
                                }}
                              >
                                {option.label}
                              </Select.ItemText>
                              {timeRange === option.value && (
                                <View style={{ position: "absolute", right: 18 }}>
                                  <Ionicons name="checkmark-circle" size={18} color="white" />
                                </View>
                              )}
                            </Select.Item>
                          ))}
                        </Card>
                      </Select.Content>
                    </Select>
                  </YStack>
                </MotiView>
              </YStack>
            </LinearGradient>
          </Card>
        </MotiView>

        {/* Enhanced Key Metrics Cards */}
        <YStack gap="$4">
          <MotiView
            from={{ opacity: 0, translateX: -30 }}
            animate={{ opacity: 1, translateX: 0 }}
            transition={{ delay: 200, duration: 600 }}
          >
            <XStack ai="center" gap="$3">
              <View
                style={{
                  backgroundColor: '#f3f4f6',
                  borderRadius: 12,
                  padding: 8,
                }}
              >
                <Ionicons name="stats-chart" size={24} color="#6366f1" />
              </View>
              <H3 color="$gray12" fontWeight="800" fontSize="$7"><Text>Key Performance Metrics</Text></H3>
            </XStack>
          </MotiView>

          <XStack gap="$4" flexWrap="wrap">
            {/* Total Revenue */}
            <MotiView
              from={{ opacity: 0, translateY: 30, scale: 0.9 }}
              animate={{ opacity: 1, translateY: 0, scale: 1 }}
              transition={{ delay: 300, type: 'spring', damping: 20 }}
              style={{ flex: 1, minWidth: 160 }}
            >
              <Card
                elevate
                p="$5"
                br="$8"
                bg="white"
                borderWidth={0}
                shadowColor="#10b981"
                shadowOffset={{ width: 0, height: 8 }}
                shadowOpacity={0.15}
                shadowRadius={16}
                pressStyle={{ scale: 0.98 }}
              >
                <LinearGradient
                  colors={['#ecfdf5', '#d1fae5']}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 16,
                  }}
                />
                <YStack gap="$3" zi={1}>
                  <XStack ai="center" jc="space-between">
                    <View
                      style={{
                        backgroundColor: '#10b981',
                        borderRadius: 12,
                        padding: 10,
                      }}
                    >
                      <Ionicons name="cash" size={24} color="white" />
                    </View>
                    <View
                      style={{
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderRadius: 8,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                      }}
                    >
                      <Text color="#10b981" fontSize="$2" fontWeight="700">
                        +12.5%
                      </Text>
                    </View>
                  </XStack>

                  <YStack gap="$1">
                    <Text color="#065f46" fontSize="$3" fontWeight="600" opacity={0.8}>
                      Total Revenue
                    </Text>
                    <H2 color="#065f46" fontWeight="900" fontSize="$8">
                      {formatCurrency(analyticsData.totalRevenue)}
                    </H2>
                  </YStack>
                </YStack>
              </Card>
            </MotiView>

            {/* Total Orders */}
            <MotiView
              from={{ opacity: 0, translateY: 30, scale: 0.9 }}
              animate={{ opacity: 1, translateY: 0, scale: 1 }}
              transition={{ delay: 400, type: 'spring', damping: 20 }}
              style={{ flex: 1, minWidth: 160 }}
            >
              <Card
                elevate
                p="$5"
                br="$8"
                bg="white"
                borderWidth={0}
                shadowColor="#3b82f6"
                shadowOffset={{ width: 0, height: 8 }}
                shadowOpacity={0.15}
                shadowRadius={16}
                pressStyle={{ scale: 0.98 }}
              >
                <LinearGradient
                  colors={['#eff6ff', '#dbeafe']}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 16,
                  }}
                />
                <YStack gap="$3" zi={1}>
                  <XStack ai="center" jc="space-between">
                    <View
                      style={{
                        backgroundColor: '#3b82f6',
                        borderRadius: 12,
                        padding: 10,
                      }}
                    >
                      <Ionicons name="receipt" size={24} color="white" />
                    </View>
                    <View
                      style={{
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderRadius: 8,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                      }}
                    >
                      <Text color="#3b82f6" fontSize="$2" fontWeight="700">
                        +8.2%
                      </Text>
                    </View>
                  </XStack>

                  <YStack gap="$1">
                    <Text color="#1e3a8a" fontSize="$3" fontWeight="600" opacity={0.8}>
                      Total Orders
                    </Text>
                    <H2 color="#1e3a8a" fontWeight="900" fontSize="$8">
                      {analyticsData.totalOrders}
                    </H2>
                  </YStack>
                </YStack>
              </Card>
            </MotiView>
          </XStack>

          <XStack gap="$4" flexWrap="wrap">
            {/* Average Order Value */}
            <MotiView
              from={{ opacity: 0, translateY: 30, scale: 0.9 }}
              animate={{ opacity: 1, translateY: 0, scale: 1 }}
              transition={{ delay: 500, type: 'spring', damping: 20 }}
              style={{ flex: 1, minWidth: 160 }}
            >
              <Card
                elevate
                p="$5"
                br="$8"
                bg="white"
                borderWidth={0}
                shadowColor="#f59e0b"
                shadowOffset={{ width: 0, height: 8 }}
                shadowOpacity={0.15}
                shadowRadius={16}
                pressStyle={{ scale: 0.98 }}
              >
                <LinearGradient
                  colors={['#fffbeb', '#fef3c7']}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 16,
                  }}
                />
                <YStack gap="$3" zi={1}>
                  <XStack ai="center" jc="space-between">
                    <View
                      style={{
                        backgroundColor: '#f59e0b',
                        borderRadius: 12,
                        padding: 10,
                      }}
                    >
                      <Ionicons name="trending-up" size={24} color="white" />
                    </View>
                    <View
                      style={{
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        borderRadius: 8,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                      }}
                    >
                      <Text color="#f59e0b" fontSize="$2" fontWeight="700">
                        +5.7%
                      </Text>
                    </View>
                  </XStack>

                  <YStack gap="$1">
                    <Text color="#92400e" fontSize="$3" fontWeight="600" opacity={0.8}>
                      Avg Order Value
                    </Text>
                    <H2 color="#92400e" fontWeight="900" fontSize="$8">
                      {formatCurrency(analyticsData.averageOrderValue)}
                    </H2>
                  </YStack>
                </YStack>
              </Card>
            </MotiView>

            {/* Completion Rate */}
            <MotiView
              from={{ opacity: 0, translateY: 30, scale: 0.9 }}
              animate={{ opacity: 1, translateY: 0, scale: 1 }}
              transition={{ delay: 600, type: 'spring', damping: 20 }}
              style={{ flex: 1, minWidth: 160 }}
            >
              <Card
                elevate
                p="$5"
                br="$8"
                bg="white"
                borderWidth={0}
                shadowColor="#8b5cf6"
                shadowOffset={{ width: 0, height: 8 }}
                shadowOpacity={0.15}
                shadowRadius={16}
                pressStyle={{ scale: 0.98 }}
              >
                <LinearGradient
                  colors={['#faf5ff', '#e9d5ff']}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 16,
                  }}
                />
                <YStack gap="$3" zi={1}>
                  <XStack ai="center" jc="space-between">
                    <View
                      style={{
                        backgroundColor: '#8b5cf6',
                        borderRadius: 12,
                        padding: 10,
                      }}
                    >
                      <Ionicons name="checkmark-circle" size={24} color="white" />
                    </View>
                    <View
                      style={{
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        borderRadius: 8,
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                      }}
                    >
                      <Text color="#8b5cf6" fontSize="$2" fontWeight="700">
                        +2.1%
                      </Text>
                    </View>
                  </XStack>

                  <YStack gap="$1">
                    <Text color="#581c87" fontSize="$3" fontWeight="600" opacity={0.8}>
                      Success Rate
                    </Text>
                    <H2 color="#581c87" fontWeight="900" fontSize="$8">
                      {formatPercentage(analyticsData.completionRate)}
                    </H2>
                  </YStack>
                </YStack>
              </Card>
            </MotiView>
          </XStack>
        </YStack>

        {/* Top Products Section */}
        <MotiView
          from={{ opacity: 0, translateX: -20 }}
          animate={{ opacity: 1, translateX: 0 }}
          transition={{ delay: 400, duration: 500 }}
        >
          <Card
            p="$5"
            br="$8"
            bg="white"
            borderWidth={1}
            borderColor="$gray4"
            shadowColor="$gray8"
            shadowOffset={{ width: 0, height: 4 }}
            shadowOpacity={0.1}
            shadowRadius={8}
          >
            <YStack gap="$4">
              <XStack ai="center" gap="$3">
                <View
                  style={{
                    backgroundColor: '#f3f4f6',
                    borderRadius: 12,
                    padding: 8,
                  }}
                >
                  <Ionicons name="trophy-outline" size={24} color="#7529B3" />
                </View>
                <H3 color="$gray12" fontWeight="700"><Text>Top Performing Products</Text></H3>
              </XStack>

              <YStack gap="$3">
                {analyticsData.topProducts?.length > 0 ? (
                  analyticsData.topProducts.map((product, index) => (
                    <Card
                      key={product.id}
                      p="$3"
                      br="$6"
                      bg="$gray1"
                      borderWidth={1}
                      borderColor="$gray3"
                    >
                      <XStack ai="center" jc="space-between">
                        <XStack ai="center" gap="$3" flex={1}>
                          <View
                            style={{
                              backgroundColor: index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32',
                              borderRadius: 20,
                              width: 32,
                              height: 32,
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <Text color="white" fontSize="$3" fontWeight="bold">
                              {index + 1}
                            </Text>
                          </View>
                          <YStack flex={1}>
                            <Text color="$gray12" fontSize="$4" fontWeight="600" numberOfLines={1}>
                              {product.name}
                            </Text>
                            <Text color="$gray9" fontSize="$3">
                              {product.sales} sold • {product.orders} orders
                            </Text>
                          </YStack>
                        </XStack>
                        <Text color="$green11" fontSize="$4" fontWeight="700">
                          {formatCurrency(product.revenue)}
                        </Text>
                      </XStack>
                    </Card>
                  ))
                ) : (
                  <Card p="$4" br="$6" bg="$gray1">
                    <Text color="$gray9" textAlign="center">
                      No sales data available for this period
                    </Text>
                  </Card>
                )}
              </YStack>
            </YStack>
          </Card>
        </MotiView>

        {/* Category Breakdown */}
        <MotiView
          from={{ opacity: 0, translateX: 20 }}
          animate={{ opacity: 1, translateX: 0 }}
          transition={{ delay: 600, duration: 500 }}
        >
          <Card
            p="$5"
            br="$8"
            bg="white"
            borderWidth={1}
            borderColor="$gray4"
            shadowColor="$gray8"
            shadowOffset={{ width: 0, height: 4 }}
            shadowOpacity={0.1}
            shadowRadius={8}
          >
            <YStack gap="$4">
              <XStack ai="center" gap="$3">
                <View
                  style={{
                    backgroundColor: '#f3f4f6',
                    borderRadius: 12,
                    padding: 8,
                  }}
                >
                  <Ionicons name="pie-chart-outline" size={24} color="#7529B3" />
                </View>
                <H3 color="$gray12" fontWeight="700"><Text>Revenue by Category</Text></H3>
              </XStack>

              <YStack gap="$3">
                {analyticsData.categoryBreakdown?.length > 0 ? (
                  analyticsData.categoryBreakdown.map((category, index) => (
                    <YStack key={category.category} gap="$2">
                      <XStack ai="center" jc="space-between">
                        <Text color="$gray12" fontSize="$4" fontWeight="600">
                          {category.category}
                        </Text>
                        <XStack ai="center" gap="$2">
                          <Text color="$gray9" fontSize="$3">
                            {formatPercentage(category.percentage)}
                          </Text>
                          <Text color="$green11" fontSize="$4" fontWeight="600">
                            {formatCurrency(category.revenue)}
                          </Text>
                        </XStack>
                      </XStack>
                      <Progress
                        value={category.percentage}
                        bg="$gray4"
                        height={8}
                        br="$4"
                      >
                        <Progress.Indicator
                          bg={`hsl(${(index * 60) % 360}, 70%, 50%)`}
                          br="$4"
                          animation="bouncy"
                        />
                      </Progress>
                    </YStack>
                  ))
                ) : (
                  <Card p="$4" br="$6" bg="$gray1">
                    <Text color="$gray9" textAlign="center">
                      No category data available
                    </Text>
                  </Card>
                )}
              </YStack>
            </YStack>
          </Card>
        </MotiView>

        {/* Peak Hours Analysis */}
        <MotiView
          from={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 800, duration: 500 }}
        >
          <Card
            p="$5"
            br="$8"
            bg="white"
            borderWidth={1}
            borderColor="$gray4"
            shadowColor="$gray8"
            shadowOffset={{ width: 0, height: 4 }}
            shadowOpacity={0.1}
            shadowRadius={8}
          >
            <YStack gap="$4">
              <XStack ai="center" gap="$3">
                <View
                  style={{
                    backgroundColor: '#f3f4f6',
                    borderRadius: 12,
                    padding: 8,
                  }}
                >
                  <Ionicons name="time-outline" size={24} color="#7529B3" />
                </View>
                <H3 color="$gray12" fontWeight="700"><Text>Peak Hours</Text></H3>
              </XStack>

              <YStack gap="$3">
                {analyticsData.peakHours?.slice(0, 5).map((hour) => {
                  const orderCounts = analyticsData.peakHours?.map(h => h.orders) || [];
                  const maxOrders = orderCounts.length > 0 ? Math.max(...orderCounts) : 1;
                  const percentage = maxOrders > 0 ? (hour.orders / maxOrders) * 100 : 0;

                  return (
                    <YStack key={hour.hour} gap="$2">
                      <XStack ai="center" jc="space-between">
                        <Text color="$gray12" fontSize="$4" fontWeight="600">
                          {hour.hour}:00 - {hour.hour + 1}:00
                        </Text>
                        <Text color="$blue11" fontSize="$4" fontWeight="600">
                          {hour.orders} orders
                        </Text>
                      </XStack>
                      <Progress
                        value={percentage}
                        bg="$gray4"
                        height={8}
                        br="$4"
                      >
                        <Progress.Indicator
                          bg="$blue8"
                          br="$4"
                          animation="bouncy"
                        />
                      </Progress>
                    </YStack>
                  );
                })}
              </YStack>
            </YStack>
          </Card>
        </MotiView>
      </YStack>
    </ScrollView>
  );
}
