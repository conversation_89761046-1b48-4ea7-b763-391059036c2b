import nodemailer from 'nodemailer';
import { IUser } from '../models/User';

export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

export class EmailService {
  private static transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  static async sendEmail(options: EmailOptions): Promise<void> {
    try {
      const mailOptions = {
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      };

      await this.transporter.sendMail(mailOptions);
      console.log(`Email sent successfully to ${options.to}`);
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }

  static async sendWelcomeEmail(user: IUser): Promise<void> {
    const subject = 'Welcome to BolTalab - Fast As Lightning! ⚡';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; text-align: center;">Welcome to BolTalab! ⚡</h1>
        <p>Hello ${user.firstName || 'there'},</p>
        <p>Thank you for joining BolTalab - Fast As Lightning! We're excited to have you as part of our community.</p>
        <p>Your account has been successfully created with the email: <strong>${user.email}</strong></p>
        
        ${user.role === 'supplier-admin' ? `
          <p>As a supplier, you can now start listing your services and connecting with customers.</p>
        ` : `
          <p>As a customer, you can now browse and book services from our verified suppliers.</p>
        `}
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL}" 
             style="background-color: #007bff; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 5px; display: inline-block;">
            Get Started
          </a>
        </div>
        
        <p>If you have any questions, feel free to contact our support team.</p>
        <p>Best regards,<br>The BolTalab Team ⚡</p>
      </div>
    `;

    await this.sendEmail({
      to: user.email,
      subject,
      html,
    });
  }

  static async sendPasswordResetCode(user: IUser, verificationCode: string): Promise<void> {
    const subject = 'Password Reset Verification Code - BolTalab ⚡';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #67B329; margin-bottom: 10px;">BolTalab ⚡</h1>
          <h2 style="color: #333; margin-top: 0;">Password Reset Verification</h2>
        </div>

        <p style="font-size: 16px; line-height: 1.5;">Hello ${user.firstName || 'there'},</p>
        <p style="font-size: 16px; line-height: 1.5;">You have requested to reset your password for your BolTalab account.</p>
        <p style="font-size: 16px; line-height: 1.5;">Please use the verification code below to proceed with your password reset:</p>

        <div style="text-align: center; margin: 30px 0;">
          <div style="background-color: #f8f9fa; border: 2px solid #67B329; padding: 20px; border-radius: 10px; display: inline-block;">
            <p style="margin: 0; font-size: 14px; color: #666; margin-bottom: 10px;">Your Verification Code:</p>
            <p style="margin: 0; font-size: 32px; font-weight: bold; color: #67B329; letter-spacing: 8px; font-family: 'Courier New', monospace;">
              ${verificationCode}
            </p>
          </div>
        </div>

        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0; font-weight: bold; color: #856404;">⚠️ Important:</p>
          <p style="margin: 5px 0 0 0; color: #856404;">This code will expire in 10 minutes for security reasons.</p>
          <p style="margin: 5px 0 0 0; color: #856404;">Do not share this code with anyone.</p>
        </div>

        <div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0; font-weight: bold; color: #0066cc;">📱 How to use this code:</p>
          <p style="margin: 5px 0 0 0; color: #0066cc;">1. Return to the BolTalab app</p>
          <p style="margin: 5px 0 0 0; color: #0066cc;">2. Enter this 6-digit code when prompted</p>
          <p style="margin: 5px 0 0 0; color: #0066cc;">3. Create your new password</p>
        </div>

        <p style="font-size: 14px; color: #666;">If you didn't request this password reset, please ignore this email. Your account remains secure.</p>

        <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;">
          <p style="color: #666; margin: 0;">Best regards,</p>
          <p style="color: #67B329; font-weight: bold; margin: 5px 0 0 0;">The BolTalab Team ⚡</p>
        </div>
      </div>
    `;

    await this.sendEmail({
      to: user.email,
      subject,
      html,
    });
  }

  static async sendEmailVerification(user: IUser, verificationCode: string): Promise<void> {
    const subject = 'Verify Your Email Address - BolTalab ⚡';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #7529B3; margin: 0;">BolTalab ⚡</h1>
          <p style="color: #666; margin: 5px 0;">Fast As Lightning - Your trusted delivery platform</p>
        </div>

        <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px; text-align: center;">
          <h2 style="color: #333; margin-bottom: 20px;">Verify Your Email Address</h2>
          <p style="color: #666; margin-bottom: 30px;">Hello ${user.firstName || 'there'},</p>
          <p style="color: #666; margin-bottom: 30px;">
            Thank you for signing up for BolTalab - Fast As Lightning! To complete your registration, please use the verification code below:
          </p>

          <div style="background-color: white; padding: 20px; border-radius: 8px; margin: 30px 0; border: 2px dashed #7529B3;">
            <h1 style="color: #7529B3; font-size: 36px; letter-spacing: 8px; margin: 0; font-family: 'Courier New', monospace;">
              ${verificationCode}
            </h1>
          </div>

          <p style="color: #666; font-size: 14px; margin-bottom: 20px;">
            Enter this code in the app to verify your email address.
          </p>

          <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 20px 0;">
            <p style="color: #856404; margin: 0; font-size: 14px;">
              <strong>⏰ This code will expire in 24 hours</strong>
            </p>
          </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background-color: #e7f3ff; border-radius: 8px;">
          <h3 style="color: #0066cc; margin-top: 0;">Need Help?</h3>
          <ul style="color: #666; padding-left: 20px;">
            <li>Make sure you entered the correct email address</li>
            <li>Check your spam/junk folder if you don't see this email</li>
            <li>The verification code is case-sensitive</li>
          </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 12px; margin: 0;">
            If you didn't create this account, please ignore this email.
          </p>
          <p style="color: #999; font-size: 12px; margin: 10px 0 0 0;">
            Best regards,<br>The BolTalab Team ⚡
          </p>
        </div>
      </div>
    `;

    await this.sendEmail({
      to: user.email,
      subject,
      html,
    });
  }

  static async sendPasswordChangeNotification(user: IUser): Promise<void> {
    const subject = 'Password Changed Successfully';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; text-align: center;">Password Changed</h1>
        <p>Hello ${user.firstName || 'there'},</p>
        <p>Your password has been successfully changed for your BolTalab account.</p>
        <p>If you didn't make this change, please contact our support team immediately.</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.FRONTEND_URL}/contact" 
             style="background-color: #007bff; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 5px; display: inline-block;">
            Contact Support
          </a>
        </div>
        
        <p>Best regards,<br>The BolTalab Team ⚡</p>
      </div>
    `;

    await this.sendEmail({
      to: user.email,
      subject,
      html,
    });
  }
}
