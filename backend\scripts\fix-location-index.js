// <PERSON>ript to fix the location geospatial index issue
// Run this script to drop and recreate the location index as sparse

const { MongoClient } = require('mongodb');

async function fixLocationIndex() {
  const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();
    const collection = db.collection('users');

    // List existing indexes
    console.log('Existing indexes:');
    const indexes = await collection.indexes();
    indexes.forEach(index => {
      console.log('- ', index.name, ':', JSON.stringify(index.key));
    });

    // Drop the existing location index if it exists
    try {
      await collection.dropIndex('location_2dsphere');
      console.log('✅ Dropped existing location_2dsphere index');
    } catch (error) {
      console.log('ℹ️  No existing location_2dsphere index to drop');
    }

    // Create new sparse geospatial index
    await collection.createIndex(
      { location: '2dsphere' },
      { 
        sparse: true,
        name: 'location_2dsphere_sparse'
      }
    );
    console.log('✅ Created new sparse location_2dsphere index');

    // Verify the new index
    console.log('\nNew indexes:');
    const newIndexes = await collection.indexes();
    newIndexes.forEach(index => {
      console.log('- ', index.name, ':', JSON.stringify(index.key));
    });

  } catch (error) {
    console.error('❌ Error fixing location index:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
fixLocationIndex().catch(console.error);
