import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { YStack, XStack, Text, Button, Card, Separator } from 'tamagui';
import { AlertTriangle, Info, XCircle, ChevronDown, ChevronUp, AlertCircle } from '@tamagui/lucide-icons';
import { getErrorMessage, UserFriendlyError } from '../../utils/errorHandler';
import { TFunction } from 'i18next';

interface EnhancedErrorDisplayProps {
  error: any;
  t: TFunction;
  onRetry?: () => void;
  onDismiss?: () => void;
  showBackendDetails?: boolean;
  showValidationErrors?: boolean;
  variant?: 'inline' | 'card' | 'alert';
  size?: 'small' | 'medium' | 'large';
}

export const EnhancedErrorDisplay: React.FC<EnhancedErrorDisplayProps> = ({
  error,
  t,
  onRetry,
  onDismiss,
  showBackendDetails = true,
  showValidationErrors = true,
  variant = 'card',
  size = 'medium'
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  if (!error) return null;

  const userFriendlyError = getErrorMessage(error, t);
  const hasBackendDetails = showBackendDetails && userFriendlyError.backendMessage && 
                           userFriendlyError.backendMessage !== userFriendlyError.message;
  const hasValidationErrors = showValidationErrors && userFriendlyError.validationErrors && 
                             userFriendlyError.validationErrors.length > 0;
  const hasAdditionalDetails = hasBackendDetails || hasValidationErrors;

  const getIcon = () => {
    switch (userFriendlyError.type) {
      case 'warning':
        return <AlertTriangle size={getIconSize()} color="$orange10" />;
      case 'info':
        return <Info size={getIconSize()} color="$blue10" />;
      default:
        return <XCircle size={getIconSize()} color="$red10" />;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 24;
      default: return 20;
    }
  };

  const getTitleSize = () => {
    switch (size) {
      case 'small': return '$3';
      case 'large': return '$5';
      default: return '$4';
    }
  };

  const getMessageSize = () => {
    switch (size) {
      case 'small': return '$2';
      case 'large': return '$4';
      default: return '$3';
    }
  };

  const renderInlineError = () => (
    <XStack alignItems="center" gap="$2" flexWrap="wrap">
      {getIcon()}
      <Text fontSize={getMessageSize()} color="$red10" flex={1}>
        {userFriendlyError.message}
      </Text>
      {hasAdditionalDetails && (
        <Button
          size="$2"
          variant="outlined"
          onPress={() => setIsExpanded(!isExpanded)}
          icon={isExpanded ? ChevronUp : ChevronDown}
        >
          {t('common.details', { defaultValue: 'Details' })}
        </Button>
      )}
    </XStack>
  );

  const renderCardError = () => (
    <Card
      backgroundColor="$red2"
      borderColor="$red6"
      borderWidth={1}
      padding="$3"
      borderRadius="$3"
    >
      <YStack gap="$2">
        <XStack alignItems="center" gap="$2">
          {getIcon()}
          <Text fontSize={getTitleSize()} fontWeight="600" color="$red10" flex={1}>
            {userFriendlyError.title}
          </Text>
          {onDismiss && (
            <Button
              size="$2"
              variant="outlined"
              onPress={onDismiss}
              icon={XCircle}
              color="$red8"
            />
          )}
        </XStack>
        
        <Text fontSize={getMessageSize()} color="$red9">
          {userFriendlyError.message}
        </Text>

        {hasAdditionalDetails && (
          <>
            <Separator borderColor="$red6" />
            
            <Button
              size="$2"
              variant="outlined"
              onPress={() => setShowDetails(!showDetails)}
              icon={showDetails ? ChevronUp : ChevronDown}
              color="$red8"
              alignSelf="flex-start"
            >
              {showDetails 
                ? t('common.hideDetails', { defaultValue: 'Hide Details' })
                : t('common.showDetails', { defaultValue: 'Show Details' })
              }
            </Button>

            {showDetails && (
              <YStack gap="$2" padding="$2" backgroundColor="$red1" borderRadius="$2">
                {hasBackendDetails && (
                  <YStack gap="$1">
                    <Text fontSize="$2" fontWeight="600" color="$red9">
                      🔍 {t('errors.backendDetails', { defaultValue: 'Technical Details' })}
                    </Text>
                    <Text fontSize="$2" color="$red8" fontFamily="monospace">
                      {userFriendlyError.backendMessage}
                    </Text>
                  </YStack>
                )}

                {hasValidationErrors && (
                  <YStack gap="$1">
                    <Text fontSize="$2" fontWeight="600" color="$red9">
                      ⚠️ {t('errors.validationIssues', { defaultValue: 'Please fix these issues' })}
                    </Text>
                    {userFriendlyError.validationErrors!.map((err, index) => (
                      <XStack key={index} gap="$2" alignItems="center">
                        <Text fontSize="$2" color="$red8">•</Text>
                        <Text fontSize="$2" color="$red8" fontWeight="500">
                          {err.field}:
                        </Text>
                        <Text fontSize="$2" color="$red8" flex={1}>
                          {err.message}
                        </Text>
                      </XStack>
                    ))}
                  </YStack>
                )}
              </YStack>
            )}
          </>
        )}

        {(onRetry || onDismiss) && (
          <XStack gap="$2" justifyContent="flex-end" marginTop="$2">
            {onRetry && (
              <Button
                size="$2"
                backgroundColor="$red9"
                color="white"
                onPress={onRetry}
              >
                {t('common.retry', { defaultValue: 'Try Again' })}
              </Button>
            )}
            {onDismiss && (
              <Button
                size="$2"
                variant="outlined"
                onPress={onDismiss}
                color="$red8"
              >
                {t('common.dismiss', { defaultValue: 'Dismiss' })}
              </Button>
            )}
          </XStack>
        )}
      </YStack>
    </Card>
  );

  const renderAlertError = () => (
    <YStack
      backgroundColor="$red1"
      borderColor="$red5"
      borderWidth={1}
      padding="$4"
      borderRadius="$4"
      gap="$3"
    >
      <XStack alignItems="center" gap="$3">
        {getIcon()}
        <YStack flex={1}>
          <Text fontSize={getTitleSize()} fontWeight="600" color="$red10">
            {userFriendlyError.title}
          </Text>
          <Text fontSize={getMessageSize()} color="$red9" marginTop="$1">
            {userFriendlyError.message}
          </Text>
        </YStack>
      </XStack>

      {hasAdditionalDetails && (
        <Button
          size="$3"
          variant="outlined"
          onPress={() => setShowDetails(!showDetails)}
          icon={showDetails ? ChevronUp : ChevronDown}
          color="$red8"
          alignSelf="flex-start"
        >
          {showDetails 
            ? t('common.hideDetails', { defaultValue: 'Hide Details' })
            : t('common.showDetails', { defaultValue: 'Show Details' })
          }
        </Button>
      )}

      {showDetails && hasAdditionalDetails && (
        <YStack gap="$3" padding="$3" backgroundColor="$red2" borderRadius="$3">
          {hasBackendDetails && (
            <YStack gap="$2">
              <XStack alignItems="center" gap="$2">
                <AlertCircle size={16} color="$red8" />
                <Text fontSize="$3" fontWeight="600" color="$red9">
                  {t('errors.backendDetails', { defaultValue: 'Technical Details' })}
                </Text>
              </XStack>
              <Text fontSize="$2" color="$red8" fontFamily="monospace" paddingLeft="$4">
                {userFriendlyError.backendMessage}
              </Text>
            </YStack>
          )}

          {hasValidationErrors && (
            <YStack gap="$2">
              <XStack alignItems="center" gap="$2">
                <AlertTriangle size={16} color="$red8" />
                <Text fontSize="$3" fontWeight="600" color="$red9">
                  {t('errors.validationIssues', { defaultValue: 'Please fix these issues' })}
                </Text>
              </XStack>
              {userFriendlyError.validationErrors!.map((err, index) => (
                <YStack key={index} paddingLeft="$4" gap="$1">
                  <XStack alignItems="center" gap="$2">
                    <Text fontSize="$2" color="$red8">•</Text>
                    <Text fontSize="$2" color="$red8" fontWeight="500">
                      {err.field}:
                    </Text>
                  </XStack>
                  <Text fontSize="$2" color="$red8" paddingLeft="$3">
                    {err.message}
                  </Text>
                </YStack>
              ))}
            </YStack>
          )}
        </YStack>
      )}

      {(onRetry || onDismiss) && (
        <XStack gap="$3" justifyContent="flex-end" marginTop="$2">
          {onRetry && (
            <Button
              size="$3"
              backgroundColor="$red9"
              color="white"
              onPress={onRetry}
            >
              {t('common.retry', { defaultValue: 'Try Again' })}
            </Button>
          )}
          {onDismiss && (
            <Button
              size="$3"
              variant="outlined"
              onPress={onDismiss}
              color="$red8"
            >
              {t('common.dismiss', { defaultValue: 'Dismiss' })}
            </Button>
          )}
        </XStack>
      )}
    </YStack>
  );

  switch (variant) {
    case 'inline':
      return renderInlineError();
    case 'alert':
      return renderAlertError();
    default:
      return renderCardError();
  }
};
