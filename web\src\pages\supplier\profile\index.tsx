import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Camera,
  Store,
  User,
  Phone,
  Mail,
  Clock,
  MapPin,
  Settings,
  Save,
  Bell,
  CreditCard,
  ToggleLeft,
  ToggleRight,
  Edit3,
  Upload,
  X,
  Check,
  Sparkles,
  Crown,
  Award,
  Target,
  Zap,
  Flame,
  Loader,
  AlertCircle
} from 'lucide-react';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';
import { apiService } from '../../../services/api';

// Floating Orb Component for background
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color} opacity-20 blur-3xl pointer-events-none`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
    }}
    animate={{
      x: [0, 30, -20, 25, 0],
      y: [0, -25, 20, -15, 0],
      scale: [1, 1.1, 0.9, 1.05, 1],
      opacity: [0.08, 0.15, 0.06, 0.12, 0.08],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Particle System Component - Reduced for better readability
const ParticleSystem: React.FC = () => {
  const particles = Array.from({ length: 30 }, (_, i) => (
    <motion.div
      key={i}
      className="absolute w-1 h-1 bg-white rounded-full opacity-10"
      style={{
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
      }}
      animate={{
        y: [0, -80, 0],
        opacity: [0, 0.3, 0],
      }}
      transition={{
        duration: Math.random() * 4 + 3,
        repeat: Infinity,
        delay: Math.random() * 3,
      }}
    />
  ));

  return <div className="absolute inset-0 overflow-hidden pointer-events-none">{particles}</div>;
};

// Modern Glass Card Component
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
    style={{
      zIndex: 10,
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

const SupplierProfilePage: React.FC = () => {
  const { user, setCurrentUser } = useCurrentUserData();

  // State for actual supplier data from API
  const [actualSupplierData, setActualSupplierData] = useState<any>(null);

  const [email, setEmail] = useState(user?.email || '');
  const [phone, setPhone] = useState('');
  const [storeName, setStoreName] = useState('');
  const [location, setLocation] = useState<[number, number]>([35.2544, 32.2211]); // Nablus, Palestine
  const [bannerUri, setBannerUri] = useState('');
  const [logoUri, setLogoUri] = useState('');

  const [storeOpen, setStoreOpen] = useState<boolean | null>(null); // Start with null, will be updated from database
  const [showFullMap, setShowFullMap] = useState(false);
  const [openTime, setOpenTime] = useState('10:00');
  const [closeTime, setCloseTime] = useState('22:00');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const bannerInputRef = useRef<HTMLInputElement>(null);
  const logoInputRef = useRef<HTMLInputElement>(null);

  // Helper function to convert 12-hour format to 24-hour format
  const convertTo24Hour = (time12h: string): string => {
    try {
      if (!time12h || typeof time12h !== 'string') {
        return '10:00'; // Default fallback
      }

      // Remove any non-breaking spaces or unusual characters
      const cleanTime = time12h.replace(/\u00A0/g, ' ').trim();
      const [time, modifier] = cleanTime.split(/\s+/);

      if (!time || !modifier) {
        return '10:00'; // Default fallback
      }

      let [hours, minutes] = time.split(':');

      if (!hours || !minutes) {
        return '10:00'; // Default fallback
      }

      let hourNum = parseInt(hours, 10);

      if (isNaN(hourNum) || hourNum < 1 || hourNum > 12) {
        return '10:00'; // Default fallback
      }

      if (hourNum === 12) {
        hourNum = 0;
      }

      if (modifier.toUpperCase() === 'PM') {
        hourNum += 12;
      }

      return `${hourNum.toString().padStart(2, '0')}:${minutes.padStart(2, '0')}`;
    } catch (error) {
      console.error('Error converting time format:', error, 'Input:', time12h);
      return '10:00'; // Default fallback
    }
  };

  // Helper function to convert 24-hour format to 12-hour format
  const convertTo12Hour = (time24h: string): string => {
    try {
      if (!time24h || typeof time24h !== 'string') {
        return '10:00 AM'; // Default fallback
      }

      const [hours, minutes] = time24h.split(':');

      if (!hours || !minutes) {
        return '10:00 AM'; // Default fallback
      }

      const hour = parseInt(hours, 10);

      if (isNaN(hour) || hour < 0 || hour > 23) {
        return '10:00 AM'; // Default fallback
      }

      const ampm = hour >= 12 ? 'PM' : 'AM';
      const displayHour = hour % 12 || 12;
      return `${displayHour}:${minutes} ${ampm}`;
    } catch (error) {
      console.error('Error converting time format:', error, 'Input:', time24h);
      return '10:00 AM'; // Default fallback
    }
  };

  // Fetch actual supplier data from API
  useEffect(() => {
    const fetchSupplierData = async () => {
      if (!user?.supplierId) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('🔍 Fetching supplier profile data...');
        console.log('👤 Current user data:', user);
        console.log('🆔 User supplierId:', user?.supplierId);

        // Use the getSupplierById method to fetch supplier data
        const response = await apiService.getSupplierById(user.supplierId);

        if (response.success && response.data) {
          const supplier = response.data;
          console.log('📥 Received supplier data:', supplier);
          console.log('🏪 Store isActive status from DB:', supplier.isActive);

          setActualSupplierData(supplier);
          setPhone(supplier.phone || '');
          setStoreName(supplier.name || '');
          setLocation(supplier.lng && supplier.lat
            ? [supplier.lng, supplier.lat]
            : [35.2544, 32.2211]
          );
          setBannerUri(supplier.banner || '');
          setLogoUri(supplier.logoUrl || '');

          // Set store open status from database
          setStoreOpen(supplier.isActive ?? true);

          // Parse open hours if available
          if (supplier.openHours) {
            console.log('🕐 Raw openHours from backend:', supplier.openHours);
            const hours = supplier.openHours.split(' - ');
            console.log('🕐 Split hours:', hours);
            if (hours.length === 2) {
              const openTime24 = convertTo24Hour(hours[0].trim());
              const closeTime24 = convertTo24Hour(hours[1].trim());
              console.log('🕐 Converted times:', { open: openTime24, close: closeTime24 });
              // Convert from 12-hour format (from backend) to 24-hour format (for HTML input)
              setOpenTime(openTime24);
              setCloseTime(closeTime24);
            }
          }
        } else {
          throw new Error('No valid supplier data received');
        }
      } catch (error) {
        console.error('❌ Error fetching supplier profile:', error);
        // Don't use fallback data - let the user know there's an issue
        setStoreOpen(true); // Default to open
      } finally {
        setIsLoading(false);
      }
    };

    fetchSupplierData();
  }, [user?.supplierId]);

  const handleBannerUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setBannerUri(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoUri(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveProfile = async () => {
    if (isSaving) return;

    // Basic validation
    if (!storeName.trim()) {
      alert('Store name is required');
      return;
    }

    if (!phone.trim()) {
      alert('Phone number is required');
      return;
    }

    setIsSaving(true);
    try {
      console.log('💾 Saving supplier profile...');

      // Prepare the update data
      const updateData = {
        firstName: user?.firstName,
        lastName: user?.lastName,
        phoneNumber: phone.trim(),
        storeName: storeName.trim(),
        lat: location[1],
        lng: location[0],
        logoUrl: logoUri,
        banner: bannerUri,
        openHours: `${convertTo12Hour(openTime)} - ${convertTo12Hour(closeTime)}`,
        isActive: storeOpen ?? false, // Default to false if null
        address: `Nablus, Palestine`, // Default address
        city: 'Nablus',
        country: 'Palestine'
      };

      console.log('📤 Sending update data:', updateData);

      const response = await apiService.updateSupplierProfile(updateData);

      if (response.success && response.data && response.data.user && response.data.supplier) {
        console.log('✅ Profile updated successfully');
        console.log('📥 Response data:', response.data);

        // Update the user context with new data
        setCurrentUser(response.data.user);

        alert('Success! 🎉\nYour profile has been updated successfully!');
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('❌ Error saving profile:', error);
      alert('Error\nFailed to save profile. Please check your connection and try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
        }
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes wave {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes breathe {
          0%, 100% { transform: scale(1) rotate(0deg); }
          50% { transform: scale(1.05) rotate(1deg); }
        }
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }
      `}</style>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Behind everything */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs - Behind everything */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="max-w-6xl mx-auto space-y-8">

            {/* Loading State */}
            {isLoading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex items-center justify-center min-h-[400px]"
              >
                <div className="text-center">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"
                  />
                  <p className="text-white text-lg font-semibold">Loading your profile...</p>
                </div>
              </motion.div>
            )}

            {/* Admin Information Section - Only show for supplier admins */}
            {!isLoading && user?.role === 'supplier-admin' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <GlassCard>
                  <div className="p-8">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl">
                        <Crown size={32} className="text-white" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-white">Admin Information</h2>
                        <p className="text-white/70">Your personal account details as the store administrator</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {/* Admin Name */}
                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-medium">Full Name</label>
                        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                          <div className="flex items-center gap-3">
                            <User size={20} className="text-white/60" />
                            <span className="text-white font-medium">
                              {user?.firstName} {user?.lastName}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Admin Email */}
                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-medium">Email Address</label>
                        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                          <div className="flex items-center gap-3">
                            <Mail size={20} className="text-white/60" />
                            <span className="text-white font-medium">{user?.email}</span>
                          </div>
                        </div>
                      </div>

                      {/* Admin Username */}
                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-medium">Username</label>
                        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                          <div className="flex items-center gap-3">
                            <User size={20} className="text-white/60" />
                            <span className="text-white font-medium">@{user?.username}</span>
                          </div>
                        </div>
                      </div>

                      {/* Admin Phone */}
                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-medium">Phone Number</label>
                        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                          <div className="flex items-center gap-3">
                            <Phone size={20} className="text-white/60" />
                            <span className="text-white font-medium">{user?.phoneNumber}</span>
                          </div>
                        </div>
                      </div>

                      {/* Admin Location */}
                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-medium">Location</label>
                        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                          <div className="flex items-center gap-3">
                            <MapPin size={20} className="text-white/60" />
                            <span className="text-white font-medium">
                              {user?.city}, {user?.country}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Role Badge */}
                      <div className="space-y-2">
                        <label className="text-white/80 text-sm font-medium">Role</label>
                        <div className="bg-gradient-to-r from-yellow-400/20 to-orange-500/20 backdrop-blur-sm rounded-xl p-4 border border-yellow-400/30">
                          <div className="flex items-center gap-3">
                            <Crown size={20} className="text-yellow-400" />
                            <span className="text-yellow-400 font-bold">Store Administrator</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 p-4 bg-blue-500/10 border border-blue-400/30 rounded-xl">
                      <div className="flex items-start gap-3">
                        <AlertCircle size={20} className="text-blue-400 mt-0.5" />
                        <div>
                          <p className="text-blue-400 font-medium">Administrator Privileges</p>
                          <p className="text-blue-300/80 text-sm mt-1">
                            As the store administrator, you have full access to manage your store, products, employees, and settings.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </GlassCard>
              </motion.div>
            )}

            {/* Profile Content - Only show when not loading */}
            {!isLoading && (
              <>

            {/* Enhanced Header with Gradient */}
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="relative">
                <div
                  className="relative w-full h-64 rounded-3xl overflow-hidden cursor-pointer group"
                  onClick={() => bannerInputRef.current?.click()}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600">
                    {bannerUri ? (
                      <img
                        src={bannerUri}
                        className="w-full h-full object-cover opacity-90"
                      />
                    ) : (
                      /* Default gradient background when no banner */
                      <div className="w-full h-full bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600" />
                    )}

                    {/* Overlay Content - Only show on hover or when no banner */}
                    <div className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
                      bannerUri
                        ? 'bg-black/0 group-hover:bg-black/40 opacity-0 group-hover:opacity-100'
                        : 'bg-black/20'
                    }`}>
                      <div className="text-center mb-16">
                        <motion.div
                          whileHover={{ scale: 1.1 }}
                          className="p-4 bg-white/20 rounded-full mb-4 mx-auto w-fit backdrop-blur-sm"
                        >
                          <Camera size={40} className="text-white" />
                        </motion.div>
                        <p className="text-white text-lg font-bold">
                          {bannerUri ? 'Change Cover Photo' : 'Add Cover Photo'}
                        </p>
                      </div>
                    </div>

                    {/* Store Status Badge */}
                    <div className={`absolute top-6 right-6 px-4 py-2 rounded-full flex items-center gap-2 shadow-lg ${
                      storeOpen ? 'bg-green-500' : 'bg-red-500'
                    }`}>
                      <motion.div
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [1, 0.8, 1]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="w-2 h-2 bg-white rounded-full"
                      />
                      <span className="text-white text-sm font-bold">
                        {storeOpen ? 'OPEN' : 'CLOSED'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Profile Picture with Enhanced Design */}
                <div className="absolute mb-2 -bottom-20 left-0 right-0 flex justify-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                    className="relative w-40 h-40 cursor-pointer group"
                    onClick={() => logoInputRef.current?.click()}
                  >
                    <div className="w-full h-full rounded-full border-4 border-white bg-white shadow-2xl overflow-hidden">
                      {logoUri ? (
                        <img
                          src={logoUri}
                          alt="Store Logo"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                          <Store size={48} className="text-gray-500" />
                        </div>
                      )}
                    </div>

                    {/* Edit Button with Enhanced Design */}
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                      className="absolute bottom-2 right-2 w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center border-4 border-white shadow-xl"
                    >
                      <Camera size={20} className="text-white" />
                    </motion.div>

                    {/* Hover Ring Effect */}
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-purple-400/0 group-hover:border-purple-400/50 transition-all duration-300"
                      whileHover={{
                        scale: 1.05,
                        boxShadow: "0 0 30px rgba(139, 92, 246, 0.3)"
                      }}
                    />
                  </motion.div>
                </div>

                {/* Hidden file inputs */}
                <input
                  ref={bannerInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleBannerUpload}
                  className="hidden"
                />
                <input
                  ref={logoInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
              </div>
            </motion.div>

            {/* Store Name Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              <GlassCard className="p-8 mt-24">
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <Store size={20} className="text-purple-400" />
                    <span className="text-white/80 text-sm font-semibold">Store Name (Click to Edit)</span>
                    <Edit3 size={16} className="text-purple-400" />
                  </div>

                  <input
                    value={storeName}
                    onChange={(e) => setStoreName(e.target.value)}
                    className="w-full text-center text-3xl font-black text-white bg-white/10 border border-white/20 rounded-2xl px-6 py-4 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-white/50"
                    placeholder="Enter your store name"
                  />

                  <p className="text-white/60 text-sm">
                    This name will be displayed to customers
                  </p>

                  <div className="flex items-center justify-center gap-2 mt-4">
                    <MapPin size={16} className="text-gray-400" />
                    <span className="text-white/70 text-sm">
                      Lat: {location[1].toFixed(4)}, Lng: {location[0].toFixed(4)}
                    </span>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              <GlassCard className="p-8">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-blue-500/20 rounded-2xl border border-blue-400/30">
                      <User size={24} className="text-blue-400" />
                    </div>
                    <h3 className="text-2xl font-black text-white">Contact Information</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <label className="block text-white/80 text-sm font-semibold">Email Address</label>
                      <div className="relative">
                        <Mail size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60" />
                        <input
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <label className="block text-white/80 text-sm font-semibold">Phone Number</label>
                      <div className="relative">
                        <Phone size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60" />
                        <input
                          type="tel"
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="+970 59 123 4567"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Business Hours */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.0, duration: 0.6 }}
            >
              <GlassCard className="p-8">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-green-500/20 rounded-2xl border border-green-400/30">
                      <Clock size={24} className="text-green-400" />
                    </div>
                    <h3 className="text-2xl font-black text-white">Business Hours</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <label className="block text-white/80 text-sm font-semibold">Opening Time</label>
                      <input
                        type="time"
                        value={openTime}
                        onChange={(e) => setOpenTime(e.target.value)}
                        className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                    </div>

                    <div className="space-y-3">
                      <label className="block text-white/80 text-sm font-semibold">Closing Time</label>
                      <input
                        type="time"
                        value={closeTime}
                        onChange={(e) => setCloseTime(e.target.value)}
                        className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className={`p-6 rounded-2xl border ${
                    storeOpen
                      ? 'bg-green-500/20 border-green-400/30'
                      : 'bg-red-500/20 border-red-400/30'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`w-3 h-3 rounded-full ${
                          storeOpen ? 'bg-green-400' : 'bg-red-400'
                        }`}></div>
                        <div>
                          <p className={`text-lg font-bold ${
                            storeOpen ? 'text-green-300' : 'text-red-300'
                          }`}>
                            Store is {storeOpen ? 'Open' : 'Closed'}
                          </p>
                          <p className={`text-sm ${
                            storeOpen ? 'text-green-400/80' : 'text-red-400/80'
                          }`}>
                            {storeOpen ? 'Accepting orders' : 'Not accepting orders'}
                          </p>
                        </div>
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setStoreOpen(!storeOpen)}
                        className={`p-2 rounded-full transition-colors ${
                          storeOpen
                            ? 'bg-green-500 hover:bg-green-600'
                            : 'bg-red-500 hover:bg-red-600'
                        }`}
                      >
                        {storeOpen ? (
                          <ToggleRight size={24} className="text-white" />
                        ) : (
                          <ToggleLeft size={24} className="text-white" />
                        )}
                      </motion.button>
                    </div>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Store Location */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.6 }}
            >
              <GlassCard className="p-8">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-orange-500/20 rounded-2xl border border-orange-400/30">
                      <MapPin size={24} className="text-orange-400" />
                    </div>
                    <h3 className="text-2xl font-black text-white">Store Location</h3>
                  </div>

                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="relative h-64 bg-white/10 border border-white/20 rounded-2xl overflow-hidden cursor-pointer group"
                    onClick={() => setShowFullMap(true)}
                  >
                    {/* Map placeholder with gradient */}
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 to-red-500/30">
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Store size={24} className="text-white" />
                          </div>
                          <p className="text-white text-lg font-bold">Your Store Location</p>
                          <p className="text-white/70 text-sm mt-2">
                            {location[1].toFixed(4)}, {location[0].toFixed(4)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Overlay with Tap Instruction */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white text-lg font-bold">Tap to Edit Location</p>
                          <p className="text-white/80 text-sm">
                            Lat: {location[1].toFixed(4)}, Lng: {location[0].toFixed(4)}
                          </p>
                        </div>
                        <div className="p-3 bg-white/20 rounded-xl">
                          <Edit3 size={20} className="text-white" />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Additional Settings */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.4, duration: 0.6 }}
            >
              <GlassCard className="p-8">
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-purple-500/20 rounded-2xl border border-purple-400/30">
                      <Settings size={24} className="text-purple-400" />
                    </div>
                    <h3 className="text-2xl font-black text-white">Additional Settings</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="p-6 bg-white/10 border border-white/20 rounded-2xl">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <Bell size={20} className="text-white/70" />
                          <div>
                            <p className="text-white font-semibold">Push Notifications</p>
                            <p className="text-white/60 text-sm">Receive order alerts</p>
                          </div>
                        </div>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="p-2 bg-green-500 rounded-full"
                        >
                          <ToggleRight size={24} className="text-white" />
                        </motion.button>
                      </div>
                    </div>

                    <div className="p-6 bg-white/10 border border-white/20 rounded-2xl">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <CreditCard size={20} className="text-white/70" />
                          <div>
                            <p className="text-white font-semibold">Online Payments</p>
                            <p className="text-white/60 text-sm">Accept card payments</p>
                          </div>
                        </div>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="p-2 bg-green-500 rounded-full"
                        >
                          <ToggleRight size={24} className="text-white" />
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Save Button */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1.6, duration: 0.6, type: 'spring' }}
            >
              <motion.button
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 20px 40px -12px rgba(139, 92, 246, 0.4)"
                }}
                whileTap={{ scale: 0.98 }}
                onClick={handleSaveProfile}
                disabled={isSaving}
                className={`w-full p-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-3xl font-bold text-lg shadow-2xl border border-white/20 hover:from-purple-700 hover:to-blue-700 transition-all duration-300 flex items-center justify-center gap-4 ${
                  isSaving ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSaving ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Loader size={24} />
                    </motion.div>
                    Saving Changes...
                  </>
                ) : (
                  <>
                    <Save size={24} />
                    Save All Changes
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                      <Sparkles size={20} />
                    </motion.div>
                  </>
                )}
              </motion.button>
            </motion.div>
            </>
            )}

          </div>
        </div>

        {/* Enhanced Map Modal */}
        <AnimatePresence>
          {showFullMap && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setShowFullMap(false)}
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-3xl overflow-hidden max-w-4xl w-full max-h-[80vh] border border-white/20"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Header */}
                <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-white text-2xl font-black">Set Store Location</h3>
                      <p className="text-white/80 text-sm mt-1">
                        Click on the map to set your store location
                      </p>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setShowFullMap(false)}
                      className="p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
                    >
                      <X size={24} className="text-white" />
                    </motion.button>
                  </div>
                </div>

                {/* Map Content */}
                <div
                  className="h-96 bg-gradient-to-br from-orange-500/30 to-red-500/30 relative cursor-pointer"
                  onClick={(e) => {
                    // Simple click to set location (in a real app, this would be a proper map)
                    const rect = e.currentTarget.getBoundingClientRect();
                    const x = (e.clientX - rect.left) / rect.width;
                    const y = (e.clientY - rect.top) / rect.height;

                    // Convert to approximate coordinates (this is just for demo)
                    const newLng = 35.2544 + (x - 0.5) * 0.01;
                    const newLat = 32.2211 + (0.5 - y) * 0.01;

                    setLocation([newLng, newLat]);
                  }}
                >
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Store size={32} className="text-white" />
                      </div>
                      <p className="text-white text-xl font-bold">Interactive Map</p>
                      <p className="text-white/70 text-sm mt-2">
                        Click to set location: {location[1].toFixed(6)}, {location[0].toFixed(6)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Bottom Info Panel */}
                <div className="p-6 bg-gradient-to-r from-slate-800 to-slate-900">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-purple-500/20 rounded-2xl">
                        <MapPin size={24} className="text-purple-400" />
                      </div>
                      <div>
                        <p className="text-white font-bold">Current Location</p>
                        <p className="text-white/70 text-sm">
                          Lat: {location[1].toFixed(6)}, Lng: {location[0].toFixed(6)}
                        </p>
                      </div>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setShowFullMap(false)}
                      className="px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-2xl font-bold flex items-center gap-2 transition-colors"
                    >
                      <Check size={20} />
                      Confirm Location
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export default SupplierProfilePage;
export { SupplierProfilePage };
