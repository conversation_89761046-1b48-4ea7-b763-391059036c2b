import mongoose, { Document, Schema } from 'mongoose';

export interface AIFeedback extends Document {
  _id: string;
  conversationId: string;
  messageId: string;
  userId: string;
  rating: number; // 1-5 scale
  feedback?: string;
  helpful?: boolean;
  categories?: string[]; // e.g., ['accuracy', 'helpfulness', 'speed']
  timestamp: Date;
  metadata?: {
    platform?: string;
    appVersion?: string;
    responseTime?: number;
  };
}

const FeedbackSchema = new Schema<AIFeedback>({
  conversationId: {
    type: String,
    required: true,
    index: true
  },
  messageId: {
    type: String,
    required: true,
    index: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
    index: true
  },
  feedback: {
    type: String,
    maxlength: 1000
  },
  helpful: {
    type: Boolean,
    index: true
  },
  categories: [{
    type: String,
    enum: [
      'accuracy',
      'helpfulness',
      'speed',
      'clarity',
      'completeness',
      'relevance',
      'politeness',
      'technical_issue'
    ]
  }],
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  metadata: {
    platform: String,
    appVersion: String,
    responseTime: Number
  }
}, {
  timestamps: true,
  collection: 'ai_feedback'
});

// Indexes for analytics and reporting
FeedbackSchema.index({ userId: 1, timestamp: -1 });
FeedbackSchema.index({ rating: 1, timestamp: -1 });
FeedbackSchema.index({ helpful: 1, timestamp: -1 });
FeedbackSchema.index({ categories: 1, timestamp: -1 });

// Compound indexes for complex queries
FeedbackSchema.index({ conversationId: 1, messageId: 1 }, { unique: true });

// Static methods for analytics
FeedbackSchema.statics.getAverageRating = function(startDate?: Date, endDate?: Date) {
  const match: any = {};
  if (startDate || endDate) {
    match.timestamp = {};
    if (startDate) match.timestamp.$gte = startDate;
    if (endDate) match.timestamp.$lte = endDate;
  }

  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalFeedback: { $sum: 1 },
        helpfulCount: {
          $sum: { $cond: [{ $eq: ['$helpful', true] }, 1, 0] }
        }
      }
    }
  ]);
};

FeedbackSchema.statics.getRatingDistribution = function(startDate?: Date, endDate?: Date) {
  const match: any = {};
  if (startDate || endDate) {
    match.timestamp = {};
    if (startDate) match.timestamp.$gte = startDate;
    if (endDate) match.timestamp.$lte = endDate;
  }

  return this.aggregate([
    { $match: match },
    {
      $group: {
        _id: '$rating',
        count: { $sum: 1 }
      }
    },
    { $sort: { _id: 1 } }
  ]);
};

FeedbackSchema.statics.getCategoryAnalysis = function(startDate?: Date, endDate?: Date) {
  const match: any = {};
  if (startDate || endDate) {
    match.timestamp = {};
    if (startDate) match.timestamp.$gte = startDate;
    if (endDate) match.timestamp.$lte = endDate;
  }

  return this.aggregate([
    { $match: match },
    { $unwind: '$categories' },
    {
      $group: {
        _id: '$categories',
        count: { $sum: 1 },
        averageRating: { $avg: '$rating' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

FeedbackSchema.statics.getUserFeedbackSummary = function(userId: string) {
  return this.aggregate([
    { $match: { userId } },
    {
      $group: {
        _id: null,
        totalFeedback: { $sum: 1 },
        averageRating: { $avg: '$rating' },
        helpfulCount: {
          $sum: { $cond: [{ $eq: ['$helpful', true] }, 1, 0] }
        },
        lastFeedback: { $max: '$timestamp' }
      }
    }
  ]);
};

// Instance methods
FeedbackSchema.methods.addCategory = function(category: string) {
  if (!this.categories) {
    this.categories = [];
  }
  if (!this.categories.includes(category)) {
    this.categories.push(category);
  }
  return this.save();
};

FeedbackSchema.methods.removeCategory = function(category: string) {
  if (this.categories) {
    this.categories = this.categories.filter((cat: string) => cat !== category);
  }
  return this.save();
};

export const FeedbackModel = mongoose.model<AIFeedback>('Feedback', FeedbackSchema);
