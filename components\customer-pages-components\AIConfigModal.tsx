import React, { useState } from 'react';
import {
  Y<PERSON><PERSON>ck,
  XStack,
  Text,
  Input,
  Button,
  Card,
  H3,
  Paragraph,
  Separator,
  Sheet,
} from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { Alert } from 'react-native';
import { aiService } from '../../services/aiService';

interface AIConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfigured: () => void;
}

export const AIConfigModal: React.FC<AIConfigModalProps> = ({
  isOpen,
  onClose,
  onConfigured,
}) => {
  const [apiKey, setApiKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  const handleSaveApiKey = async () => {
    if (!apiKey.trim()) {
      Alert.alert('Error', 'Please enter a valid API key');
      return;
    }

    if (!apiKey.startsWith('sk-')) {
      Alert.alert('Error', 'OpenAI API keys should start with "sk-"');
      return;
    }

    setIsLoading(true);
    try {
      // Set the API key in the service
      aiService.setApiKey(apiKey.trim());
      
      // Test the API key with a simple request
      const testResponse = await aiService.sendMessage(
        'test-conversation',
        'Hello, this is a test message to verify the API key works.'
      );

      if (testResponse.success) {
        Alert.alert(
          'Success!',
          'API key configured successfully. AI Assistant is now ready to help!',
          [{ text: 'OK', onPress: () => { onConfigured(); onClose(); } }]
        );
      } else {
        throw new Error(testResponse.error || 'API key test failed');
      }
    } catch (error) {
      console.error('API key configuration error:', error);
      Alert.alert(
        'Configuration Failed',
        'The API key appears to be invalid or there was a connection error. Please check your key and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Sheet
      modal
      open={isOpen}
      onOpenChange={onClose}
      snapPoints={[85]}
      dismissOnSnapToBottom
    >
      <Sheet.Overlay />
      <Sheet.Handle />
      <Sheet.Frame padding="$4" backgroundColor="white">
        <YStack gap="$4">
          {/* Header */}
          <XStack alignItems="center" justifyContent="space-between">
            <H3 color="$purple10"><Text>🤖 Configure AI Assistant</Text></H3>
            <Button
              circular
              size="$3"
              backgroundColor="$gray4"
              onPress={onClose}
            >
              <Ionicons name="close" size={18} color="$gray10" />
            </Button>
          </XStack>

          <Separator />

          {/* Instructions */}
          <Card backgroundColor="$blue2" padding="$3" borderRadius="$4">
            <XStack alignItems="center" gap="$2" marginBottom="$2">
              <Ionicons name="information-circle" size={20} color="$blue10" />
              <Text fontSize="$4" fontWeight="600" color="$blue10">
                OpenAI API Key Required
              </Text>
            </XStack>
            <Paragraph color="$blue11" fontSize="$3" lineHeight="$4">
              <Text>To enable AI-powered assistance, you need to provide your OpenAI API key. 
              This allows the assistant to understand and respond to your questions about Wasel services.</Text>
            </Paragraph>
          </Card>

          {/* API Key Input */}
          <YStack gap="$3">
            <Text fontSize="$4" fontWeight="600" color="$gray12">
              Enter your OpenAI API Key:
            </Text>
            <Input
              placeholder="sk-..."
              value={apiKey}
              onChangeText={setApiKey}
              secureTextEntry
              borderRadius="$4"
              borderWidth={2}
              borderColor="$gray4"
              backgroundColor="$gray1"
              fontSize="$4"
              paddingHorizontal="$4"
              paddingVertical="$3"
              focusStyle={{
                borderColor: "$purple8",
                backgroundColor: "white"
              }}
            />
          </YStack>

          {/* Instructions Toggle */}
          <Button
            backgroundColor="$gray2"
            borderRadius="$4"
            onPress={() => setShowInstructions(!showInstructions)}
          >
            <XStack alignItems="center" gap="$2">
              <Ionicons 
                name={showInstructions ? "chevron-up" : "chevron-down"} 
                size={16} 
                color="$gray10" 
              />
              <Text color="$gray11" fontSize="$3">
                How to get an OpenAI API Key
              </Text>
            </XStack>
          </Button>

          {/* Detailed Instructions */}
          {showInstructions && (
            <Card backgroundColor="$gray1" padding="$3" borderRadius="$4">
              <YStack gap="$2">
                <Text fontSize="$3" fontWeight="600" color="$gray12">
                  Steps to get your API key:
                </Text>
                <Text fontSize="$3" color="$gray11">
                  1. Visit platform.openai.com
                </Text>
                <Text fontSize="$3" color="$gray11">
                  2. Sign up or log in to your account
                </Text>
                <Text fontSize="$3" color="$gray11">
                  3. Go to API Keys section
                </Text>
                <Text fontSize="$3" color="$gray11">
                  4. Click "Create new secret key"
                </Text>
                <Text fontSize="$3" color="$gray11">
                  5. Copy the key (starts with "sk-")
                </Text>
                <Text fontSize="$3" color="$gray11">
                  6. Paste it above and save
                </Text>
              </YStack>
            </Card>
          )}

          {/* Action Buttons */}
          <XStack gap="$3" marginTop="$4">
            <Button
              flex={1}
              backgroundColor="$gray4"
              borderRadius="$4"
              onPress={onClose}
            >
              <Text color="$gray11" fontSize="$4">Cancel</Text>
            </Button>
            <Button
              flex={2}
              backgroundColor="$purple10"
              borderRadius="$4"
              onPress={handleSaveApiKey}
              disabled={!apiKey.trim() || isLoading}
            >
              <XStack alignItems="center" gap="$2">
                {isLoading && <Ionicons name="sync" size={16} color="white" />}
                <Text color="white" fontSize="$4" fontWeight="600">
                  {isLoading ? 'Testing...' : 'Save & Test'}
                </Text>
              </XStack>
            </Button>
          </XStack>

          {/* Security Note */}
          <Card backgroundColor="$yellow2" padding="$3" borderRadius="$4">
            <XStack alignItems="center" gap="$2" marginBottom="$1">
              <Ionicons name="shield-checkmark" size={16} color="$yellow10" />
              <Text fontSize="$3" fontWeight="600" color="$yellow11">
                Security Note
              </Text>
            </XStack>
            <Text fontSize="$2" color="$yellow11" lineHeight="$3">
              Your API key is stored locally on your device and is only used to communicate with OpenAI's servers. 
              It is never shared with Wasel servers.
            </Text>
          </Card>
        </YStack>
      </Sheet.Frame>
    </Sheet>
  );
};
