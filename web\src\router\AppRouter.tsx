import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Authentication Pages
import LoginPage from '../pages/authentication/LoginPage';
import SignupPage from '../pages/authentication/SignupPage';
import EmailVerificationPage from '../pages/authentication/EmailVerificationPage';
import ResetPasswordPage from '../pages/authentication/ResetPasswordPage';
import VerifyBeforeResetPage from '../pages/authentication/VerifyBeforeResetPage';

// Demo Pages
import LanguageDemoPage from '../pages/demo/LanguageDemoPage';

// Customer Pages
import CustomerHomePage from '../pages/customer/HomePage';
import CustomerOrdersPage from '../pages/customer/OrdersPage';
import CustomerPackagesPage from '../pages/customer/PackagesPage';
import PackageDetailsPage from '../pages/customer/PackageDetailsPage';
import CustomerProfilePage from '../pages/customer/ProfilePage';
import SuppliersMapPage from '../pages/customer/SuppliersMapPage';
import SendPackagePage from '../pages/customer/SendPackagePage';
import SupplierCategoriesPage from '../pages/customer/SupplierCategoriesPage';
import SuppliersPage from '../pages/customer/SuppliersPage';
import SupplierDetailsPage from '../pages/customer/SupplierDetailsPage';
import SupplierProductDetailsPage from '../pages/customer/SupplierProductDetailsPage';
import OrderCheckoutPage from '../pages/customer/OrderCheckoutPage';
import OrderConfirmationPage from '../pages/customer/OrderConfirmationPage';
import RequestPickupPage from '../pages/customer/RequestPickupPage';
import RequestPickupConfirmationPage from '../pages/customer/RequestPickupConfirmationPage';
import SendPackageConfirmationPage from '../pages/customer/SendPackageConfirmationPage';
import SelectLocationPage from '../pages/customer/SelectLocationPage';
import OrderDetailsPage from '../pages/customer/OrderDetailsPage';
import OrderTrackingPage from '../pages/customer/OrderTrackingPage';
import PackageTrackingPage from '../pages/customer/PackageTrackingPage';

// Supplier Pages
import SupplierHomePage from '../pages/supplier/HomePage';
import SupplierAnalyticsPage from '../pages/supplier/AnalyticsPage';
import SupplierNotificationsPage from '../pages/supplier/notifications';
import SupplierProductsPage from '../pages/supplier/products';
import SupplierAddProductPage from '../pages/supplier/products/AddProduct';
import SupplierEditProductPage from '../pages/supplier/products/EditProduct';
import ManageOptionsPage from '../pages/supplier/products/manage-options';
import SupplierProfilePage from '../pages/supplier/ProfilePage';
import EmployeesPage from '../pages/supplier/employees';
import { SupplierOrderDetails, SupplierTracking } from '../pages/supplier/home';

// Layout Components
import AuthLayout from '../components/layouts/AuthLayout';
import CustomerLayout from '../components/layouts/CustomerLayout';
import SupplierLayout from '../components/layouts/SupplierLayout';

// Protected Route Component
import ProtectedRoute from '../components/auth/ProtectedRoute';

const AppRouter: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Root redirect */}
        <Route path="/" element={<Navigate to="/auth/login" replace />} />

        {/* Authentication Routes */}
        <Route path="/auth" element={<AuthLayout />}>
          <Route path="login" element={<LoginPage />} />
          <Route path="signup" element={<SignupPage />} />
          <Route path="email-verification" element={<EmailVerificationPage />} />
          <Route path="reset-password" element={<ResetPasswordPage />} />
          <Route path="verify-before-reset" element={<VerifyBeforeResetPage />} />
        </Route>

        {/* Demo Routes */}
        <Route path="/demo" element={<div className="min-h-screen" />}>
          <Route path="language" element={<LanguageDemoPage />} />
        </Route>

        {/* Customer Routes */}
        <Route
          path="/customer"
          element={
            <ProtectedRoute requiredRole="customer">
              <CustomerLayout />
            </ProtectedRoute>
          }
        >
          <Route path="home" element={<CustomerHomePage />} />
          <Route path="orders" element={<CustomerOrdersPage />} />
          <Route path="packages" element={<CustomerPackagesPage />} />
          <Route path="packages/details/:packageId" element={<PackageDetailsPage />} />
          <Route path="profile" element={<CustomerProfilePage />} />
          <Route path="suppliers-map" element={<SuppliersMapPage />} />
          <Route path="send-package" element={<SendPackagePage />} />
          <Route path="supplier-categories" element={<SupplierCategoriesPage />} />
          <Route path="suppliers-page" element={<SuppliersPage />} />
          <Route path="supplier-details" element={<SupplierDetailsPage />} />
          <Route path="supplier-product-details" element={<SupplierProductDetailsPage />} />
          <Route path="order-checkout" element={<OrderCheckoutPage />} />
          <Route path="order-confirmation" element={<OrderConfirmationPage />} />
          <Route path="request-pickup" element={<RequestPickupPage />} />
          <Route path="request-pickup-confirmation" element={<RequestPickupConfirmationPage />} />
          <Route path="send-package-confirmation" element={<SendPackageConfirmationPage />} />
          <Route path="select-location" element={<SelectLocationPage />} />
          <Route path="order-details" element={<OrderDetailsPage />} />
          <Route path="order-tracking" element={<OrderTrackingPage />} />
          <Route path="package-tracking" element={<PackageTrackingPage />} />
        </Route>

        {/* Supplier Routes */}
        <Route
          path="/supplier"
          element={
            <ProtectedRoute requiredRole="supplier">
              <SupplierLayout />
            </ProtectedRoute>
          }
        >
          <Route path="home" element={<SupplierHomePage />} />
          <Route path="order-details/:orderId" element={<SupplierOrderDetails />} />
          <Route path="tracking/:orderId" element={<SupplierTracking />} />
          <Route path="analytics" element={<SupplierAnalyticsPage />} />
          <Route path="notifications" element={<SupplierNotificationsPage />} />
          <Route path="products" element={<SupplierProductsPage />} />
          <Route path="products/add" element={<SupplierAddProductPage />} />
          <Route path="products/:productId/edit" element={<SupplierEditProductPage />} />
          <Route path="products/:productId/manage-options" element={<ManageOptionsPage />} />
          <Route path="profile" element={<SupplierProfilePage />} />
          <Route path="employees" element={<EmployeesPage />} />
        </Route>

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/auth/login" replace />} />
      </Routes>
    </Router>
  );
};

export default AppRouter;
