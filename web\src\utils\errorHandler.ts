// Simple error handling utility for user-friendly error messages

export type ErrorType = 'network' | 'server' | 'auth' | 'validation' | 'notFound' | 'conflict' | 'forbidden' | 'unknown';

export interface ErrorInfo {
  type: ErrorType;
  message: string;
  userFriendlyMessage: string;
  statusCode?: number;
  backendMessage?: string; // Add backend message for detailed feedback
  validationErrors?: Array<{ field: string; message: string }>; // Add validation errors
}

/**
 * Detect error type based on error object or HTTP status code
 */
export const detectErrorType = (error: any, statusCode?: number): ErrorType => {
  // Check HTTP status codes first
  if (statusCode) {
    switch (statusCode) {
      case 400:
        return 'validation';
      case 401:
        return 'auth';
      case 403:
        return 'forbidden';
      case 404:
        return 'notFound';
      case 409:
        return 'conflict';
      case 422:
        return 'validation';
      case 500:
      case 502:
      case 503:
      case 504:
        return 'server';
      default:
        return 'unknown';
    }
  }

  // Check error message patterns
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return 'network';
  }

  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    // Network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network';
    }
    
    // Authentication errors
    if (message.includes('401') || message.includes('unauthorized') || message.includes('token')) {
      return 'auth';
    }
    
    // Validation errors
    if (message.includes('validation') || message.includes('invalid') || message.includes('400')) {
      return 'validation';
    }
    
    // Not found errors
    if (message.includes('404') || message.includes('not found')) {
      return 'notFound';
    }
    
    // Conflict errors
    if (message.includes('409') || message.includes('conflict') || message.includes('duplicate')) {
      return 'conflict';
    }
    
    // Forbidden errors
    if (message.includes('403') || message.includes('forbidden') || message.includes('permission')) {
      return 'forbidden';
    }
    
    // Server errors
    if (message.includes('500') || message.includes('server') || message.includes('internal')) {
      return 'server';
    }
  }

  return 'unknown';
};

/**
 * Get user-friendly error message based on error type
 */
export const getErrorMessage = (type: ErrorType, t?: (key: string) => string): string => {
  if (t) {
    // Use i18n translations if available
    const translationKey = `errors.${type}Error`;
    const translated = t(translationKey);
    if (translated !== translationKey) {
      return translated;
    }
  }

  switch (type) {
    case 'network':
      return 'Connection problem. Please check your internet.';
    case 'server':
      return 'Service temporarily unavailable. Please try again.';
    case 'auth':
      return 'Please log in again to continue.';
    case 'validation':
      return 'Please check your input and try again.';
    case 'notFound':
      return 'The requested information was not found.';
    case 'conflict':
      return 'This item already exists. Please try something different.';
    case 'forbidden':
      return 'You don\'t have permission to access this.';
    default:
      return 'Something went wrong. Please try again.';
  }
};

/**
 * Process API response and return error info if failed
 */
export const processApiResponse = (response: any): { success: boolean; error?: ErrorInfo } => {
  if (!response) {
    return {
      success: false,
      error: {
        type: 'unknown',
        message: 'No response received',
        userFriendlyMessage: 'Something went wrong. Please try again.'
      }
    };
  }

  if (response.success) {
    return { success: true };
  }

  // Extract status code from response if available
  const statusCode = response.statusCode || response.status;
  const errorType = detectErrorType(response, statusCode);
  
  // Extract backend message - prioritize different possible fields
  let backendMessage = '';
  if (response.message) {
    backendMessage = response.message;
  } else if (response.error) {
    // Handle case where error is an object with a message property
    if (typeof response.error === 'object' && response.error.message) {
      backendMessage = response.error.message;
    } else if (typeof response.error === 'string') {
      backendMessage = response.error;
    }
  }
  
  // Extract validation errors
  let validationErrors: Array<{ field: string; message: string }> | undefined;
  if (response.errors && Array.isArray(response.errors)) {
    validationErrors = response.errors.map((err: any) => ({
      field: err.field || err.param || 'unknown',
      message: err.message || err.msg || 'Validation error'
    }));
  } else if (response.validationErrors && Array.isArray(response.validationErrors)) {
    validationErrors = response.validationErrors.map((err: any) => ({
      field: err.field || err.param || 'unknown',
      message: err.message || err.msg || 'Validation error'
    }));
  }
  
  return {
    success: false,
    error: {
      type: errorType,
      message: backendMessage || 'Request failed',
      userFriendlyMessage: getErrorMessage(errorType),
      statusCode,
      backendMessage,
      validationErrors
    }
  };
};

/**
 * Handle fetch errors and return structured error info
 */
export const handleFetchError = (error: any, t?: (key: string) => string): ErrorInfo => {
  const errorType = detectErrorType(error);
  const userFriendlyMessage = getErrorMessage(errorType, t);
  
  return {
    type: errorType,
    message: error instanceof Error ? error.message : 'Unknown error',
    userFriendlyMessage,
    statusCode: error.status || error.statusCode
  };
};

/**
 * Retry function with exponential backoff
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on certain error types
      const errorType = detectErrorType(error);
      if (errorType === 'auth' || errorType === 'forbidden' || errorType === 'validation') {
        throw error;
      }
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}; 