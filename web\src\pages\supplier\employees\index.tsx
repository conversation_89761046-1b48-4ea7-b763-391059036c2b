import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Users,
  Edit,
  Trash2,
  Search,
  Crown,
  Sparkles,
  Award,
  Target,
  Zap,
  Mail,
  Phone,
  MapPin,
  Calendar,
  User,
  Shield,
  ChevronRight,
  Filter,
  MoreVertical
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import { apiService } from '../../../services/api';
import AddEmployeeModal from '../employees/AddEmployeeModal';
import EditEmployeeModal from '../employees/EditEmployeeModal';

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  username: string;
  dateOfBirth?: string;
  gender?: string;
  address: string;
  city: string;
  country: string;
  isActive: boolean;
  createdAt: string;
}

// Modern Glass Card Component
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
    style={{ zIndex: 10, position: 'relative' }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Floating Orb Component
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06,
      zIndex: -1,
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Particle System Component
const ParticleSystem: React.FC = () => {
  const particles = Array.from({ length: 20 }, (_, i) => (
    <motion.div
      key={i}
      className="absolute w-1 h-1 bg-white/30 rounded-full"
      style={{
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
      }}
      animate={{
        y: [0, -100, 0],
        opacity: [0, 1, 0],
        scale: [0, 1, 0],
      }}
      transition={{
        duration: Math.random() * 3 + 2,
        repeat: Infinity,
        delay: Math.random() * 3,
      }}
    />
  ));

  return <div className="absolute inset-0 overflow-hidden pointer-events-none">{particles}</div>;
};

const EmployeesPage: React.FC = () => {
  const { user } = useAuth();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  // Only allow supplier admins to access this page
  if (user?.role !== 'supplier-admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">Only supplier administrators can manage employees.</p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await apiService.getEmployees();

      if (response.success && response.data) {
        // Transform User[] to Employee[] by ensuring required fields are present
        const employees: Employee[] = (response.data.employees || []).map(user => ({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          username: user.username,
          dateOfBirth: user.dateOfBirth,
          gender: user.gender,
          address: user.address || '', // Handle optional address from User type
          city: user.city || '',
          country: user.country || '',
          isActive: user.isActive,
          createdAt: user.createdAt,
        }));
        setEmployees(employees);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddEmployee = async (employeeData: any) => {
    try {
      const response = await apiService.addEmployee(employeeData);

      if (response.success) {
        await fetchEmployees();
        setShowAddModal(false);
      }
      return response;
    } catch (error) {
      console.error('Error adding employee:', error);
      return { success: false, message: 'Failed to add employee' };
    }
  };

  const handleEditEmployee = async (employeeData: any) => {
    if (!selectedEmployee) return { success: false, message: 'No employee selected' };

    try {
      const response = await apiService.updateEmployee(selectedEmployee.id, employeeData);

      if (response.success) {
        await fetchEmployees();
        setShowEditModal(false);
        setSelectedEmployee(null);
      }
      return response;
    } catch (error) {
      console.error('Error updating employee:', error);
      return { success: false, message: 'Failed to update employee' };
    }
  };

  const handleDeleteEmployee = async (employeeId: string) => {
    if (!confirm('Are you sure you want to delete this employee?')) return;

    try {
      const response = await apiService.deleteEmployee(employeeId);

      if (response.success) {
        await fetchEmployees();
      }
    } catch (error) {
      console.error('Error deleting employee:', error);
    }
  };

  const filteredEmployees = employees.filter(employee =>
    employee.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
        }
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes wave {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes breathe {
          0%, 100% { transform: scale(1) rotate(0deg); }
          50% { transform: scale(1.05) rotate(1deg); }
        }
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }
      `}</style>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Behind everything */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs - Behind everything */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="max-w-7xl mx-auto space-y-8">

            {/* Enhanced Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <GlassCard className="p-8">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                        className="p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl"
                      >
                        <Users size={40} className="text-white" />
                      </motion.div>
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full"
                      />
                    </div>
                    <div>
                      <h1 className="text-4xl font-bold text-white mb-2 flex items-center gap-3">
                        Employee Management
                        <motion.div
                          animate={{ rotate: [0, 10, -10, 0] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Crown size={32} className="text-yellow-400" />
                        </motion.div>
                      </h1>
                      <p className="text-white/70 text-lg">Manage your store team with powerful tools</p>
                      <div className="flex items-center gap-4 mt-3">
                        <div className="flex items-center gap-2 text-emerald-400">
                          <Shield size={16} />
                          <span className="text-sm font-medium">Admin Access</span>
                        </div>
                        <div className="flex items-center gap-2 text-blue-400">
                          <Target size={16} />
                          <span className="text-sm font-medium">{employees.length} Team Members</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowAddModal(true)}
                    className="relative group bg-gradient-to-r from-emerald-500 to-green-600 text-white px-8 py-4 rounded-2xl font-bold flex items-center gap-3 shadow-2xl overflow-hidden"
                  >
                    {/* Button shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:animate-[shimmer_1s_ease-in-out] pointer-events-none" />

                    <motion.div
                      animate={{ rotate: [0, 180, 360] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                      <Plus size={24} />
                    </motion.div>
                    Add New Employee

                    <motion.div
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ChevronRight size={20} />
                    </motion.div>
                  </motion.button>
                </div>
              </GlassCard>
            </motion.div>

            {/* Enhanced Search Bar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <GlassCard className="p-6">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60" size={20} />
                    <input
                      type="text"
                      placeholder="Search employees by name, email, or username..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                    />
                    {searchTerm && (
                      <motion.button
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        onClick={() => setSearchTerm('')}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                      >
                        ×
                      </motion.button>
                    )}
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-4 bg-white/10 border border-white/20 rounded-2xl text-white/80 hover:text-white hover:bg-white/20 transition-all duration-300"
                  >
                    <Filter size={20} />
                  </motion.button>
                </div>

                {searchTerm && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-4 pt-4 border-t border-white/20"
                  >
                    <p className="text-white/70 text-sm">
                      {filteredEmployees.length} employee{filteredEmployees.length !== 1 ? 's' : ''} found
                      {searchTerm && ` for "${searchTerm}"`}
                    </p>
                  </motion.div>
                )}
              </GlassCard>
            </motion.div>

            {/* Employees Grid */}
            {filteredEmployees.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <GlassCard className="p-12 text-center">
                  <motion.div
                    animate={{
                      y: [0, -10, 0],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{ duration: 3, repeat: Infinity }}
                    className="mb-6"
                  >
                    <Users className="mx-auto text-white/60 mb-4" size={80} />
                  </motion.div>

                  <h3 className="text-2xl font-bold text-white mb-3">
                    {searchTerm ? 'No employees found' : 'Build Your Dream Team'}
                  </h3>

                  <p className="text-white/70 text-lg mb-8 max-w-md mx-auto">
                    {searchTerm
                      ? 'Try adjusting your search terms or check the spelling'
                      : 'Start by adding your first team member to help manage your store operations'
                    }
                  </p>

                  {!searchTerm && (
                    <motion.button
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setShowAddModal(true)}
                      className="relative group bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-2xl font-bold flex items-center gap-3 mx-auto shadow-2xl overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:animate-[shimmer_1s_ease-in-out] pointer-events-none" />

                      <motion.div
                        animate={{ rotate: [0, 180, 360] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        <Plus size={24} />
                      </motion.div>
                      Add Your First Employee

                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <Sparkles size={20} />
                      </motion.div>
                    </motion.button>
                  )}
                </GlassCard>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {filteredEmployees.map((employee, index) => (
                  <motion.div
                    key={employee.id}
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: index * 0.1,
                      type: "spring",
                      stiffness: 300,
                      damping: 30
                    }}
                    whileHover={{ y: -8, scale: 1.02 }}
                  >
                    <GlassCard className="p-6 h-full" hoverEffect={false}>
                      {/* Employee Header */}
                      <div className="flex items-start justify-between mb-6">
                        <div className="flex items-center gap-4">
                          <motion.div
                            whileHover={{ scale: 1.1, rotate: 5 }}
                            className="relative"
                          >
                            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                              <User size={28} className="text-white" />
                            </div>
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 2, repeat: Infinity }}
                              className={`absolute -top-1 -right-1 w-4 h-4 rounded-full ${
                                employee.isActive ? 'bg-emerald-400' : 'bg-red-400'
                              }`}
                            />
                          </motion.div>

                          <div>
                            <h3 className="text-xl font-bold text-white mb-1">
                              {employee.firstName} {employee.lastName}
                            </h3>
                            <p className="text-white/70 font-medium">@{employee.username}</p>
                          </div>
                        </div>

                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          className="p-2 bg-white/10 rounded-xl text-white/60 hover:text-white hover:bg-white/20 transition-all duration-300"
                        >
                          <MoreVertical size={18} />
                        </motion.button>
                      </div>

                      {/* Employee Details */}
                      <div className="space-y-4 mb-6">
                        <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                          <Mail size={18} className="text-blue-400" />
                          <div>
                            <p className="text-white/60 text-xs font-medium uppercase tracking-wide">Email</p>
                            <p className="text-white font-medium">{employee.email}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                          <Phone size={18} className="text-emerald-400" />
                          <div>
                            <p className="text-white/60 text-xs font-medium uppercase tracking-wide">Phone</p>
                            <p className="text-white font-medium">{employee.phoneNumber}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                          <MapPin size={18} className="text-purple-400" />
                          <div>
                            <p className="text-white/60 text-xs font-medium uppercase tracking-wide">Location</p>
                            <p className="text-white font-medium">{employee.city}, {employee.country}</p>
                          </div>
                        </div>

                        {employee.dateOfBirth && (
                          <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl">
                            <Calendar size={18} className="text-yellow-400" />
                            <div>
                              <p className="text-white/60 text-xs font-medium uppercase tracking-wide">Joined</p>
                              <p className="text-white font-medium">
                                {new Date(employee.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Status Badge */}
                      <div className="mb-6">
                        <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-xl font-medium ${
                          employee.isActive
                            ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30'
                            : 'bg-red-500/20 text-red-400 border border-red-500/30'
                        }`}>
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ duration: 1.5, repeat: Infinity }}
                            className={`w-2 h-2 rounded-full ${
                              employee.isActive ? 'bg-emerald-400' : 'bg-red-400'
                            }`}
                          />
                          {employee.isActive ? 'Active Employee' : 'Inactive'}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3">
                        <motion.button
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            setSelectedEmployee(employee);
                            setShowEditModal(true);
                          }}
                          className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-3 rounded-xl font-bold flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                          <Edit size={18} />
                          Edit
                        </motion.button>

                        <motion.button
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handleDeleteEmployee(employee.id)}
                          className="flex-1 bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-3 rounded-xl font-bold flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                          <Trash2 size={18} />
                          Remove
                        </motion.button>
                      </div>
                    </GlassCard>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>
        </div>

        {/* Modals */}
        <AnimatePresence>
          {showAddModal && (
            <AddEmployeeModal
              onClose={() => setShowAddModal(false)}
              onSubmit={handleAddEmployee}
            />
          )}

          {showEditModal && selectedEmployee && (
            <EditEmployeeModal
              employee={selectedEmployee}
              onClose={() => {
                setShowEditModal(false);
                setSelectedEmployee(null);
              }}
              onSubmit={handleEditEmployee}
            />
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export default EmployeesPage;
