import { Router } from 'express';
import { query, body, param } from 'express-validator';
import { SupplierController } from '../controllers/supplierController';
import { authenticate, authorizeSupplier } from '../middleware/auth';
import rateLimit from 'express-rate-limit';

const router = Router();

// Rate limiting for product management
const productManagementLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit each IP to 50 requests per windowMs
  message: {
    success: false,
    message: 'Too many product management requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for product creation (more restrictive)
const productCreationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 product creations per windowMs
  message: {
    success: false,
    message: 'Too many product creation requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation rules for supplier queries
const supplierQueryValidation = [
  query('category')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be a string between 1 and 50 characters'),
  query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search must be a string between 1 and 100 characters'),
  query('lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  query('lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180'),
  query('radius')
    .optional()
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Radius must be a number between 0.1 and 100 km'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be an integer between 1 and 100'),
  query('sortBy')
    .optional()
    .isIn(['name', 'rating', 'deliveryTime', 'createdAt'])
    .withMessage('SortBy must be one of: name, rating, deliveryTime, createdAt'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('SortOrder must be either asc or desc')
];

const productQueryValidation = [
  query('category')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be a string between 1 and 50 characters'),
  query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search must be a string between 1 and 100 characters'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be an integer between 1 and 100')
];

// Validation for supplier ID parameter
const supplierIdValidation = [
  param('supplierId')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Supplier ID must be a valid alphanumeric string')
];

// Validation for product ID parameter
const productIdValidation = [
  param('productId')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Product ID must be a valid alphanumeric string')
];

// Validation for adding/updating products
const productDataValidation = [
  body('name')
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Product name must be 2-100 characters'),
  body('price')
    .isFloat({ min: 0.01, max: 10000 })
    .withMessage('Price must be a number between 0.01 and 10000'),
  body('discountPrice')
    .optional()
    .isFloat({ min: 0, max: 10000 })
    .withMessage('Discount price must be a number between 0 and 10000'),
  body('categoryId')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category ID is required'),
  body('categoryName')
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category name must be 1-50 characters'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('image')
    .isString()
    .isLength({ min: 10, max: 10000000 }) // Base64 images can be large - increased limit
    .withMessage('Image is required and must be a valid base64 string'),
  body('isAvailable')
    .optional()
    .isBoolean()
    .withMessage('isAvailable must be a boolean'),
  body('restaurantOptions')
    .optional()
    .isObject()
    .withMessage('Restaurant options must be an object'),
  body('restaurantOptions.additions')
    .optional()
    .isArray({ max: 50 })
    .withMessage('Restaurant additions must be an array with maximum 50 items'),
  body('restaurantOptions.additions.*.name')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Addition name must be 1-100 characters'),
  body('restaurantOptions.additions.*.price')
    .optional()
    .isFloat({ min: 0, max: 1000 })
    .withMessage('Addition price must be between 0 and 1000'),
  body('restaurantOptions.without')
    .optional()
    .isArray({ max: 50 })
    .withMessage('Restaurant without options must be an array with maximum 50 items'),
  body('restaurantOptions.without.*')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Without option must be 1-100 characters'),
  body('restaurantOptions.sides')
    .optional()
    .isArray({ max: 50 })
    .withMessage('Restaurant sides must be an array with maximum 50 items'),
  body('restaurantOptions.sides.*.name')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Side name must be 1-100 characters'),
  body('restaurantOptions.sides.*.price')
    .optional()
    .isFloat({ min: 0, max: 1000 })
    .withMessage('Side price must be between 0 and 1000'),
  body('clothingOptions')
    .optional()
    .isObject()
    .withMessage('Clothing options must be an object'),
  body('clothingOptions.sizes')
    .optional()
    .isArray({ max: 20 })
    .withMessage('Clothing sizes must be an array with maximum 20 items'),
  body('clothingOptions.sizes.*')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Size must be 1-50 characters'),
  body('clothingOptions.colors')
    .optional()
    .isArray({ max: 30 })
    .withMessage('Clothing colors must be an array with maximum 30 items'),
  body('clothingOptions.colors.*')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Color must be 1-50 characters'),
  body('clothingOptions.gallery')
    .optional()
    .isArray({ max: 10 })
    .withMessage('Clothing gallery must be an array with maximum 10 items'),
  body('clothingOptions.gallery.*')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Gallery URL must be 1-2000 characters'),
  body('customOptions')
    .optional()
    .isArray({ max: 20 })
    .withMessage('Custom options must be an array with maximum 20 items'),
  body('customOptions.*.title')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Custom option title must be 1-100 characters'),
  body('customOptions.*.type')
    .optional()
    .isIn(['text', 'number', 'select', 'multi-select'])
    .withMessage('Custom option type must be one of: text, number, select, multi-select'),
  body('customOptions.*.values')
    .optional()
    .isArray({ max: 50 })
    .withMessage('Custom option values must be an array with maximum 50 items'),
  body('customOptions.*.values.*.name')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Custom option value name must be 1-100 characters'),
  body('customOptions.*.values.*.price')
    .optional()
    .isFloat({ min: 0, max: 1000 })
    .withMessage('Custom option value price must be between 0 and 1000')
];

// Public routes (no authentication required)
router.get('/', supplierQueryValidation, SupplierController.getSuppliers);
router.get('/category/:category', supplierQueryValidation, SupplierController.getSuppliersByCategory);
router.get('/:id', SupplierController.getSupplierById);
router.get('/:id/products', productQueryValidation, SupplierController.getSupplierProducts);
router.get('/:supplierId/products/:productId', SupplierController.getProductById);

// Protected routes (require authentication)
router.use(authenticate);

// Link user to existing supplier (requires authentication)
router.post('/link-to-existing', SupplierController.linkToExistingSupplier);

// Supplier profile management (requires authentication and supplier role)
router.get('/profile', authorizeSupplier(), SupplierController.getSupplierProfile);
router.put('/profile', authorizeSupplier(), SupplierController.updateSupplierProfile);

// Admin utility endpoints
router.get('/check-categories', SupplierController.checkProductCategories);
router.post('/fix-missing-categories', SupplierController.fixMissingCategories);

// Product management endpoints (public for testing)
router.post('/:supplierId/products', SupplierController.addProduct);
router.put('/:supplierId/products/:productId', SupplierController.updateProduct);
router.delete('/:supplierId/products/:productId', SupplierController.deleteProduct);
router.patch('/:supplierId/products/:productId/toggle-availability', SupplierController.toggleProductAvailability);

// Product management endpoints (requires authentication + authorization)
router.post('/:supplierId/products',
  productCreationLimiter,
  supplierIdValidation,
  productDataValidation,
  SupplierController.addProduct
);

router.put('/:supplierId/products/:productId',
  productManagementLimiter,
  supplierIdValidation,
  productIdValidation,
  productDataValidation,
  SupplierController.updateProduct
);

router.delete('/:supplierId/products/:productId',
  productManagementLimiter,
  supplierIdValidation,
  productIdValidation,
  SupplierController.deleteProduct
);

router.patch('/:supplierId/products/:productId/toggle-availability',
  productManagementLimiter,
  supplierIdValidation,
  productIdValidation,
  SupplierController.toggleProductAvailability
);

export default router;
