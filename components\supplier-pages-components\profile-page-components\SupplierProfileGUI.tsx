import { useState, useEffect } from 'react'
import { Image, ScrollView, Pressable, Alert } from 'react-native'
import { YStack, XStack, Text, H3, Input, Button, Card, Label, Switch, View } from 'tamagui'
import { Ionicons } from '@expo/vector-icons'
import * as Location from 'expo-location';
import { useCurrentUserData } from '~/components/useCurrentUserData'
import { suppliersData } from '~/temp-data/suppliersData'
import * as ImagePicker from 'expo-image-picker'
import { Modal, Platform } from 'react-native'
import MapView, { Marker } from 'react-native-maps'
import { MotiView } from 'moti'
import { LinearGradient } from 'expo-linear-gradient'
import DateTimePicker from '@react-native-community/datetimepicker'
import { apiService } from '~/services/apiService'
import { useRouter } from 'expo-router'

export default function SupplierProfile() {
  const { user, clearUser, setCurrentUser } = useCurrentUserData();
  const router = useRouter();
  const supplierData = suppliersData.find((s) => s.id === user?.supplierId);

  // State for actual supplier data from API
  const [actualSupplierData, setActualSupplierData] = useState<any>(null);

  const [email, setEmail] = useState(user?.email || '');
  const [phone, setPhone] = useState('');
  const [storeName, setStoreName] = useState('');
  const [location, setLocation] = useState<[number, number]>([35.2544, 32.2211]); // Nablus, Palestine
  const [bannerUri, setBannerUri] = useState('');
  const [logoUri, setLogoUri] = useState('');

  const [mapFullscreen, setMapFullscreen] = useState(false);
  const [storeOpen, setStoreOpen] = useState<boolean | null>(null); // Start with null, will be updated from database
  const [showFullMap, setShowFullMap] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch actual supplier data from API
  useEffect(() => {
    const fetchSupplierData = async () => {
      if (!user?.supplierId) {
        setIsLoading(false);
        return;
      }

      try {
        console.log('🔍 Fetching supplier profile data...');
        console.log('👤 Current user data:', user);
        console.log('🆔 User supplierId:', (user as any)?.supplierId);
        let supplier;

        // Use the working public endpoint directly (since linking is confirmed working)
        console.log('📡 Using getSupplierById (confirmed working method)...');
        const supplierData = await apiService.getSupplierById('3a-kefak');
        console.log('📥 Supplier data received:', supplierData);

        if (supplierData && supplierData.id) {
          supplier = supplierData;
          console.log('✅ Got supplier data successfully');
        } else {
          throw new Error('No valid supplier data received');
        }

        if (supplier) {
          console.log('📥 Received supplier data:', supplier);
          console.log('🏪 Store isActive status from DB:', supplier.isActive);
          console.log('🏪 Store isActive type:', typeof supplier.isActive);

          setActualSupplierData(supplier);
          setPhone(supplier.phone || supplierData?.phone || '');
          setStoreName(supplier.name || supplierData?.name || '');
          setLocation(supplier.lng && supplier.lat
            ? [supplier.lng, supplier.lat]
            : (supplierData?.lng && supplierData?.lat
              ? [supplierData.lng, supplierData.lat]
              : [35.2544, 32.2211])
          );
          setBannerUri(supplier.banner || supplierData?.banner || '');
          setLogoUri(supplier.logoUrl || supplierData?.logoUrl || '');

          // ✅ THIS IS THE KEY FIX: Set store open state from database isActive field
          const isActiveFromDB = supplier.isActive === true;
          console.log('🔄 Setting storeOpen to:', isActiveFromDB);
          console.log('🔄 Previous storeOpen state was:', storeOpen);
          setStoreOpen(isActiveFromDB);
          console.log('✅ storeOpen state updated to:', isActiveFromDB);

          // ✅ Parse and set opening hours from database
          if (supplier.openHours) {
            console.log('🕐 Parsing openHours from database:', supplier.openHours);
            try {
              // Handle formats like "10:00 AM - 10:00 PM" or "10:00 - 22:00"
              const parts = supplier.openHours.split(' - ');
              if (parts.length === 2) {
                const openTimeStr = parts[0].trim();
                const closeTimeStr = parts[1].trim();

                console.log('🕐 Setting opening time to:', openTimeStr);
                console.log('🕐 Setting closing time to:', closeTimeStr);

                setOpenTimeString(openTimeStr);
                setCloseTimeString(closeTimeStr);
              }
            } catch (error) {
              console.error('❌ Error parsing openHours:', error);
              // Keep default times if parsing fails
            }
          }
        } else {
          console.log('⚠️ No supplier data from API, using temp data');
          // Fallback to temp data
          setPhone(supplierData?.phone || '');
          setStoreName(supplierData?.name || '');
          setLocation(supplierData?.lng && supplierData?.lat
            ? [supplierData.lng, supplierData.lat]
            : [35.2544, 32.2211]
          );
          setBannerUri(supplierData?.banner || '');
          setLogoUri(supplierData?.logoUrl || '');

          // Default to true when no API data (store open by default)
          console.log('⚠️ No API data - defaulting storeOpen to true');
          setStoreOpen(true);
        }
      } catch (error) {
        console.error('❌ Error fetching supplier profile:', error);
        // Fallback to temp data
        setPhone(supplierData?.phone || '');
        setStoreName(supplierData?.name || '');
        setLocation(supplierData?.lng && supplierData?.lat
          ? [supplierData.lng, supplierData.lat]
          : [35.2544, 32.2211]
        );
        setBannerUri(supplierData?.banner || '');
        setLogoUri(supplierData?.logoUrl || '');
        // Default to true on error (store open by default)
        console.log('❌ API error - defaulting storeOpen to true');
        setStoreOpen(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSupplierData();
  }, [user?.supplierId]);

  async function pickBannerImage() {
    const res = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
    });
    if (!res.canceled && res.assets[0].uri) setBannerUri(res.assets[0].uri);
  }

  async function pickLogoImage() {
    const res = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });
    if (!res.canceled && res.assets[0].uri) setLogoUri(res.assets[0].uri);
  }

  const [openTimeString, setOpenTimeString] = useState('10:00 AM');
  const [closeTimeString, setCloseTimeString] = useState('10:00 PM');
  const [showPicker, setShowPicker] = useState<'open' | 'close' | null>(null);

  // Helper function to convert time string to Date object for picker
  const timeStringToDate = (timeStr: string): Date => {
    const date = new Date();
    try {
      if (timeStr.includes('AM') || timeStr.includes('PM')) {
        const [time, period] = timeStr.split(' ');
        const [hours, minutes] = time.split(':').map(Number);
        let hour24 = hours;
        if (period === 'PM' && hours !== 12) hour24 += 12;
        if (period === 'AM' && hours === 12) hour24 = 0;
        date.setHours(hour24, minutes || 0, 0, 0);
      } else {
        const [hours, minutes] = timeStr.split(':').map(Number);
        date.setHours(hours, minutes || 0, 0, 0);
      }
    } catch (error) {
      console.error('Error parsing time string:', error);
    }
    return date;
  };

  // Helper function to convert Date object to time string
  const dateToTimeString = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const onTimeChange = (_e: any, selected?: Date) => {
    if (selected) {
      const timeString = dateToTimeString(selected);
      if (showPicker === 'open') {
        setOpenTimeString(timeString);
        console.log('🕐 Opening time changed to:', timeString);
      } else if (showPicker === 'close') {
        setCloseTimeString(timeString);
        console.log('🕐 Closing time changed to:', timeString);
      }
    }
    setShowPicker(null);
  };

  const handleSaveProfile = async () => {
    if (isSaving) return;

    // Basic validation
    if (!storeName.trim()) {
      Alert.alert('Error', 'Store name is required');
      return;
    }

    if (!phone.trim()) {
      Alert.alert('Error', 'Phone number is required');
      return;
    }

    setIsSaving(true);
    try {
      console.log('💾 Saving supplier profile...');

      // Prepare the update data
      const updateData = {
        firstName: user?.firstName,
        lastName: user?.lastName,
        phoneNumber: phone.trim(),
        storeName: storeName.trim(),
        lat: location[1],
        lng: location[0],
        logoUrl: logoUri,
        banner: bannerUri,
        openHours: `${openTimeString} - ${closeTimeString}`,
        isActive: storeOpen ?? false, // Default to false if null
        address: `Nablus, Palestine`, // Default address
        city: 'Nablus',
        country: 'Palestine'
      };

      console.log('📤 Sending update data:', updateData);

      const response = await apiService.updateSupplierProfile(updateData);

      if (response.user && response.supplier) {
        console.log('✅ Profile updated successfully');
        console.log('📥 Response data:', response);

        // Update the user context with new data
        setCurrentUser(response.user);

        Alert.alert(
          'Success! 🎉',
          'Your profile has been updated successfully!',
          [{ text: 'OK' }]
        );
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('❌ Error saving profile:', error);
      Alert.alert(
        'Error',
        'Failed to save profile. Please check your connection and try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🚪 Logging out supplier...');
              await apiService.logout();
              clearUser();
              console.log('✅ Logout successful, navigating to login...');
              router.replace('/');
            } catch (error) {
              console.error('❌ Logout failed:', error);
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          }
        }
      ]
    );
  };



  // Show loading state while fetching supplier data
  if (isLoading) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <Text fontSize="$6" fontWeight="600" color="$purple10">Loading...</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView contentContainerStyle={{ paddingBottom: 120 }} showsVerticalScrollIndicator={false}>
        {/* Enhanced Header with Gradient */}
        <MotiView
          from={{ opacity: 0, translateY: -50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ duration: 800 }}
        >
          <View style={{ position: 'relative' }}>
            <Pressable onPress={pickBannerImage}>
              <LinearGradient
                colors={['#7c3aed', '#a855f7', '#ec4899']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{ width: '100%', height: 220 }}
              >
                {bannerUri ? (
                  <Image
                    source={{ uri: bannerUri }}
                    style={{ width: '100%', height: 220, opacity: 0.8 }}
                    resizeMode="cover"
                  />
                ) : null}

                {/* Overlay Content */}
                <View
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0,0,0,0.3)',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Ionicons name="camera" size={40} color="white" style={{ opacity: 0.8 }} />
                  <Text color="white" fontSize="$4" fontWeight="600" mt="$2">
                    Change Cover Photo
                  </Text>
                </View>

                {/* Store Status Badge */}
                <View
                  style={{
                    position: 'absolute',
                    top: 50,
                    right: 20,
                    backgroundColor: storeOpen ? '#10b981' : '#ef4444',
                    borderRadius: 20,
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 6,
                  }}
                >
                  <View
                    style={{
                      width: 8,
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: 'white',
                    }}
                  />
                  <Text color="white" fontSize="$3" fontWeight="700">
                    {storeOpen ? 'OPEN' : 'CLOSED'}
                  </Text>
                </View>
              </LinearGradient>
            </Pressable>

            {/* Profile Picture with Enhanced Design */}
            <MotiView
              from={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 400, type: 'spring', damping: 15 }}
              style={{
                position: 'absolute',
                bottom: -60,
                left: 0,
                right: 0,
                alignItems: 'center',
              }}
            >
              <Pressable onPress={pickLogoImage}>
                <View
                  style={{
                    width: 120,
                    height: 120,
                    borderRadius: 60,
                    borderWidth: 4,
                    borderColor: 'white',
                    backgroundColor: '#f8fafc',
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 8 },
                    shadowOpacity: 0.2,
                    shadowRadius: 16,
                    elevation: 8,
                    position: 'relative',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {logoUri ? (
                    <Image
                      source={{ uri: logoUri }}
                      style={{
                        width: 112,
                        height: 112,
                        borderRadius: 56,
                        margin: 4,
                      }}
                      resizeMode="cover"
                    />
                  ) : (
                    <View
                      style={{
                        width: 112,
                        height: 112,
                        borderRadius: 56,
                        margin: 4,
                        backgroundColor: '#e2e8f0',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <Ionicons name="storefront" size={40} color="#64748b" />
                    </View>
                  )}

                  {/* Edit Button - Better Positioned */}
                  <View
                    style={{
                      position: 'absolute',
                      bottom: 4,
                      right: 4,
                      backgroundColor: '#7c3aed',
                      borderRadius: 18,
                      width: 36,
                      height: 36,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderWidth: 3,
                      borderColor: 'white',
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.2,
                      shadowRadius: 4,
                      elevation: 4,
                    }}
                  >
                    <Ionicons name="camera" size={18} color="white" />
                  </View>
                </View>
              </Pressable>
            </MotiView>
          </View>
        </MotiView>

        {/* Store Name Section with Edit Indication */}
        <MotiView
          from={{ opacity: 0, translateY: 30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 600, duration: 600 }}
          style={{ marginTop: 80, paddingHorizontal: 20 }}
        >
          <YStack ai="center" gap="$3">
            <Card bg="$gray1" p="$4" br="$6" borderWidth={2} borderColor="$gray6" width="100%">
              <YStack ai="center" gap="$2">
                <XStack ai="center" gap="$2">
                  <Ionicons name="storefront" size={20} color="#7c3aed" />
                  <Text color="$gray11" fontSize="$3" fontWeight="600">
                    Store Name (Tap to Edit)
                  </Text>
                  <Ionicons name="pencil" size={16} color="#7c3aed" />
                </XStack>

                <Input
                  value={storeName}
                  onChangeText={setStoreName}
                  textAlign="center"
                  fontSize="$6"
                  fontWeight="800"
                  color="$gray12"
                  bg="$gray2"
                  borderWidth={1}
                  borderColor="$gray6"
                  borderRadius="$4"
                  placeholder="Enter your store name"
                  placeholderTextColor="$gray8"
                  width="100%"
                  p="$2"
                />

                <Text color="$gray9" fontSize="$2" textAlign="center">
                  This name will be displayed to customers
                </Text>
              </YStack>
            </Card>

            <XStack ai="center" gap="$2" mt="$2">
              <Ionicons name="location" size={16} color="#64748b" />
              <Text color="$gray10" fontSize="$3">
                Lat: {location[1].toFixed(4)}, Lng: {location[0].toFixed(4)}
              </Text>
            </XStack>
          </YStack>
        </MotiView>

        {/* Enhanced Settings Sections */}
        <YStack gap="$4" p="$4" mt="$4">
          {/* Contact Information */}
          <MotiView
            from={{ opacity: 0, translateX: -30 }}
            animate={{ opacity: 1, translateX: 0 }}
            transition={{ delay: 800, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#3b82f6',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="person" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800"><Text>Contact Information</Text></H3>
                </XStack>

                <YStack gap="$3">
                  <YStack gap="$2">
                    <Label color="$gray11" fontSize="$3" fontWeight="600"><Text>Email Address</Text></Label>
                    <Input
                      value={email}
                      onChangeText={setEmail}
                      autoCapitalize="none"
                      keyboardType="email-address"
                      bg="$gray2"
                      borderColor="$gray6"
                      borderRadius="$4"
                      fontSize="$4"
                      p="$3"
                    />
                  </YStack>

                  <YStack gap="$2">
                    <Label color="$gray11" fontSize="$3" fontWeight="600"><Text>Phone Number</Text></Label>
                    <Input
                      value={phone}
                      onChangeText={setPhone}
                      keyboardType="phone-pad"
                      bg="$gray2"
                      borderColor="$gray6"
                      borderRadius="$4"
                      fontSize="$4"
                      p="$3"
                    />
                  </YStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Business Hours */}
          <MotiView
            from={{ opacity: 0, translateX: 30 }}
            animate={{ opacity: 1, translateX: 0 }}
            transition={{ delay: 1000, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#10b981',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="time" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800"><Text>Business Hours</Text></H3>
                </XStack>

                <YStack gap="$3">
                  <XStack gap="$3">
                    <YStack gap="$2" flex={1}>
                      <Label color="$gray11" fontSize="$3" fontWeight="600"><Text>Opening Time</Text></Label>
                      <Pressable
                        onPress={() => {
                          console.log('🕐 Opening time picker clicked');
                          setShowPicker('open');
                        }}
                        style={({ pressed }) => ({
                          opacity: pressed ? 0.7 : 1,
                        })}
                      >
                        <View style={{ position: 'relative' }}>
                          <Input
                            editable={false}
                            value={openTimeString}
                            bg="$gray2"
                            borderColor="$gray6"
                            borderRadius="$4"
                            fontSize="$4"
                            p="$3"
                            pointerEvents="none"
                          />
                          <View style={{
                            position: 'absolute',
                            right: 12,
                            top: '50%',
                            transform: [{ translateY: -10 }],
                            pointerEvents: 'none'
                          }}>
                            <Ionicons name="time-outline" size={20} color="#666" />
                          </View>
                        </View>
                      </Pressable>
                    </YStack>

                    <YStack gap="$2" flex={1}>
                      <Label color="$gray11" fontSize="$3" fontWeight="600"><Text>Closing Time</Text></Label>
                      <Pressable
                        onPress={() => {
                          console.log('🕐 Closing time picker clicked');
                          setShowPicker('close');
                        }}
                        style={({ pressed }) => ({
                          opacity: pressed ? 0.7 : 1,
                        })}
                      >
                        <View style={{ position: 'relative' }}>
                          <Input
                            editable={false}
                            value={closeTimeString}
                            bg="$gray2"
                            borderColor="$gray6"
                            borderRadius="$4"
                            fontSize="$4"
                            p="$3"
                            pointerEvents="none"
                          />
                          <View style={{
                            position: 'absolute',
                            right: 12,
                            top: '50%',
                            transform: [{ translateY: -10 }],
                            pointerEvents: 'none'
                          }}>
                            <Ionicons name="time-outline" size={20} color="#666" />
                          </View>
                        </View>
                      </Pressable>
                    </YStack>
                  </XStack>

                  {showPicker && (
                    <DateTimePicker
                      value={showPicker === 'open' ? timeStringToDate(openTimeString) : timeStringToDate(closeTimeString)}
                      mode="time"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={onTimeChange}
                    />
                  )}

                  <Card bg={storeOpen ? "$green2" : "$red2"} p="$3" br="$4" borderWidth={1} borderColor={storeOpen ? "$green6" : "$red6"}>
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <View
                          style={{
                            width: 12,
                            height: 12,
                            borderRadius: 6,
                            backgroundColor: storeOpen ? '#10b981' : '#ef4444',
                          }}
                        />
                        <YStack>
                          <Text color={storeOpen ? "$green11" : "$red11"} fontSize="$4" fontWeight="700">
                            Store is {storeOpen ? 'Open' : 'Closed'}
                          </Text>
                          <Text color={storeOpen ? "$green9" : "$red9"} fontSize="$2">
                            {storeOpen ? 'Accepting orders' : 'Not accepting orders'}
                          </Text>
                        </YStack>
                      </XStack>
                      <Switch
                        checked={storeOpen ?? false}
                        onCheckedChange={setStoreOpen}
                        backgroundColor={storeOpen ? '$green8' : '$red7'}
                      />
                    </XStack>
                  </Card>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Store Location */}
          <MotiView
            from={{ opacity: 0, translateY: 30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 1200, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f97316',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="location" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800"><Text>Store Location</Text></H3>
                </XStack>

                <Pressable onPress={() => setMapFullscreen(true)}>
                  <Card
                    elevate
                    br="$6"
                    height={200}
                    overflow="hidden"
                    bg="$gray2"
                    borderWidth={2}
                    borderColor="$gray6"
                    pressStyle={{ scale: 0.98 }}
                  >
                    <View style={{ position: 'relative', flex: 1 }}>
                      {/* Mini Map Preview */}
                      <MapView
                        style={{ flex: 1 }}
                        region={{
                          latitude: location[1],
                          longitude: location[0],
                          latitudeDelta: 0.005,
                          longitudeDelta: 0.005,
                        }}
                        scrollEnabled={false}
                        zoomEnabled={false}
                        pitchEnabled={false}
                        rotateEnabled={false}
                        showsUserLocation={false}
                        showsMyLocationButton={false}
                        showsCompass={false}
                        showsScale={false}
                      >
                        <Marker
                          coordinate={{
                            latitude: location[1],
                            longitude: location[0],
                          }}
                        >
                          <View
                            style={{
                              backgroundColor: '#7c3aed',
                              borderRadius: 15,
                              padding: 6,
                              borderWidth: 2,
                              borderColor: 'white',
                            }}
                          >
                            <Ionicons name="storefront" size={16} color="white" />
                          </View>
                        </Marker>
                      </MapView>

                      {/* Overlay with Tap Instruction */}
                      <LinearGradient
                        colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.6)']}
                        style={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          padding: 12,
                        }}
                      >
                        <XStack ai="center" jc="space-between">
                          <YStack>
                            <Text color="white" fontSize="$4" fontWeight="700">
                              Tap to Edit Location
                            </Text>
                            <Text color="white" fontSize="$2" opacity={0.9}>
                              {location[1].toFixed(4)}, {location[0].toFixed(4)}
                            </Text>
                          </YStack>
                          <View
                            style={{
                              backgroundColor: 'rgba(255,255,255,0.2)',
                              borderRadius: 12,
                              padding: 8,
                            }}
                          >
                            <Ionicons name="expand" size={16} color="white" />
                          </View>
                        </XStack>
                      </LinearGradient>
                    </View>
                  </Card>
                </Pressable>
              </YStack>
            </Card>
          </MotiView>

          {/* Additional Settings */}
          <MotiView
            from={{ opacity: 0, translateY: 30 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 1400, duration: 600 }}
          >
            <Card elevate br="$8" p="$5" bg="$background" borderWidth={1} borderColor="$gray4">
              <YStack gap="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#8b5cf6',
                      borderRadius: 12,
                      padding: 10,
                    }}
                  >
                    <Ionicons name="settings" size={20} color="white" />
                  </View>
                  <H3 color="$gray12" fontWeight="800"><Text>Additional Settings</Text></H3>
                </XStack>

                <YStack gap="$3">
                  <Card bg="$gray2" p="$3" br="$4">
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <Ionicons name="notifications" size={20} color="#64748b" />
                        <YStack>
                          <Text color="$gray12" fontSize="$4" fontWeight="600">Push Notifications</Text>
                          <Text color="$gray10" fontSize="$2">Receive order alerts</Text>
                        </YStack>
                      </XStack>
                      <Switch checked={true} backgroundColor="$green8" />
                    </XStack>
                  </Card>

                  {/* {(<Card bg="$gray2" p="$3" br="$4">
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <Ionicons name="car" size={20} color="#64748b" />
                        <YStack>
                          <Text color="$gray12" fontSize="$4" fontWeight="600">Delivery Service</Text>
                          <Text color="$gray10" fontSize="$2">Offer delivery to customers</Text>
                        </YStack>
                      </XStack>
                      <Switch checked={true} backgroundColor="$green8" />
                    </XStack>
                  </Card>)} */}

                  <Card bg="$gray2" p="$3" br="$4">
                    <XStack ai="center" jc="space-between">
                      <XStack ai="center" gap="$3">
                        <Ionicons name="card" size={20} color="#64748b" />
                        <YStack>
                          <Text color="$gray12" fontSize="$4" fontWeight="600">Online Payments</Text>
                          <Text color="$gray10" fontSize="$2">Accept card payments</Text>
                        </YStack>
                      </XStack>
                      <Switch checked={true} backgroundColor="$green8" />
                    </XStack>
                  </Card>

                  {/* Logout Button */}
                  <Card bg="$red2" p="$3" br="$4" borderWidth={1} borderColor="$red6">
                    <Pressable onPress={handleLogout}>
                      <XStack ai="center" gap="$3">
                        <View
                          style={{
                            backgroundColor: '#ef4444',
                            borderRadius: 8,
                            padding: 8,
                          }}
                        >
                          <Ionicons name="log-out" size={20} color="white" />
                        </View>
                        <YStack flex={1}>
                          <Text color="$red11" fontSize="$4" fontWeight="600">Logout</Text>
                          <Text color="$red10" fontSize="$2">Sign out of your account</Text>
                        </YStack>
                        <Ionicons name="chevron-forward" size={16} color="#ef4444" />
                      </XStack>
                    </Pressable>
                  </Card>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Save Button */}
          <MotiView
            from={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1600, duration: 600, type: 'spring' }}
          >
            <Button
              size="$5"
              br="$8"
              bg="transparent"
              borderWidth={0}
              pressStyle={{ scale: 0.98 }}
              mt="$4"
              onPress={handleSaveProfile}
              disabled={isSaving}
              opacity={isSaving ? 0.7 : 1}
            >
              <LinearGradient
                colors={['#7c3aed', '#a855f7']}
                style={{
                  borderRadius: 16,
                  padding: 16,
                  width: '100%',
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  gap: 12,
                }}
              >
                <Ionicons name={isSaving ? "hourglass" : "save"} size={24} color="white" />
                <Text color="white" fontSize="$5" fontWeight="800">
                  {isSaving ? 'Saving...' : 'Save All Changes'}
                </Text>
              </LinearGradient>
            </Button>
          </MotiView>
        </YStack>
      </ScrollView>

      {/* Enhanced Map Modal */}
      <Modal visible={mapFullscreen} animationType="slide" presentationStyle="fullScreen">
        <View style={{ flex: 1, backgroundColor: '#000' }}>
          {/* Header */}
          <LinearGradient
            colors={['#7c3aed', '#a855f7']}
            style={{
              paddingTop: 50,
              paddingBottom: 20,
              paddingHorizontal: 20,
            }}
          >
            <XStack ai="center" jc="space-between">
              <YStack>
                <Text color="white" fontSize="$5" fontWeight="800">
                  Set Store Location
                </Text>
                <Text color="white" fontSize="$3" opacity={0.9}>
                  Tap on the map to set your store location
                </Text>
              </YStack>
              <Button
                bg="rgba(255,255,255,0.2)"
                color="white"
                br="$10"
                size="$4"
                icon={<Ionicons name="close" size={20} color="white" />}
                onPress={() => setMapFullscreen(false)}
              />
            </XStack>
          </LinearGradient>

          {/* Map */}
          <MapView
            style={{ flex: 1 }}
            initialRegion={{
              latitude: location[1],
              longitude: location[0],
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
            onPress={(e) => {
              const { latitude, longitude } = e.nativeEvent.coordinate;
              setLocation([longitude, latitude]);
            }}
            showsUserLocation={true}
            showsMyLocationButton={true}
            showsCompass={true}
            showsScale={true}
          >
            <Marker
              coordinate={{
                latitude: location[1],
                longitude: location[0],
              }}
              title="Store Location"
              description="Your store is located here"
            >
              <View
                style={{
                  backgroundColor: '#7c3aed',
                  borderRadius: 20,
                  padding: 8,
                  borderWidth: 3,
                  borderColor: 'white',
                }}
              >
                <Ionicons name="storefront" size={24} color="white" />
              </View>
            </Marker>
          </MapView>

          {/* Bottom Info Panel */}
          <LinearGradient
            colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.9)']}
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              padding: 20,
            }}
          >
            <Card bg="rgba(255,255,255,0.1)" p="$4" br="$6" borderWidth={1} borderColor="rgba(255,255,255,0.2)">
              <YStack gap="$3">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#7c3aed',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="location" size={20} color="white" />
                  </View>
                  <YStack flex={1}>
                    <Text color="white" fontSize="$4" fontWeight="700">
                      Current Location
                    </Text>
                    <Text color="white" fontSize="$3" opacity={0.8}>
                      Latitude: {location[1].toFixed(6)}
                    </Text>
                    <Text color="white" fontSize="$3" opacity={0.8}>
                      Longitude: {location[0].toFixed(6)}
                    </Text>
                  </YStack>
                </XStack>

                <Button
                  bg="$green9"
                  color="white"
                  br="$6"
                  size="$4"
                  icon={<Ionicons name="checkmark" size={20} color="white" />}
                  onPress={() => setMapFullscreen(false)}
                >
                  <Text color="white" fontSize="$4" fontWeight="700">
                    Confirm Location
                  </Text>
                </Button>
              </YStack>
            </Card>
          </LinearGradient>
        </View>
      </Modal>
    </>
  )
}
