import { create } from 'zustand';
import { getSupplierProducts, getSupplierProductCategories, createSupplierProductCategory, updateSupplierProductCategory, deleteSupplierProductCategory } from '../services/api';

type Category = {
  _id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  isActive: boolean;
};

type CategoryStore = {
  categories: Category[];
  selectedCategory: string | null;
  loadCategories: (supplierId: string) => Promise<void>;
  addCategory: (cat: string) => Promise<{ success: boolean; message: string }>;
  deleteCategory: (cat: string) => Promise<{ success: boolean; message: string }>;
  selectCategory: (cat: string | null) => void;
  renameCategory: (oldCat: string, newCat: string, updateProducts?: (oldCat: string, newCat: string) => void) => Promise<{ success: boolean; message: string }>;
  supplierId: string | null;
  setSupplierId: (id: string) => void;
}

export const useSupplierCategories = create<CategoryStore>((set, get) => ({
  categories: [],
  selectedCategory: null,
  supplierId: null,
  
  setSupplierId: (id: string) => set({ supplierId: id }),
  
  loadCategories: async (supplierId: string) => {
    try {
      // First try to load from supplier product categories
      const categoriesResponse = await getSupplierProductCategories(supplierId);
      if (categoriesResponse.success && categoriesResponse.data) {
        set({ categories: categoriesResponse.data });
        return;
      }
      
      // Fallback to loading from products (for backward compatibility)
      const response = await getSupplierProducts(supplierId);
      if (response.success && response.data) {
        // Get unique categories from products and create mock category objects
        const productCategories = response.data.products.reduce<Category[]>((acc: Category[], product: any) => {
          if (product.categoryName && !acc.find(cat => cat.name === product.categoryName)) {
            acc.push({
              _id: product.categoryId || `temp-${product.categoryName}`,
              name: product.categoryName,
              isActive: true
            });
          }
          return acc;
        }, []);

        set({ categories: productCategories });
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      // Fallback to empty categories
      set({ categories: [] });
    }
  },
  
  addCategory: async (cat: string) => {
    try {
      const trimmedCat = cat.trim();
      const currentState = get();
      
      // Check if supplierId is set
      if (!currentState.supplierId) {
        return { success: false, message: 'Supplier ID not set. Please refresh the page.' };
      }
      
      // Check if category already exists locally
      if (currentState.categories.find(c => c.name === trimmedCat)) {
        return { success: false, message: 'Category already exists' };
      }

      // Save to backend
      const response = await createSupplierProductCategory(currentState.supplierId, {
        name: trimmedCat,
        color: '#3B82F6', // Default blue color
        icon: 'tag' // Default icon
      });

      if (response.success) {
        // Update local state with the new category object
        const newCategory: Category = {
          _id: response.data._id || response.data.id,
          name: trimmedCat,
          color: '#3B82F6',
          icon: 'tag',
          isActive: true
        };
        
        set((s) => ({
          categories: [...s.categories, newCategory]
        }));
        return { success: true, message: 'Category added successfully' };
      } else {
        return { success: false, message: response.message || 'Failed to add category' };
      }
    } catch (error) {
      console.error('Error adding category:', error);
      return { success: false, message: 'Failed to add category' };
    }
  },
    
  deleteCategory: async (cat: string) => {
    try {
      const currentState = get();
      
      // Check if supplierId is set
      if (!currentState.supplierId) {
        return { success: false, message: 'Supplier ID not set. Please refresh the page.' };
      }
      
      // Don't allow deleting 'All' category
      if (cat === 'All') {
        return { success: false, message: 'Cannot delete default category' };
      }

      // Find the category in the backend to get its ID
      const categoriesResponse = await getSupplierProductCategories(currentState.supplierId);
      if (categoriesResponse.success && categoriesResponse.data) {
        const categoryToDelete = categoriesResponse.data.find((catItem: any) => catItem.name === cat);
        
        if (categoryToDelete) {
          // Delete from backend
          const deleteResponse = await deleteSupplierProductCategory(categoryToDelete._id || categoryToDelete.id);
          
          if (deleteResponse.success) {
            // Update local state
            set((s) => {
              const newCategories = s.categories.filter((c) => c.name !== cat);
              return {
                categories: newCategories,
                selectedCategory: s.selectedCategory === cat ? null : s.selectedCategory,
              };
            });
            return { success: true, message: 'Category deleted successfully' };
          } else {
            return { success: false, message: deleteResponse.message || 'Failed to delete category from backend' };
          }
        }
      }
      
      // If category not found in backend, just remove from local state
      set((s) => {
        const newCategories = s.categories.filter((c) => c.name !== cat);
        return {
          categories: newCategories,
          selectedCategory: s.selectedCategory === cat ? null : s.selectedCategory,
        };
      });

      return { success: true, message: 'Category deleted successfully' };
    } catch (error) {
      console.error('Error deleting category:', error);
      return { success: false, message: 'Failed to delete category' };
    }
  },
    
  selectCategory: (cat) => set(() => ({ selectedCategory: cat })),
  
  renameCategory: async (oldCat: string, newCat: string, updateProducts?: (oldCat: string, newCat: string) => void) => {
    try {
      const currentState = get();
      
      // Check if supplierId is set
      if (!currentState.supplierId) {
        return { success: false, message: 'Supplier ID not set. Please refresh the page.' };
      }
      
      // Don't allow renaming 'All' category
      if (oldCat === 'All') {
        return { success: false, message: 'Cannot rename default category' };
      }
      
      const trimmedNewCat = newCat.trim();
      if (!trimmedNewCat) {
        return { success: false, message: 'Category name cannot be empty' };
      }
      
      if (currentState.categories.find(c => c.name === trimmedNewCat)) {
        return { success: false, message: 'Category name already exists' };
      }

      // Find the category in the backend to get its ID
      const categoriesResponse = await getSupplierProductCategories(currentState.supplierId);
      if (categoriesResponse.success && categoriesResponse.data) {
        const categoryToUpdate = categoriesResponse.data.find((catItem: any) => catItem.name === oldCat);
        
        if (categoryToUpdate) {
          // Update in backend
          const updateResponse = await updateSupplierProductCategory(categoryToUpdate._id || categoryToUpdate.id, {
            name: trimmedNewCat
          });
          
          if (updateResponse.success) {
            // Update local state
            set((s) => {
              const newCategories = s.categories.map((c) =>
                c.name === oldCat ? { ...c, name: trimmedNewCat } : c
              );
              return { 
                categories: newCategories,
                selectedCategory: s.selectedCategory === oldCat ? trimmedNewCat : s.selectedCategory
              };
            });

            // Update products if callback is provided
            if (updateProducts) {
              updateProducts(oldCat, trimmedNewCat);
            }

            return { success: true, message: 'Category renamed successfully' };
          } else {
            return { success: false, message: updateResponse.message || 'Failed to rename category in backend' };
          }
        }
      }
      
      // If category not found in backend, just update local state
      set((s) => {
        const newCategories = s.categories.map((c) =>
          c.name === oldCat ? { ...c, name: trimmedNewCat } : c
        );
        return { 
          categories: newCategories,
          selectedCategory: s.selectedCategory === oldCat ? trimmedNewCat : s.selectedCategory
        };
      });

      // Update products if callback is provided
      if (updateProducts) {
        updateProducts(oldCat, trimmedNewCat);
      }

      return { success: true, message: 'Category renamed successfully' };
    } catch (error) {
      console.error('Error renaming category:', error);
      return { success: false, message: 'Failed to rename category' };
    }
  },
}));
