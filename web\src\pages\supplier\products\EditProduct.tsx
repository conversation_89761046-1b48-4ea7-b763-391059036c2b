import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Edit3,
  Save,
  X,
  Image as ImageIcon,
  Tag,
  DollarSign,
  Star,
  AlertCircle,
  CheckCircle2,
  ArrowLeft,
  Camera,
  Upload,
  Trash2,
  Settings,
  Package,
  Grid3X3,
  List,
  BarChart3,
  Plus,
  ChevronRight,
  Sparkles,
  Target,
  TrendingUp,
  Award,
  Crown,
  Zap,
  Coffee,
  Shirt,
  Utensils,
  Layers,
  Hash,
  Type,
  ToggleLeft,
  ToggleRight,
  Calendar,
  Clock,
  Store
} from 'lucide-react';
import { SafeImage } from '../../../components/common/SafeImage';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';
import { useSupplierProductsStore, type Product } from '../../../stores/supplierProductsStore';
import { useSupplierCategories } from '../../../stores/supplierCategoriesStore';

// Import enhanced error handling
import { EnhancedErrorDisplay } from '../../../components/common/EnhancedErrorDisplay';
import type { ErrorInfo } from '../../../utils/errorHandler';
import { detectErrorType, getErrorMessage } from '../../../utils/errorHandler';

// Import the Addition type from the store
type Addition = {
  id: string;
  name: string;
  price: number;
};

// Modern Glass Card Component
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06,
      zIndex: -1,
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

// Enhanced Input Component
const EnhancedInput: React.FC<{
  label: string;
  placeholder: string;
  value: string | number;
  onChange: (value: string) => void;
  error?: string;
  type?: 'text' | 'number' | 'textarea';
  icon?: React.ComponentType<any>;
  required?: boolean;
  multiline?: boolean;
  rows?: number;
  detectTextDirection?: (text: string) => 'ltr' | 'rtl';
  getTextDirectionClasses?: (text: string) => string;
}> = ({ label, placeholder, value, onChange, error, type = 'text', icon: Icon, required = false, multiline = false, rows = 3, detectTextDirection, getTextDirectionClasses }) => (
  <div className="space-y-3">
    <div className="flex items-center gap-2">
      {Icon && (
        <div className="p-2 bg-white/20 rounded-xl border border-white/30">
          <Icon size={16} className="text-white/90" />
        </div>
      )}
      <label className="text-white/90 text-sm font-semibold">
        {label}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>
    </div>
    
    {multiline ? (
      <textarea
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        rows={rows}
        dir={detectTextDirection ? detectTextDirection(String(value)) : 'ltr'}
        className={`w-full px-4 py-3 bg-white/10 border rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all resize-none ${
          error ? 'border-red-400/60' : 'border-white/20'
        } ${getTextDirectionClasses ? getTextDirectionClasses(String(value)) : ''}`}
      />
    ) : (
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        dir={type === 'text' && detectTextDirection ? detectTextDirection(String(value)) : 'ltr'}
        className={`w-full px-4 py-3 bg-white/10 border rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all ${
          error ? 'border-red-400/60' : 'border-white/20'
        } ${type === 'text' && getTextDirectionClasses ? getTextDirectionClasses(String(value)) : ''}`}
      />
    )}
    
    {error && (
      <motion.p
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-red-300 text-sm font-medium flex items-center gap-2"
      >
        <AlertCircle size={14} />
        {error}
      </motion.p>
    )}
  </div>
);

// Enhanced Select Component
const EnhancedSelect: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder: string;
  error?: string;
  icon?: React.ComponentType<any>;
  required?: boolean;
}> = ({ label, value, onChange, options, placeholder, error, icon: Icon, required = false }) => (
  <div className="space-y-3">
    <div className="flex items-center gap-2">
      {Icon && (
        <div className="p-2 bg-white/20 rounded-xl border border-white/30">
          <Icon size={16} className="text-white/90" />
        </div>
      )}
      <label className="text-white/90 text-sm font-semibold">
        {label}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>
    </div>
    
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={`w-full px-4 py-3 bg-white/10 border rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all ${
        error ? 'border-red-400/60' : 'border-white/20'
      }`}
    >
      <option value="" className="bg-gray-800 text-white">
        {placeholder}
      </option>
      {options.map((option) => (
        <option key={option} value={option} className="bg-gray-800 text-white">
          {option}
        </option>
      ))}
    </select>
    
    {error && (
      <motion.p
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-red-300 text-sm font-medium flex items-center gap-2"
      >
        <AlertCircle size={14} />
        {error}
      </motion.p>
    )}
  </div>
);

// Enhanced Toggle Component
const EnhancedToggle: React.FC<{
  label: string;
  description: string;
  value: boolean;
  onChange: (value: boolean) => void;
  icon?: React.ComponentType<any>;
}> = ({ label, description, value, onChange, icon: Icon }) => (
  <div className="p-6 bg-white/10 rounded-2xl border border-white/20">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        {Icon && (
          <div className="p-2 bg-white/20 rounded-xl border border-white/30">
            <Icon size={16} className="text-white/90" />
          </div>
        )}
        <div>
          <p className="text-white font-semibold text-base">{label}</p>
          <p className="text-white/70 text-sm">{description}</p>
        </div>
      </div>
      
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => onChange(!value)}
        className={`relative p-2 rounded-xl border transition-all ${
          value 
            ? 'bg-orange-500 border-white/30 text-white' 
            : 'bg-transparent border-orange-400/40 text-orange-400'
        }`}
      >
        <Star size={20} />
      </motion.button>
    </div>
  </div>
);

// Enhanced Image Upload Component
const EnhancedImageUpload: React.FC<{
  image: string;
  onImageChange: (image: string) => void;
  error?: string;
  loading?: boolean;
}> = ({ image, onImageChange, error, loading = false }) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onImageChange(result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <div className="p-2 bg-white/20 rounded-xl border border-white/30">
          <ImageIcon size={16} className="text-white/90" />
        </div>
        <label className="text-white/90 text-sm font-semibold">
          Product Image
          <span className="text-red-400 ml-1">*</span>
        </label>
      </div>
      
      <div className="relative">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
          id="image-upload"
        />
        
        <label
          htmlFor="image-upload"
          className={`block cursor-pointer transition-all ${
            error ? 'border-red-400/60' : 'border-white/20'
          }`}
        >
          <div className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all hover:border-white/40 ${
            error ? 'border-red-400/60 bg-red-500/10' : 'border-white/20 bg-white/5'
          }`}>
            {loading ? (
              <div className="flex flex-col items-center gap-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                <p className="text-white/70 text-sm">Uploading image...</p>
              </div>
            ) : image ? (
              <div className="space-y-4">
                <SafeImage
                  src={image}
                  alt="Product preview"
                  className="w-32 h-32 rounded-2xl object-cover mx-auto border-2 border-white/30"
                  fallbackIcon={
                    <div className="flex flex-col items-center justify-center text-white/60 w-32 h-32">
                      <ImageIcon size={32} />
                      <span className="text-xs mt-1">No Image</span>
                    </div>
                  }
                />
                <div>
                  <p className="text-white font-semibold text-base">Tap to change image</p>
                  <p className="text-white/60 text-sm">Recommended: Square image, at least 400x400px</p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="mx-auto w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center">
                  <Upload size={32} className="text-white/60" />
                </div>
                <div>
                  <p className="text-white font-semibold text-base">Tap to upload product image</p>
                  <p className="text-white/60 text-sm">Recommended: Square image, at least 400x400px</p>
                </div>
              </div>
            )}
          </div>
        </label>
      </div>
      
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-red-300 text-sm font-medium flex items-center gap-2"
        >
          <AlertCircle size={14} />
          {error}
        </motion.p>
      )}
    </div>
  );
};

const EditProductPage: React.FC = () => {
  const navigate = useNavigate();
  const { productId } = useParams<{ productId: string }>();
  const { user } = useCurrentUserData();
  const { t, i18n } = useTranslation();

  // Utility function to detect Arabic text and determine direction
  const detectTextDirection = (text: string): 'ltr' | 'rtl' => {
    if (!text) return i18n.language === 'ar' ? 'rtl' : 'ltr';

    // Arabic Unicode range: \u0600-\u06FF, \u0750-\u077F, \u08A0-\u08FF, \uFB50-\uFDFF, \uFE70-\uFEFF
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicRegex.test(text) ? 'rtl' : 'ltr';
  };

  // Get dynamic text direction classes
  const getTextDirectionClasses = (text: string): string => {
    const direction = detectTextDirection(text);
    return direction === 'rtl' ? 'text-right dir-rtl' : 'text-left dir-ltr';
  };
  const {
    products,
    updateProduct,
    loadProducts,
    setSupplierId,
    isLoading: storeLoading,
    error: storeError
  } = useSupplierProductsStore();
  const { categories, loadCategories, setSupplierId: setCategorySupplierId } = useSupplierCategories();

  // Enhanced state management - Initialize with existing product data
  const [product, setProduct] = useState({
    name: "",
    price: 0,
    discountPrice: 0,
    image: "",
    categoryId: "",
    categoryName: "",
    description: "",
    isAvailable: true,
    restaurantOptions: {
      additions: [] as Addition[],
      without: [] as string[],
      sides: [] as Addition[],
    },
    clothingOptions: {
      sizes: [] as string[],
      colors: [] as string[],
      gallery: [] as string[],
    },
    customOptions: [] as {
      id: string;
      title: string;
      type: 'text' | 'number' | 'select' | 'multi-select';
      values?: string[];
    }[],
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(true);
  const [existingProduct, setExistingProduct] = useState<Product | null>(null);
  const [apiError, setApiError] = useState<ErrorInfo | null>(null);
  const [categoriesError, setCategoriesError] = useState<ErrorInfo | null>(null);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [errorType, setErrorType] = useState<string>('unknown');

  // Load existing product data when component mounts
  useEffect(() => {
    const initializeProduct = async () => {
      console.log('🔄 EditProduct useEffect triggered with productId:', productId);

      if (!productId) {
        console.log('⚠️ No productId provided');
        setIsLoading(false);
        return;
      }

      if (!user?.supplierId) {
        console.log('⚠️ No supplier ID found');
        setIsLoading(false);
        return;
      }

      try {
        // Initialize supplier ID and load products and categories
        setSupplierId(user.supplierId);
        setCategorySupplierId(user.supplierId);
        setCategoriesLoading(true);

        await Promise.all([
          loadProducts(),
          loadCategories(user.supplierId).catch((error) => {
            console.error('Failed to load categories:', error);
            const errorType = detectErrorType(error);
            setErrorType(errorType);

            let errorMessage = getErrorMessage(errorType);
            if (errorType === 'network') {
              errorMessage = 'Failed to load categories. Please check your connection and try again.';
            } else if (errorType === 'server') {
              errorMessage = 'Categories service is temporarily unavailable. Please try again later.';
            } else if (errorType === 'auth') {
              errorMessage = 'Authentication failed. Please log in again.';
            }

            setCategoriesError({
              type: errorType,
              message: errorMessage,
              userFriendlyMessage: errorMessage,
              statusCode: error.status || 500,
              backendMessage: error.message || 'Failed to load categories'
            });
          })
        ]);

        setCategoriesLoading(false);

        // Find the product in the store
        const foundProduct = products.find(p => p.id === productId);
        console.log('📦 Found product:', foundProduct);

        if (foundProduct) {
          console.log('✅ Product found, setting state...');
          setExistingProduct(foundProduct);
          setProduct({
            name: foundProduct.name || "",
            price: foundProduct.price || 0,
            discountPrice: foundProduct.discountPrice || 0,
            image: foundProduct.image || "",
            categoryId: foundProduct.categoryId || "",
            categoryName: foundProduct.categoryName || "",
            description: foundProduct.description || "",
            isAvailable: foundProduct.isAvailable ?? true,
            restaurantOptions: {
              additions: foundProduct.restaurantOptions?.additions || [],
              without: foundProduct.restaurantOptions?.without || [],
              sides: foundProduct.restaurantOptions?.sides || [],
            },
            clothingOptions: {
              sizes: foundProduct.clothingOptions?.sizes || [],
              colors: foundProduct.clothingOptions?.colors || [],
              gallery: foundProduct.clothingOptions?.gallery || [],
            },
            customOptions: foundProduct.customizations || [],
          });
        } else {
          console.log('❌ Product not found');
          setApiError({
            type: 'notFound',
            message: 'Product not found',
            userFriendlyMessage: 'The product you are trying to edit was not found.',
            statusCode: 404
          });
        }
      } catch (error) {
        console.error('❌ Error loading product:', error);

        // Use enhanced error detection
        const errorType = detectErrorType(error);
        setErrorType(errorType);

        // Provide specific error messages based on error type
        let errorMessage = getErrorMessage(errorType);
        if (errorType === 'network') {
          errorMessage = 'Connection failed. Please check your internet and try again.';
        } else if (errorType === 'server') {
          errorMessage = 'Server is temporarily unavailable. Please try again in a few minutes.';
        } else if (errorType === 'auth') {
          errorMessage = 'Your session has expired. Please log in again.';
          // Redirect to login after showing error
          setTimeout(() => navigate('/authentication/login'), 3000);
        } else if (errorType === 'notFound') {
          errorMessage = 'The product you are trying to edit was not found.';
        } else if (errorType === 'forbidden') {
          errorMessage = 'You do not have permission to edit this product.';
        }

        setApiError({
          type: errorType,
          message: errorMessage,
          userFriendlyMessage: errorMessage,
          statusCode: (error as any)?.status || (error as any)?.statusCode || 500,
          backendMessage: (error as any)?.message || 'Failed to load product data'
        });
        setCategoriesLoading(false);
      } finally {
        setIsLoading(false);
      }
    };

    initializeProduct();
  }, [productId, user?.supplierId]);

  // Form validation
  const validateField = useCallback((field: string, value: any) => {
    const newErrors = { ...errors };

    switch (field) {
      case 'name':
        if (!value || value.trim().length < 2) {
          newErrors.name = 'Product name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          newErrors.name = 'Product name must be less than 50 characters';
        } else {
          delete newErrors.name;
        }
        break;
      case 'price':
        if (!value || isNaN(value) || value <= 0) {
          newErrors.price = 'Please enter a valid price';
        } else if (value > 10000) {
          newErrors.price = 'Price seems too high (max: 10,000)';
        } else {
          delete newErrors.price;
        }
        break;
      case 'categoryId':
        if (!value) {
          newErrors.categoryId = 'Please select a category';
        } else {
          delete newErrors.categoryId;
        }
        break;
      case 'image':
        if (!value) {
          newErrors.image = 'Please upload a product image';
        } else {
          delete newErrors.image;
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [errors]);

  // Enhanced form validation
  const validateForm = useCallback(() => {
    const fields = ['name', 'price', 'categoryId', 'image'];
    let isValid = true;

    fields.forEach(field => {
      const fieldValue = field === 'price' ? product.price : product[field as keyof typeof product];
      if (!validateField(field, fieldValue)) {
        isValid = false;
      }
    });

    return isValid;
  }, [product, validateField]);

  // Enhanced submit handler
  const handleSubmit = useCallback(async () => {
    // Clear any existing errors
    setApiError(null);
    setErrorType('unknown');

    // Validate all fields with detailed feedback
    const nameValid = validateField('name', product.name);
    const priceValid = validateField('price', product.price);
    const categoryValid = filteredCategories.length === 0 ? true : validateField('categoryId', product.categoryId);
    const imageValid = validateField('image', product.image);

    if (!nameValid || !priceValid || !categoryValid || !imageValid) {
      // Show validation error summary
      const validationErrors = [];
      if (!nameValid) validationErrors.push({ field: 'name', message: 'Product name is required and must be at least 2 characters' });
      if (!priceValid) validationErrors.push({ field: 'price', message: 'Price must be a positive number' });
      if (!categoryValid && filteredCategories.length > 0) validationErrors.push({ field: 'category', message: 'Please select a category' });
      if (!imageValid) validationErrors.push({ field: 'image', message: 'Product image is required' });

      setApiError({
        type: 'validation',
        message: `Please complete all required fields: ${validationErrors.map(e => e.field).join(', ')}.`,
        userFriendlyMessage: `Please complete all required fields: ${validationErrors.map(e => e.field).join(', ')}.`,
        statusCode: 400,
        validationErrors
      });
      setErrorType('validation');

      // Scroll to the first error field for better UX
      setTimeout(() => {
        const firstErrorField = document.querySelector('input[class*="border-red-400"], select[class*="border-red-400"]');
        if (firstErrorField) {
          firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);

      return;
    }

    if (!existingProduct) {
      setApiError({
        type: 'notFound',
        message: 'Product not found',
        userFriendlyMessage: 'The product you are trying to edit was not found.',
        statusCode: 404
      });
      return;
    }

    if (!user?.supplierId) {
      setApiError({
        type: 'auth',
        message: 'No supplier ID found. Please log in again.',
        userFriendlyMessage: 'No supplier ID found. Please log in again.',
        statusCode: 401
      });
      setErrorType('auth');
      return;
    }

    setIsLoading(true);

    try {
      console.log('📦 Updating product:', product);

      // Prepare the update data with proper validation
      const updateData = {
        name: product.name.trim(),
        price: Number(product.price),
        discountPrice: product.discountPrice ? Number(product.discountPrice) : undefined,
        image: product.image,
        categoryId: product.categoryId.trim(),
        categoryName: product.categoryName.trim(),
        description: product.description ? product.description.trim() : undefined,
        isAvailable: Boolean(product.isAvailable),
        restaurantOptions: product.restaurantOptions || {
          additions: [],
          without: [],
          sides: []
        },
        clothingOptions: product.clothingOptions || {
          sizes: [],
          colors: [],
          gallery: []
        },
        customizations: product.customOptions || []
      };

      console.log('📤 Sending update data:', updateData);

      // Update product using backend API
      await updateProduct(existingProduct.id, updateData);

      // Navigate back immediately on success
      navigate('/supplier/products');
    } catch (error: any) {
      console.error('❌ Error updating product:', error);

      // Use enhanced error detection
      const errorType = detectErrorType(error);
      setErrorType(errorType);

      // Provide more specific error messages based on error type
      let errorMessage = getErrorMessage(errorType);
      let backendMessage = '';

      // Extract backend message if available
      if (error.response?.data?.message) {
        backendMessage = error.response.data.message;
      } else if (error.response?.data?.error) {
        backendMessage = error.response.data.error;
      } else if (error.message) {
        backendMessage = error.message;
      }

      // Log the full error for debugging
      console.error('🔍 Full error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Customize messages based on error type
      if (errorType === 'network') {
        errorMessage = 'Connection failed. Please check your internet and try again.';
      } else if (errorType === 'server') {
        errorMessage = 'Server is temporarily unavailable. Please try again in a few minutes.';
      } else if (errorType === 'auth') {
        errorMessage = 'Your session has expired. Please log in again.';
        // Redirect to login after showing error
        setTimeout(() => navigate('/authentication/login'), 3000);
      } else if (errorType === 'validation') {
        errorMessage = 'Please check your product information and try again.';
      } else if (errorType === 'conflict') {
        errorMessage = 'A product with this name already exists. Please choose a different name.';
      } else if (errorType === 'forbidden') {
        errorMessage = 'You do not have permission to update this product.';
      } else if (errorType === 'notFound') {
        errorMessage = 'The product you are trying to update was not found.';
      }

      // Extract validation errors if available
      let validationErrors;
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        validationErrors = error.response.data.errors.map((err: any) => ({
          field: err.field || err.param || 'unknown',
          message: err.message || err.msg || 'Validation error'
        }));
      }

      setApiError({
        type: errorType,
        message: errorMessage,
        userFriendlyMessage: errorMessage,
        statusCode: error.response?.status || error.status || error.statusCode || 500,
        backendMessage: backendMessage || 'Failed to update product',
        validationErrors
      });
    } finally {
      setIsLoading(false);
    }
  }, [product, validateForm, updateProduct, existingProduct, navigate]);

  // Retry functions for error handling
  const retryLoadCategories = () => {
    setCategoriesError(null);
    if (user?.supplierId) {
      setCategoriesLoading(true);
      loadCategories(user.supplierId)
        .then(() => {
          setCategoriesLoading(false);
        })
        .catch((error) => {
          console.error('Failed to load categories:', error);
          const errorType = detectErrorType(error);
          setErrorType(errorType);
          setCategoriesLoading(false);

          // Provide specific error messages for category loading
          let errorMessage = getErrorMessage(errorType);
          if (errorType === 'network') {
            errorMessage = 'Failed to load categories. Please check your connection and try again.';
          } else if (errorType === 'server') {
            errorMessage = 'Categories service is temporarily unavailable. Please try again later.';
          } else if (errorType === 'auth') {
            errorMessage = 'Authentication failed. Please log in again.';
            setTimeout(() => navigate('/authentication/login'), 3000);
          }

          setCategoriesError({
            type: errorType,
            message: errorMessage,
            userFriendlyMessage: errorMessage,
            statusCode: (error as any)?.status || 500,
            backendMessage: (error as any)?.message || 'Failed to load categories'
          });
        });
    } else {
      setCategoriesError({
        type: 'auth',
        message: 'No supplier ID found. Please contact support.',
        userFriendlyMessage: 'No supplier ID found. Please contact support.',
        statusCode: 403
      });
    }
  };

  const retryOperation = () => {
    setApiError(null);
    setErrorType('unknown');
    // Retry the product update
    handleSubmit();
  };

  // Clear API errors when user makes changes
  const clearApiErrors = () => {
    if (apiError) {
      setApiError(null);
      setErrorType('unknown');
    }
  };

  // Enhanced categories with better organization
  const filteredCategories = categories.filter((cat) => cat.name !== "All").map(cat => cat.name);

  // Show loading state while fetching product
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-6"></div>
          <h2 className="text-white text-2xl font-bold mb-2">Loading Product...</h2>
          <p className="text-white/60">Please wait while we fetch your product details.</p>
        </div>
      </div>
    );
  }

  // Show product not found after loading is complete
  if (!isLoading && !existingProduct && !apiError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle size={80} className="text-white/40 mx-auto mb-6" />
          <h2 className="text-white text-2xl font-bold mb-2">Product not found</h2>
          <p className="text-white/60 mb-6">
            The product you're trying to edit could not be found.
          </p>
          <button
            onClick={() => navigate('/supplier/products')}
            className="px-6 py-3 bg-white/15 text-white rounded-xl border border-white/30 hover:bg-white/25 transition-all"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }

        /* RTL Support */
        .dir-rtl {
          direction: rtl;
          text-align: right;
        }
        .dir-ltr {
          direction: ltr;
          text-align: left;
        }

        /* Ensure proper text alignment for RTL */
        input[dir="rtl"], textarea[dir="rtl"] {
          text-align: right;
        }
        input[dir="ltr"], textarea[dir="ltr"] {
          text-align: left;
        }

        /* Placeholder text alignment for RTL */
        input[dir="rtl"]::placeholder, textarea[dir="rtl"]::placeholder {
          text-align: right;
        }
        input[dir="ltr"]::placeholder, textarea[dir="ltr"]::placeholder {
          text-align: left;
        }
      `}</style>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container */}
        <div className="relative min-h-screen w-full p-8 pb-24">
          <div className="w-full max-w-4xl mx-auto space-y-8">

            {/* Error Displays */}
            {categoriesError && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <EnhancedErrorDisplay
                  error={categoriesError}
                  onRetry={retryLoadCategories}
                  onDismiss={() => setCategoriesError(null)}
                  variant="floating"
                />
              </motion.div>
            )}

            {apiError && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <EnhancedErrorDisplay
                  error={apiError}
                  onRetry={retryOperation}
                  onDismiss={() => setApiError(null)}
                  variant="floating"
                />
              </motion.div>
            )}

            {/* Enhanced Header Section */}
            <GlassCard gradient="from-white/25 to-white/15" className="p-8">
              <div className="flex items-center gap-6">
                {/* Enhanced Store Icon */}
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                  className="relative"
                >
                  <motion.div
                    className="relative p-4 bg-white/25 border border-white/40 rounded-2xl"
                    whileHover={{ scale: 1.05, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <Edit3 size={32} className="text-white" />
                  </motion.div>
                </motion.div>

                {/* Enhanced Welcome Text */}
                <div className="flex-1">
                  <motion.h1
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-white text-3xl font-black mb-2 bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent"
                  >
                    Edit Product
                  </motion.h1>
                  <motion.p
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-white/80 text-base"
                  >
                    Update your product information
                  </motion.p>
                </div>

                {/* Back Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => navigate('/supplier/products')}
                  className="p-4 bg-white/15 border border-white/30 rounded-2xl text-white hover:bg-white/25 transition-all"
                >
                  <ArrowLeft size={24} />
                </motion.button>
              </div>
            </GlassCard>

            {/* Enhanced Form Card */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-8">
              <div className="space-y-8">
                {/* Product Name Section */}
                <EnhancedInput
                  label="Product Name"
                  placeholder="Enter product name..."
                  value={product.name}
                  onChange={(text) => {
                    setProduct(prev => ({ ...prev, name: text }));
                    validateField('name', text);
                    clearApiErrors();
                  }}
                  error={errors.name}
                  icon={Tag}
                  required
                  detectTextDirection={detectTextDirection}
                  getTextDirectionClasses={getTextDirectionClasses}
                />

                {/* Price Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-white/20 rounded-xl border border-white/30">
                      <DollarSign size={16} className="text-white/90" />
                    </div>
                    <label className="text-white/90 text-sm font-semibold">
                      Price
                      <span className="text-red-400 ml-1">*</span>
                    </label>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-white/70 text-sm">Regular Price (₪)</label>
                      <input
                        type="number"
                        placeholder="0.00"
                        value={product.price}
                        onChange={(e) => {
                          const price = parseFloat(e.target.value) || 0;
                          setProduct(prev => ({ ...prev, price }));
                          validateField('price', price);
                          clearApiErrors();
                        }}
                        className={`w-full px-4 py-3 bg-white/10 border rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all ${
                          errors.price ? 'border-red-400/60' : 'border-white/20'
                        }`}
                      />
                      {errors.price && (
                        <p className="text-red-300 text-sm font-medium flex items-center gap-2">
                          <AlertCircle size={14} />
                          {errors.price}
                        </p>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-white/70 text-sm">Discount Price (₪)</label>
                      <input
                        type="number"
                        placeholder="0.00"
                        value={product.discountPrice}
                        onChange={(e) => {
                          const discountPrice = parseFloat(e.target.value) || 0;
                          setProduct(prev => ({ ...prev, discountPrice }));
                          clearApiErrors();
                        }}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all"
                      />
                    </div>
                  </div>
                </div>

                {/* Category Section */}
                <EnhancedSelect
                  label="Category"
                  value={product.categoryName}
                  onChange={(value) => {
                    // Find the category to get both ID and name
                    const selectedCategory = categories.find(cat => cat.name === value);
                    setProduct(prev => ({
                      ...prev,
                      categoryId: selectedCategory?._id || value,
                      categoryName: value
                    }));
                    validateField('categoryId', selectedCategory?._id || value);
                    clearApiErrors();
                  }}
                  options={filteredCategories}
                  placeholder="Select a category..."
                  error={errors.categoryId}
                  icon={Grid3X3}
                  required
                />



                {/* Image Section */}
                <EnhancedImageUpload
                  image={product.image}
                  onImageChange={(image) => {
                    setProduct(prev => ({ ...prev, image }));
                    validateField('image', image);
                    clearApiErrors();
                  }}
                  error={errors.image}
                />

                {/* Description Section */}
                <EnhancedInput
                  label="Description"
                  placeholder="Enter product description (optional)..."
                  value={product.description}
                  onChange={(text) => {
                    setProduct(prev => ({ ...prev, description: text }));
                    clearApiErrors();
                  }}
                  icon={Package}
                  multiline
                  rows={3}
                  detectTextDirection={detectTextDirection}
                  getTextDirectionClasses={getTextDirectionClasses}
                />
              </div>
            </GlassCard>

            {/* Enhanced Action Buttons */}
            <GlassCard gradient="from-white/15 to-white/10" className="p-6">
              <div className="flex gap-4 justify-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => navigate('/supplier/products')}
                  disabled={isLoading}
                  className="flex-1 px-8 py-4 bg-white/15 border border-white/30 text-white rounded-2xl hover:bg-white/25 transition-all font-semibold disabled:opacity-50"
                >
                  Cancel
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSubmit}
                  disabled={isLoading}
                  className="flex-2 px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl hover:from-purple-700 hover:to-blue-700 transition-all font-bold disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 size={20} />
                      Update Product
                    </>
                  )}
                </motion.button>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </>
  );
};

export default EditProductPage;
