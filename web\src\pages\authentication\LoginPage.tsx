import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
  Mail, Lock, Eye, EyeOff, AlertCircle, Sparkles, Zap,
  Shield, Crown, Gem, Star, Heart, Flame, ChevronRight,
  Bot, Package, Truck, MapPin, Gift, TrendingUp
} from 'lucide-react';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import { LanguageSwitcher } from '../../components/common/LanguageSwitcher';
import { useAuth } from '../../contexts/AuthContext';
import { useCurrentUserData } from '../../hooks/useCurrentUserData';

interface LoginFormData {
  email: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [animationPhase, setAnimationPhase] = useState<'initial' | 'welcome' | 'form'>('initial');

  const { login } = useAuth();
  const navigate = useNavigate();
  const { user } = useCurrentUserData();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>();

  // Enhanced animation sequence
  useEffect(() => {
    const timer1 = setTimeout(() => setAnimationPhase('welcome'), 800);
    const timer2 = setTimeout(() => setAnimationPhase('form'), 1600);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  const onSubmit = async (data: LoginFormData) => {
    if (!data.email || !data.password) {
        setError('Please enter both email/username and password.');
        return;
      }
    setIsLoading(true);
    setError('');

    try {
      const result = await login(data.email, data.password);

      if (result.success) {
        // Navigate based on user role
        const userRole = user?.role;
        if (userRole === 'customer') {
          navigate('/customer/home');
        } else if (['supplier-admin', 'supplier-employee'].includes(userRole || '')) {
          navigate('/supplier/home');
        } else {
          navigate('/customer/home'); // Default fallback
        }
      } else {
        // Check if the error is related to email verification
        if (result.message?.includes('verify your email')) {
          navigate('/auth/email-verification', {
            state: { email: data.email, fromLogin: true }
          });
        } else {
          setError(result.message || 'Login failed. Please try again.');
        }
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      {/* Language Switcher */}
      <LanguageSwitcher variant="floating" position="top-right" />
      
      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.3, 0.1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -bottom-20 -right-20 w-32 h-32 bg-gradient-to-br from-third-500/20 to-primary-500/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-primary-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, -30, -10],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}

        {/* Service icons floating */}
        {[
          { icon: Package, delay: 0, x: 20, y: 30 },
          { icon: Truck, delay: 2, x: 80, y: 70 },
          { icon: MapPin, delay: 4, x: 10, y: 80 },
          { icon: Gift, delay: 6, x: 90, y: 20 },
        ].map((item, index) => (
          <motion.div
            key={index}
            className="absolute opacity-5"
            style={{ left: `${item.x}%`, top: `${item.y}%` }}
            animate={{
              y: [-5, 5, -5],
              rotate: [-5, 5, -5],
              opacity: [0.05, 0.15, 0.05],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: item.delay,
              ease: "easeInOut"
            }}
          >
            <item.icon size={24} className="text-primary-400" />
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10"
      >
        {/* Enhanced Welcome Header */}
        <AnimatePresence mode="wait">
          {animationPhase === 'initial' && (
            <motion.div
              key="initial"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.1, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                animate={{
                  scale: [1, 1.05, 1],
                  rotate: [0, 2, -2, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-block"
              >
                <Sparkles size={48} className="text-primary-500 mx-auto mb-2" />
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-700">Initializing...</h3>
            </motion.div>
          )}

          {animationPhase === 'welcome' && (
            <motion.div
              key="welcome"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-8"
            >
              <motion.div
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
                className="inline-block mb-4"
              >
                <div className="relative">
                  <Crown size={56} className="text-primary-500 mx-auto" />
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 0.8, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute inset-0 bg-primary-400/20 rounded-full blur-xl"
                  />
                </div>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent"
              >
                {t('auth.welcomeBack')} 👑
              </motion.h2>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-gray-600 mt-2"
              >
                {t('auth.signInToContinue')}
              </motion.p>
            </motion.div>
          )}

          {animationPhase === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Premium Welcome Header */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center mb-8"
              >
                <div className="flex items-center justify-center gap-3 mb-4">
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Zap size={32} className="text-primary-500" />
                  </motion.div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-secondary-500 to-third-500 bg-clip-text text-transparent">
                    {t('auth.welcomeBack')}
                  </h2>
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  >
                    <Sparkles size={28} className="text-secondary-500" />
                  </motion.div>
                </div>
                <p className="text-gray-600 font-medium">
                  {t('auth.signInToContinue')}
                </p>
              </motion.div>

              {/* Enhanced Error Display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: -10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -10 }}
                  className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl flex items-center space-x-3 text-red-700 shadow-lg"
                >
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 0.5 }}
                  >
                    <AlertCircle size={20} className="text-red-500" />
                  </motion.div>
                  <span className="text-sm font-medium">{error}</span>
                </motion.div>
              )}

              {/* Premium Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Enhanced Email Field - Always LTR for better UX */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className="relative"
                >
                  <div className="relative group">
                    <motion.div
                      className={`absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 rounded-2xl transition-all duration-300 ${
                        focusedField === 'email' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                      }`}
                    />
                    <div className="relative">
                      <Input
                        label={t('auth.usernameOrEmail')}
                        type="text"
                        placeholder={t('auth.enterUsernameOrEmail')}
                        icon={<Mail size={18} className="text-gray-800" />}
                        error={errors.email?.message}
                        onFocus={() => setFocusedField('email')}
                        className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-primary-300 focus:border-primary-500 rounded-2xl transition-all duration-300"
                        {...register('email', {
                          required: t('auth.usernameOrEmail') + ' is required',
                          onBlur: () => setFocusedField(null)
                        })}
                      />
                    </div>
                  </div>
                </motion.div>

                {/* Enhanced Password Field - Always LTR for better UX */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                  className="relative"
                >
                  <div className="relative group">
                    <motion.div
                      className={`absolute inset-0 bg-gradient-to-r from-secondary-500/10 to-third-500/10 rounded-2xl transition-all duration-300 ${
                        focusedField === 'password' ? 'scale-105 opacity-100' : 'scale-100 opacity-0'
                      }`}
                    />
                    <div className="relative">
                      <Input
                        label={t('auth.password')}
                        type={showPassword ? 'text' : 'password'}
                        placeholder={t('auth.enterPassword')}
                        icon={<Lock size={18} className="text-gray-800" />}
                        error={errors.password?.message}
                        onFocus={() => setFocusedField('password')}
                        className="bg-white/80 backdrop-blur-sm border-2 border-gray-200 hover:border-secondary-300 focus:border-secondary-500 rounded-2xl transition-all duration-300"
                        {...register('password', {
                          required: t('auth.password') + ' is required',
                          minLength: {
                            value: 6,
                            message: t('auth.password') + ' must be at least 6 characters',
                          },
                          onBlur: () => setFocusedField(null)
                        })}
                      />
                      <motion.button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-4 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                      </motion.button>
                    </div>
                  </div>
                </motion.div>

                {/* Enhanced Forgot Password Link */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="flex items-center justify-between"
                >
                  <Link
                    to="/auth/verify-before-reset"
                    className="group flex items-center gap-2 text-sm text-primary-600 hover:text-primary-700 transition-all duration-300"
                  >
                    <Shield size={16} />
                    <span className="font-medium">{t('auth.forgotPassword')}</span>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ChevronRight size={14} />
                    </motion.div>
                  </Link>
                </motion.div>

                {/* Premium Submit Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  className="relative"
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl blur-lg opacity-30"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    loading={isLoading}
                    disabled={isLoading}
                    className="w-full relative z-10 bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white font-bold py-4 px-8 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                  >
                    <div className="flex items-center justify-center gap-3">
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Zap size={20} />
                        </motion.div>
                      ) : (
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Zap size={20} />
                        </motion.div>
                      )}
                      <span className="text-lg">
                        {isLoading ? t('auth.signingIn') : t('auth.signIn')}
                      </span>
                    </div>
                  </Button>
                </motion.div>
              </form>

              {/* Enhanced Sign Up Link */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="mt-8 text-center"
              >
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500 font-medium">{t('auth.newToBolTalab')}</span>
                  </div>
                </div>
                <motion.div
                  className="mt-4"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    to="/auth/signup"
                    className="group inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-semibold transition-all duration-300 bg-gradient-to-r from-primary-50 to-secondary-50 hover:from-primary-100 hover:to-secondary-100 px-6 py-3 rounded-2xl border border-primary-200 hover:border-primary-300"
                  >
                    <Star size={16} />
                    <span>{t('auth.createAccount')}</span>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      animate={{ x: [0, 3, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <ChevronRight size={16} />
                    </motion.div>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default LoginPage;
