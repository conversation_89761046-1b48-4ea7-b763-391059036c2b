import React, { useState } from 'react';
import { YStack, XStack, <PERSON>, <PERSON><PERSON>, Card } from 'tamagui';
import { EnhancedErrorDisplay } from './EnhancedErrorDisplay';
import { showEnhancedErrorAlert, formatErrorMessage } from '../../utils/errorDisplay';
import { useTranslation } from 'react-i18next';

/**
 * Example component demonstrating how to use enhanced error handling
 * with backend messages and validation errors
 */
export const ErrorDisplayExample: React.FC = () => {
  const { t } = useTranslation();
  const [currentError, setCurrentError] = useState<any>(null);

  // Simulate different types of errors
  const simulateValidationError = () => {
    const validationError = {
      success: false,
      message: 'Validation failed',
      error: 'Please check your input',
      errors: [
        { field: 'email', msg: 'Invalid email format' },
        { field: 'password', msg: 'Password must be at least 8 characters' }
      ]
    };
    setCurrentError(validationError);
  };

  const simulateBackendError = () => {
    const backendError = {
      success: false,
      message: 'Something went wrong',
      error: 'Database connection timeout: Unable to connect to MongoDB cluster',
      statusCode: 500
    };
    setCurrentError(backendError);
  };

  const simulateAuthenticationError = () => {
    const authError = {
      success: false,
      message: 'Authentication failed',
      error: 'Invalid token: JWT has expired',
      statusCode: 401
    };
    setCurrentError(authError);
  };

  const simulateNetworkError = () => {
    const networkError = new Error('Network request failed');
    (networkError as any).message = 'Failed to fetch: Network error';
    setCurrentError(networkError);
  };

  // Show error using enhanced alert
  const showErrorAlert = () => {
    if (currentError) {
      showEnhancedErrorAlert(currentError, t, true, true);
    }
  };

  // Format error message with backend details
  const getFormattedError = () => {
    if (currentError) {
      return formatErrorMessage(currentError, t, true);
    }
    return 'No error to display';
  };

  return (
    <YStack padding="$4" gap="$4">
      <Text fontSize="$6" fontWeight="bold" textAlign="center">
        Enhanced Error Handling Demo
      </Text>
      
      <Text fontSize="$4" textAlign="center" color="$gray10">
        This component demonstrates how to display backend error messages with enhanced feedback
      </Text>

      {/* Error Simulation Buttons */}
      <Card padding="$4" backgroundColor="$gray2">
        <Text fontSize="$4" fontWeight="600" marginBottom="$3">
          Simulate Different Error Types:
        </Text>
        <YStack gap="$2">
          <Button onPress={simulateValidationError} backgroundColor="$orange9">
            Validation Error
          </Button>
          <Button onPress={simulateBackendError} backgroundColor="$red9">
            Backend Error
          </Button>
          <Button onPress={simulateAuthenticationError} backgroundColor="$blue9">
            Authentication Error
          </Button>
          <Button onPress={simulateNetworkError} backgroundColor="$purple9">
            Network Error
          </Button>
        </YStack>
      </Card>

      {/* Current Error Display */}
      {currentError && (
        <Card padding="$4" backgroundColor="$red1" borderColor="$red6">
          <Text fontSize="$4" fontWeight="600" color="$red10" marginBottom="$3">
            Current Error:
          </Text>
          
          {/* Enhanced Error Display Component */}
          <EnhancedErrorDisplay
            error={currentError}
            t={t}
            variant="alert"
            size="large"
            onRetry={() => setCurrentError(null)}
            onDismiss={() => setCurrentError(null)}
            showBackendDetails={true}
            showValidationErrors={true}
          />
        </Card>
      )}

      {/* Alternative Display Methods */}
      <Card padding="$4" backgroundColor="$blue1" borderColor="$blue6">
        <Text fontSize="$4" fontWeight="600" color="$blue10" marginBottom="$3">
          Alternative Display Methods:
        </Text>
        
        <YStack gap="$3">
          <Button onPress={showErrorAlert} backgroundColor="$blue9">
            Show Enhanced Alert
          </Button>
          
          <Card padding="$3" backgroundColor="$blue2">
            <Text fontSize="$3" fontWeight="600" color="$blue9" marginBottom="$2">
              Formatted Error Message:
            </Text>
            <Text fontSize="$2" color="$blue8" fontFamily="monospace">
              {getFormattedError()}
            </Text>
          </Card>
        </YStack>
      </Card>

      {/* Usage Instructions */}
      <Card padding="$4" backgroundColor="$green1" borderColor="$green6">
        <Text fontSize="$4" fontWeight="600" color="$green10" marginBottom="$3">
          How to Use:
        </Text>
        
        <YStack gap="$2">
          <Text fontSize="$3" color="$green9">
            • <Text fontWeight="600">EnhancedErrorDisplay:</Text> Use this component to show errors with expandable details
          </Text>
          <Text fontSize="$3" color="$green9">
            • <Text fontWeight="600">showEnhancedErrorAlert:</Text> Show errors in an alert with backend details
          </Text>
          <Text fontSize="$3" color="$green9">
            • <Text fontWeight="600">formatErrorMessage:</Text> Get formatted error text for custom displays
          </Text>
          <Text fontSize="$3" color="$green9">
            • <Text fontWeight="600">Backend messages</Text> are automatically extracted and displayed
          </Text>
          <Text fontSize="$3" color="$green9">
            • <Text fontWeight="600">Validation errors</Text> are shown with field names and messages
          </Text>
        </YStack>
      </Card>
    </YStack>
  );
};
