import { ScrollView, Dimensions, Pressable } from 'react-native';
import { Text, View, YStack, XStack, Button, H2, H3, Card } from 'tamagui';
import { useRouter } from 'expo-router';
import { useMyOrdersStore, MyOrder } from '../../customer-pages-components/orders-page-components/useMyOrdersStore';
import { Ionicons } from '@expo/vector-icons';
import { useCurrentUserData } from '~/components/useCurrentUserData';
import { getSupplierById } from '~/services/apiService';
import { useSupplierProducts } from '../products-page-components/useSupplierProducts';
import { MotiView } from 'moti';
import { LinearGradient } from 'expo-linear-gradient';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';


export default function SupplierHomeGUI() {
  const { t } = useTranslation();
  const router = useRouter();
  const orders = useMyOrdersStore((s) => s.orders);
  const { user } = useCurrentUserData();
  const [supplierData, setSupplierData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const { setSupplierId, loadProducts } = useSupplierProducts();

  const [storeOpen, setStoreOpen] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Initialize supplier ID and load products
  useEffect(() => {
    if (user?.supplierId) {
      console.log('🏪 Initializing supplier in home:', user.supplierId);
      setSupplierId(user.supplierId);
      loadProducts();
    }
  }, [user?.supplierId, setSupplierId, loadProducts]);

  // Fetch supplier data from backend
  useEffect(() => {
    const fetchSupplierData = async () => {
      if (!user?.supplierId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const supplier = await getSupplierById(user.supplierId);
        setSupplierData(supplier);
      } catch (error) {
        console.error('Error fetching supplier data:', error);
        setSupplierData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchSupplierData();
  }, [user?.supplierId]);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  const newOrders = orders.filter((o) => o.status === 'Pending' /*&& o.supplierId === user?.supplierId*/);
  const inPreparingOrders = orders.filter((o) => o.status === 'Preparing' /*&& o.supplierId === user?.supplierId*/);
  const onTheWayOrders = orders.filter((o) => o.status === 'On the Way' /*&& o.supplierId === user?.supplierId*/);
  const deliveredOrders = orders.filter((o) => o.status === 'Delivered' /*&& o.supplierId === user?.supplierId*/);
  const allSupplierOrders = orders.filter((o) => o.supplierId === user?.supplierId);

  const windowWidth = Dimensions.get('window').width;

  // Calculate today's stats
  const today = new Date();
  const todayOrders = allSupplierOrders.filter(order => {
    const orderDate = new Date(order.createdAt || today);
    return orderDate.toDateString() === today.toDateString();
  });

  const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total, 0);
  const avgRating = 4.8; // This would come from reviews data
  const totalProducts = 45; // This would come from products data

  //const [updatingOrders, setUpdatingOrders] = useState<Set<string>>(new Set());

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Preparing': return "$yellow7";
      case 'On the Way': return "$orange6";
      case 'Delivered': return "$green6";
      default: return "$red7";
    }
  };

  /*const handleMarkPreparing = async (orderId: string) => {
      setUpdatingOrders(prev => new Set(prev).add(orderId));
      try {
        useMyOrdersStore.setState((state) => ({
          orders: state.orders.map((o) =>
            o.id === orderId ? { ...o, status: 'Preparing' } : o
          ),
        }));

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error('Error updating order to Preparing:', error);
      } finally {
        setUpdatingOrders(prev => {
          const newSet = new Set(prev);
          newSet.delete(orderId);
          return newSet;
        });
      }
  }*/

  const renderOrderCard = (order: MyOrder) => (
    <Card width={"100%"} elevate p="$4" br="$6" bg="$gray1" borderColor="$gray4" key={order.id}>
        <YStack jc="space-between" ai="center" mb="$2">
            <Text color="$gray12" fontSize="$4" fontWeight="700">#{order.id}</Text>
            <Text
            fontSize="$2"
            bg={getStatusColor(order.status)}
            px="$2"
            py="$1"
            br="$4"
            color="white"
            >
            {order.status}
            </Text>
        </YStack>

        <YStack gap="$2">
            <XStack ai="center" gap="$2">
            <Ionicons name="cube-outline" size={18} color="#6B7280" />
            <Text>{order.items.length} items</Text>
            </XStack>
            <XStack ai="center" gap="$2">
            <Ionicons name="cash-outline" size={18} color="#6B7280" />
            <Text>₪{order.total.toFixed(2)}</Text>
            </XStack>
            <XStack ai="center" gap="$2">
            <Ionicons name="location-outline" size={18} color="#6B7280" />
            <Text numberOfLines={2}>{order.address?.address ? order.address.address : 'No address'}</Text>
            </XStack>
        </YStack>

        <YStack mt="$3" gap="$2">
            <Button mt="$3" size="$3" br="$6" borderColor={"$primary"} color={"$primary"} variant="outlined"
              icon={<Ionicons name="eye-outline" size={18} color="#7529B3"/>}
              onPress={() => router.push({
                pathname: "/(supplier-pages)/home/<USER>",
                params: {orderId: order.id}
              })}
            >
            <Text>View</Text>
            </Button>
            {/*(order.status === 'Pending' ? (
            <Button
                mt="$3" size="$3" br="$6" borderColor={"$secondary"} color={"$secondary"} variant="outlined"
                icon={<Ionicons name="checkmark-circle-outline" size={16} color="#67B329" />}
                onPress={() => {
                  handleMarkPreparing(order.id);
                }}
                disabled={updatingOrders.has(order.id)}
            >
                {updatingOrders.has(order.id) ? 'Accepting...' : 'Accept Order'}
            </Button>
            ) : null)*/}
        </YStack>
        </Card>
  );

  const EnhancedSection = ({
    title,
    icon,
    color,
    orders,
    bgGradient,
  }: {
    title: string;
    icon: string;
    color: string;
    orders: MyOrder[];
    bgGradient: string[];
  }) => (
    <Card
      elevate
      br="$8"
      overflow="hidden"
      borderWidth={0}
      shadowColor={color as any}
      shadowOffset={{ width: 0, height: 8 }}
      shadowOpacity={0.2}
      shadowRadius={16}
    >
      <LinearGradient
        colors={bgGradient as any}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ padding: 20 }}
      >
        <XStack ai="center" jc="space-between" mb="$3">
          <XStack ai="center" gap="$3">
            <View
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 12,
                padding: 10,
              }}
            >
              <Ionicons name={icon as any} size={24} color="white" />
            </View>
            <YStack>
              <H3 color="white" fontWeight="800">
                {title}
              </H3>
              <Text color="white" opacity={0.9} fontSize="$3">
                {orders.length} orders
              </Text>
            </YStack>
          </XStack>

          {orders.length > 0 ? (
            <View
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 16,
                paddingHorizontal: 12,
                paddingVertical: 6,
              }}
            >
              <Text color="white" fontSize="$4" fontWeight="800">
                {orders.length}
              </Text>
            </View>
          ) : null}
        </XStack>
      </LinearGradient>

      <View bg="white" p="$4">
        {orders.length > 0 ? (
          <YStack gap="$3">
            {orders.slice(0, 2).map((order, index) => (
              <MotiView
                key={order.id}
                from={{ opacity: 0, translateX: -20 }}
                animate={{ opacity: 1, translateX: 0 }}
                transition={{ delay: index * 100, duration: 400 }}
              >
                {renderOrderCard(order)}
              </MotiView>
            ))}
            {orders.length > 2 ? (
              <Button
                bg={color as any}
                color="white"
                br="$6"
                size="$3"
                fontWeight="600"
                iconAfter={<Ionicons name="chevron-forward" size={16} color="white" />}
              >
                <Text>View All {orders.length} Orders</Text>
              </Button>
            ) : null}
          </YStack>
        ) : (
          <YStack ai="center" gap="$2" py="$3">
            <View
              style={{
                backgroundColor: '#f3f4f6',
                borderRadius: 16,
                padding: 12,
              }}
            >
              <Ionicons name="checkmark-done" size={24} color="#10b981" />
            </View>
            <Text color="$gray11" fontSize="$3" fontWeight="600">
              No {title.toLowerCase()}
            </Text>
          </YStack>
        )}
      </View>
    </Card>
  );

  return (
    <ScrollView contentContainerStyle={{ padding: 16, paddingBottom: 120, width: windowWidth }} showsVerticalScrollIndicator={false}>
      <YStack gap="$5">
        {/* EXTREME Dashboard Header */}
        <MotiView
          from={{ opacity: 0, translateY: -50, scale: 0.9 }}
          animate={{ opacity: 1, translateY: 0, scale: 1 }}
          transition={{ type: 'spring', damping: 20, stiffness: 300 }}
        >
          <Card
            elevate
            br="$10"
            overflow="hidden"
            borderWidth={0}
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 16 }}
            shadowOpacity={0.5}
            shadowRadius={32}
          >
            <LinearGradient
              colors={["#7529B3", "#8F3DD2", "#A855F7"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{ padding: 32 }}
            >
              {/* Decorative Background Elements */}
              <View
                style={{
                  position: 'absolute',
                  top: -60,
                  right: -60,
                  width: 140,
                  height: 140,
                  borderRadius: 70,
                  backgroundColor: 'rgba(255,255,255,0.1)',
                }}
              />
              <View
                style={{
                  position: 'absolute',
                  bottom: -40,
                  left: -40,
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                  backgroundColor: 'rgba(255,255,255,0.08)',
                }}
              />

              <YStack gap="$6">
                {/* Welcome Section */}
                <XStack ai="center" gap="$4">
                  <MotiView
                    from={{ scale: 0, rotate: '-180deg' }}
                    animate={{ scale: 1, rotate: '0deg' }}
                    transition={{ delay: 400, type: 'spring', damping: 15 }}
                  >
                    <View style={{ position: 'relative' }}>
                      <LinearGradient
                        colors={['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']}
                        style={{
                          borderRadius: 24,
                          padding: 20,
                          borderWidth: 2,
                          borderColor: 'rgba(255,255,255,0.2)',
                        }}
                      >
                        <Ionicons name="storefront" size={40} color="white" />
                      </LinearGradient>

                      {/* Store Status Indicator */}
                      <MotiView
                        from={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 600, type: 'spring', damping: 10 }}
                        style={{
                          position: 'absolute',
                          top: -8,
                          right: -8,
                        }}
                      >
                        <LinearGradient
                          colors={storeOpen ? ['#10b981', '#059669'] : ['#ef4444', '#dc2626']}
                          style={{
                            borderRadius: 12,
                            padding: 6,
                            borderWidth: 2,
                            borderColor: 'white',
                          }}
                        >
                          <Ionicons
                            name={storeOpen ? "checkmark" : "close"}
                            size={12}
                            color="white"
                          />
                        </LinearGradient>
                      </MotiView>
                    </View>
                  </MotiView>

                  <YStack flex={1} gap="$2">
                    <MotiView
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 600, duration: 600 }}
                    >
                      <H2 color="white" fontWeight="900" fontSize="$9">
                        <Text>👋 Hello, {supplierData?.name}</Text>
                      </H2>
                    </MotiView>

                    <MotiView
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 700, duration: 600 }}
                    >
                      <XStack ai="center" gap="$3">
                        <View
                          style={{
                            backgroundColor: storeOpen ? '#10b981' : '#ef4444',
                            borderRadius: 8,
                            paddingHorizontal: 12,
                            paddingVertical: 6,
                          }}
                        >
                          <Text color="white" fontSize="$2" fontWeight="700">
                            {storeOpen ? '🟢 OPEN' : '🔴 CLOSED'}
                          </Text>
                        </View>
                        <Text color="white" opacity={0.9} fontSize="$4" fontWeight="500">
                          {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </Text>
                      </XStack>
                    </MotiView>
                  </YStack>

                  {/* Quick Store Toggle */}
                  <MotiView
                    from={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 800, duration: 400 }}
                  >
                    <Pressable
                      onPress={() => setStoreOpen(!storeOpen)}
                      style={{
                        backgroundColor: 'rgba(255,255,255,0.15)',
                        borderRadius: 16,
                        padding: 16,
                        borderWidth: 2,
                        borderColor: 'rgba(255,255,255,0.3)',
                      }}
                    >
                      <Ionicons
                        name={storeOpen ? "pause" : "play"}
                        size={24}
                        color="white"
                      />
                    </Pressable>
                  </MotiView>
                </XStack>

                {/* Quick Stats Grid */}
                <MotiView
                  from={{ opacity: 0, translateY: 20 }}
                  animate={{ opacity: 1, translateY: 0 }}
                  transition={{ delay: 900, duration: 600 }}
                >
                  <XStack gap="$4" jc="space-between">
                    <YStack ai="center" gap="$1" flex={1}>
                      <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                        TODAY'S REVENUE
                      </Text>
                      <Text color="white" fontSize="$6" fontWeight="900">
                        ₪{todayRevenue.toFixed(0)}
                      </Text>
                    </YStack>

                    <View
                      style={{
                        width: 1,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                      }}
                    />

                    <YStack ai="center" gap="$1" flex={1}>
                      <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                        TODAY'S ORDERS
                      </Text>
                      <Text color="white" fontSize="$6" fontWeight="900">
                        {todayOrders.length}
                      </Text>
                    </YStack>

                    <View
                      style={{
                        width: 1,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                      }}
                    />

                    <YStack ai="center" gap="$1" flex={1}>
                      <Text color="white" opacity={0.8} fontSize="$2" fontWeight="600">
                        RATING
                      </Text>
                      <XStack ai="center" gap="$1">
                        <Ionicons name="star" size={16} color="#fbbf24" />
                        <Text color="white" fontSize="$6" fontWeight="900">
                          {avgRating}
                        </Text>
                      </XStack>
                    </YStack>
                  </XStack>
                </MotiView>
              </YStack>
            </LinearGradient>
          </Card>
        </MotiView>

        {/* Quick Actions Section */}
        <MotiView
          from={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 300, duration: 500 }}
        >
          <Card
            p="$4"
            br="$8"
            bg="white"
            borderWidth={1}
            borderColor="$gray4"
            shadowColor="$gray6"
            shadowOffset={{ width: 0, height: 8 }}
            shadowOpacity={0.1}
            shadowRadius={16}
          >
            <YStack gap="$4">
              <H3 color="$gray12" fontWeight="800"><Text>⚡ Quick Actions</Text></H3>

              <XStack gap="$3" flexWrap="wrap" jc="space-between">
                <Pressable
                  onPress={() => router.push('/(supplier-pages)/products/add-product')}
                  style={{ flex: 1, minWidth: '45%' }}
                >
                  <MotiView
                    from={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 400, type: 'spring', damping: 15 }}
                  >
                    <LinearGradient
                      colors={['#10b981', '#059669']}
                      style={{
                        borderRadius: 16,
                        padding: 20,
                        alignItems: 'center',
                        gap: 8,
                      }}
                    >
                      <Ionicons name="add-circle" size={32} color="white" />
                      <Text color="white" fontSize="$3" fontWeight="700">
                        Add Product
                      </Text>
                    </LinearGradient>
                  </MotiView>
                </Pressable>

                <Pressable
                  onPress={() => router.push('/(supplier-pages)/analytics')}
                  style={{ flex: 1, minWidth: '45%' }}
                >
                  <MotiView
                    from={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 500, type: 'spring', damping: 15 }}
                  >
                    <LinearGradient
                      colors={['#3b82f6', '#2563eb']}
                      style={{
                        borderRadius: 16,
                        padding: 20,
                        alignItems: 'center',
                        gap: 8,
                      }}
                    >
                      <Ionicons name="analytics" size={32} color="white" />
                      <Text color="white" fontSize="$3" fontWeight="700">
                        Analytics
                      </Text>
                    </LinearGradient>
                  </MotiView>
                </Pressable>

                <Pressable
                  onPress={() => router.push('/(supplier-pages)/products')}
                  style={{ flex: 1, minWidth: '45%' }}
                >
                  <MotiView
                    from={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 600, type: 'spring', damping: 15 }}
                  >
                    <LinearGradient
                      colors={['#f59e0b', '#d97706']}
                      style={{
                        borderRadius: 16,
                        padding: 20,
                        alignItems: 'center',
                        gap: 8,
                      }}
                    >
                      <Ionicons name="pricetags" size={32} color="white" />
                      <Text color="white" fontSize="$3" fontWeight="700">
                        Products
                      </Text>
                    </LinearGradient>
                  </MotiView>
                </Pressable>

                <Pressable
                  onPress={() => router.push('/(supplier-pages)/profile')}
                  style={{ flex: 1, minWidth: '45%' }}
                >
                  <MotiView
                    from={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 700, type: 'spring', damping: 15 }}
                  >
                    <LinearGradient
                      colors={['#8b5cf6', '#7c3aed']}
                      style={{
                        borderRadius: 16,
                        padding: 20,
                        alignItems: 'center',
                        gap: 8,
                      }}
                    >
                      <Ionicons name="settings" size={32} color="white" />
                      <Text color="white" fontSize="$3" fontWeight="700">
                        Settings
                      </Text>
                    </LinearGradient>
                  </MotiView>
                </Pressable>
              </XStack>
            </YStack>
          </Card>
        </MotiView>

        {/* Orders Section - PROMINENT & ENHANCED */}
        <MotiView
          from={{ opacity: 0, translateY: 30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 400, duration: 600 }}
        >
          <Card
            elevate
            br="$10"
            overflow="hidden"
            borderWidth={0}
            shadowColor="#ef4444"
            shadowOffset={{ width: 0, height: 12 }}
            shadowOpacity={0.3}
            shadowRadius={24}
          >
            <LinearGradient
              colors={["#ef4444", "#dc2626", "#b91c1c"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{ padding: 24 }}
            >
              <XStack ai="center" jc="space-between" mb="$4">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      borderRadius: 16,
                      padding: 12,
                    }}
                  >
                    <Ionicons name="alert-circle" size={28} color="white" />
                  </View>
                  <YStack>
                    <H2 color="white" fontWeight="900">
                      <Text>🚨 New Orders</Text>
                    </H2>
                    <Text color="white" opacity={0.9} fontSize="$3">
                      {newOrders.length} orders need attention
                    </Text>
                  </YStack>
                </XStack>

                {newOrders.length > 0 ? (
                  <View
                    style={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      borderRadius: 20,
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                    }}
                  >
                    <Text color="white" fontSize="$5" fontWeight="800">
                      {newOrders.length}
                    </Text>
                  </View>
                ) : null}
              </XStack>
            </LinearGradient>

            <View bg="white" p="$4">
              {newOrders.length > 0 ? (
                <YStack gap="$3">
                  {newOrders.slice(0, 3).map((order, index) => (
                    <MotiView
                      key={order.id}
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 500 + index * 100, duration: 400 }}
                    >
                      {renderOrderCard(order)}
                    </MotiView>
                  ))}
                  {newOrders.length > 3 ? (
                    <Button
                      bg="$red9"
                      color="white"
                      br="$6"
                      size="$4"
                      fontWeight="700"
                      iconAfter={<Ionicons name="chevron-forward" size={20} color="white" />}
                    >
                      <Text>View All {newOrders.length} New Orders</Text>
                    </Button>
                  ) : null}
                </YStack>
              ) : (
                <YStack ai="center" gap="$3" py="$4">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 20,
                      padding: 16,
                    }}
                  >
                    <Ionicons name="checkmark-done" size={32} color="#10b981" />
                  </View>
                  <Text color="$gray11" fontSize="$4" fontWeight="600">
                    🎉 All caught up! No new orders.
                  </Text>
                </YStack>
              )}
            </View>
          </Card>
        </MotiView>

        {/* Performance Highlights */}
        <MotiView
          from={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 500, duration: 600 }}
        >
          <Card
            p="$4"
            br="$8"
            bg="white"
            borderWidth={1}
            borderColor="$gray4"
            shadowColor="$gray6"
            shadowOffset={{ width: 0, height: 8 }}
            shadowOpacity={0.1}
            shadowRadius={16}
          >
            <YStack gap="$4">
              <H3 color="$gray12" fontWeight="800"><Text>📊 Today's Highlights</Text></H3>

              <XStack gap="$3" flexWrap="wrap">
                {/* Revenue Card */}
                <MotiView
                  from={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 600, type: 'spring', damping: 15 }}
                  style={{ flex: 1, minWidth: '45%' }}
                >
                  <LinearGradient
                    colors={['#10b981', '#059669']}
                    style={{
                      borderRadius: 12,
                      padding: 16,
                    }}
                  >
                    <YStack gap="$2">
                      <XStack ai="center" gap="$2">
                        <Ionicons name="trending-up" size={20} color="white" />
                        <Text color="white" fontSize="$2" fontWeight="600" opacity={0.9}>
                          REVENUE
                        </Text>
                      </XStack>
                      <Text color="white" fontSize="$6" fontWeight="900">
                        ₪{todayRevenue.toFixed(0)}
                      </Text>
                      <Text color="white" fontSize="$1" opacity={0.8}>
                        +12% from yesterday
                      </Text>
                    </YStack>
                  </LinearGradient>
                </MotiView>

                {/* Orders Card */}
                <MotiView
                  from={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 700, type: 'spring', damping: 15 }}
                  style={{ flex: 1, minWidth: '45%' }}
                >
                  <LinearGradient
                    colors={['#3b82f6', '#2563eb']}
                    style={{
                      borderRadius: 12,
                      padding: 16,
                    }}
                  >
                    <YStack gap="$2">
                      <XStack ai="center" gap="$2">
                        <Ionicons name="receipt" size={20} color="white" />
                        <Text color="white" fontSize="$2" fontWeight="600" opacity={0.9}>
                          ORDERS
                        </Text>
                      </XStack>
                      <Text color="white" fontSize="$6" fontWeight="900">
                        {todayOrders.length}
                      </Text>
                      <Text color="white" fontSize="$1" opacity={0.8}>
                        {todayOrders.length > 0 ? `+${todayOrders.length} today` : `${todayOrders.length} today`}
                      </Text>
                    </YStack>
                  </LinearGradient>
                </MotiView>
              </XStack>

              {/* Quick Stats Row */}
              <XStack gap="$3" jc="space-between">
                <YStack ai="center" gap="$1" flex={1}>
                  <Text color="$gray8" fontSize="$1" fontWeight="600">
                    AVG ORDER
                  </Text>
                  <Text color="$gray12" fontSize="$4" fontWeight="800">
                    ₪{todayOrders.length > 0 ? (todayRevenue / todayOrders.length).toFixed(0) : '0'}
                  </Text>
                </YStack>

                <View
                  style={{
                    width: 1,
                    backgroundColor: '#e5e7eb',
                  }}
                />

                <YStack ai="center" gap="$1" flex={1}>
                  <Text color="$gray8" fontSize="$1" fontWeight="600">
                    COMPLETION
                  </Text>
                  <Text color="$gray12" fontSize="$4" fontWeight="800">
                    {allSupplierOrders.length > 0
                      ? Math.round((deliveredOrders.length / allSupplierOrders.length) * 100)
                      : 0}%
                  </Text>
                </YStack>

                <View
                  style={{
                    width: 1,
                    backgroundColor: '#e5e7eb',
                  }}
                />

                <YStack ai="center" gap="$1" flex={1}>
                  <Text color="$gray8" fontSize="$1" fontWeight="600">
                    PRODUCTS
                  </Text>
                  <Text color="$gray12" fontSize="$4" fontWeight="800">
                    {totalProducts}
                  </Text>
                </YStack>
              </XStack>
            </YStack>
          </Card>
        </MotiView>

        {/* Enhanced Other Order Sections */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 600, duration: 500 }}
        >
          <EnhancedSection
            title="In Preparing Orders"
            icon="timer-outline"
            color="#e4c000"
            orders={inPreparingOrders}
            bgGradient={["#fbbf24", "#f59e0b"]}
          />
        </MotiView>

        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 700, duration: 500 }}
        >
          <EnhancedSection
            title="On The Way Orders"
            icon="car-outline"
            color="#F97316"
            orders={onTheWayOrders}
            bgGradient={["#f97316", "#ea580c"]}
          />
        </MotiView>

        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 800, duration: 500 }}
        >
          <EnhancedSection
            title="Delivered Orders"
            icon="checkmark-done-outline"
            color="#10B981"
            orders={deliveredOrders}
            bgGradient={["#10b981", "#059669"]}
          />
        </MotiView>
      </YStack>
    </ScrollView>
  );
}
