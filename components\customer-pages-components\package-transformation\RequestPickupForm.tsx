import { useState, useEffect } from 'react';
import { ScrollView, Alert } from 'react-native';
import { Button, Input, Label, Text, View, YStack, Card, Separator, XStack, H3, H4 } from 'tamagui';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useRequestPickupStore, useUpdateRequestPickup, usePickupDeliveryAddress } from './useRequestPickupStore';
import { requestPickup } from '../../../services/apiService';
import { CustomTextField } from '../../CustomTextField';
import { useTranslation } from 'react-i18next';

export function RequestPickupForm() {
  const router = useRouter();
  const { t } = useTranslation();
  const {
    pickup,
    itemDescription,
    preferredTime,
    notes,
    updateField,
    reset
  } = useUpdateRequestPickup();
  const { addPickupRequest } = useRequestPickupStore();
  const { deliveryAddress, setDeliveryAddress } = usePickupDeliveryAddress();

  // Add local state for required fields
  const [senderName, setSenderName] = useState('');
  const [senderPhone, setSenderPhone] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [recipientPhone, setRecipientPhone] = useState('');
  const [packageDescription, setPackageDescription] = useState('');

  const isFormValid = () =>
    pickup &&
    deliveryAddress &&
    senderName.trim() &&
    senderPhone.trim() &&
    recipientName.trim() &&
    recipientPhone.trim() &&
    itemDescription.trim() &&
    ['documents', 'electronics', 'clothing', 'food', 'gifts', 'general'].includes(itemDescription) &&
    packageDescription.trim().length >= 2 &&
    preferredTime.trim();

  return (
    <>
      {/* Enhanced Professional Header */}
      <View
        width="100%"
        style={{
          paddingTop: 50,
          paddingBottom: 30,
          paddingHorizontal: 0,
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
          backgroundImage: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          backgroundColor: '#f093fb',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
          <YStack gap="$3" alignItems="center">
            <View
              style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 20,
                padding: 16,
                borderWidth: 2,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <Ionicons name="hand-left" size={32} color="white" />
            </View>
            <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
              {t('requestPickup.title', { defaultValue: 'Request Pickup' })}
            </Text>
            <Text fontSize="$4" color="rgba(255,255,255,0.9)" textAlign="center" maxWidth={280}>
              {t('requestPickup.subtitle', { defaultValue: 'Schedule a convenient pickup service' })}
            </Text>
          </YStack>
        </MotiView>
      </View>

      <ScrollView
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}
        style={{ backgroundColor: '#f8fafc', width: '100%' }}
      >
        <YStack gap="$5" px="$0" py="$4" width="100%">
          {/* Pickup Location */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 200, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="location" size={20} color="#f093fb" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('requestPickup.pickupLocation', { defaultValue: 'Pickup Location' })}
                  </H4>
                </XStack>

                <YStack gap="$3">
                  <Label fontSize="$4" fontWeight="600" color="$gray11">
                    {t('requestPickup.address', { defaultValue: 'Address' })}<Text color="$red9">*</Text>
                  </Label>
                  <XStack gap="$3" ai="center">
                    <XStack
                      flex={1}
                      alignItems="center"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor="$gray6"
                      borderRadius="$6"
                      backgroundColor="$background"
                      pressStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="location-outline" size={20} color="#f093fb" />
                      <Text
                        flex={1}
                        fontSize="$4"
                        color={pickup?.address ? '$gray12' : '$gray8'}
                      >
                        {pickup?.address || t('requestPickup.setOnMap', { defaultValue: 'Set location on map' })}
                      </Text>
                    </XStack>
                    <Button
                      size="$4"
                      bg="$primary"
                      color="white"
                      br="$6"
                      fontWeight="600"
                      pressStyle={{ bg: '$primaryPress' }}
                      onPress={() => router.push('/home/<USER>')}
                    >
                      {t('common.set', { defaultValue: 'Set' })}
                    </Button>
                  </XStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Sender Information */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 300, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="person-circle" size={20} color="#f093fb" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('requestPickup.senderInfo', { defaultValue: 'Sender Information' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <CustomTextField
                    icon="person-outline"
                    label={t('requestPickup.senderName', { defaultValue: 'Your Full Name' })}
                    placeholder={t('requestPickup.senderNamePlaceholder', { defaultValue: 'e.g. Mohammad Ali' })}
                    value={senderName}
                    onChangeText={setSenderName}
                    required
                    autoCapitalize="words"
                  />

                  <CustomTextField
                    icon="call-outline"
                    label={t('requestPickup.senderPhone', { defaultValue: 'Your Phone Number' })}
                    placeholder={t('requestPickup.senderPhonePlaceholder', { defaultValue: 'e.g. 0599123456' })}
                    value={senderPhone}
                    onChangeText={setSenderPhone}
                    keyboardType="phone-pad"
                    required
                  />
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Delivery Address */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 400, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="flag" size={20} color="#f093fb" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('requestPickup.deliveryLocation', { defaultValue: 'Delivery Location' })}
                  </H4>
                </XStack>

                <YStack gap="$3">
                  <Label fontSize="$4" fontWeight="600" color="$gray11">
                    {t('requestPickup.deliveryAddress', { defaultValue: 'Delivery Address' })}<Text> </Text><Text color="$red9">*</Text>
                  </Label>
                  <XStack gap="$3" ai="center">
                    <XStack
                      flex={1}
                      alignItems="center"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor="$gray6"
                      borderRadius="$6"
                      backgroundColor="$background"
                      pressStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="flag-outline" size={20} color="#f093fb" />
                      <Text
                        flex={1}
                        fontSize="$4"
                        color={deliveryAddress?.address ? '$gray12' : '$gray8'}
                      >
                        {deliveryAddress?.address || t('requestPickup.setDeliveryOnMap', { defaultValue: 'Set delivery location on map' })}
                      </Text>
                    </XStack>
                    <Button
                      size="$4"
                      bg="$primary"
                      color="white"
                      br="$6"
                      fontWeight="600"
                      pressStyle={{ bg: '$primaryPress' }}
                      onPress={() => router.push('/home/<USER>')}
                    >
                      {t('common.set', { defaultValue: 'Set' })}
                    </Button>
                  </XStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Recipient Information */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 500, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="person" size={20} color="#f093fb" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('requestPickup.recipientInfo', { defaultValue: 'Recipient Information' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <CustomTextField
                    icon="person-outline"
                    label={t('requestPickup.recipientName', { defaultValue: 'Recipient Full Name' })}
                    placeholder={t('requestPickup.recipientNamePlaceholder', { defaultValue: 'e.g. Ahmad Jaber' })}
                    value={recipientName}
                    onChangeText={setRecipientName}
                    required
                    autoCapitalize="words"
                  />

                  <CustomTextField
                    icon="call-outline"
                    label={t('requestPickup.recipientPhone', { defaultValue: 'Recipient Phone Number' })}
                    placeholder={t('requestPickup.recipientPhonePlaceholder', { defaultValue: 'e.g. 0599123456' })}
                    value={recipientPhone}
                    onChangeText={setRecipientPhone}
                    keyboardType="phone-pad"
                    required
                  />
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Item Details */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ delay: 600, type: 'spring', damping: 15 }}
          >
            <Card
              elevate
              p="$6"
              br="$0"
              bg="white"
              borderWidth={0}
              borderColor="$gray4"
              shadowColor="$primary"
              shadowOffset={{ width: 0, height: 8 }}
              shadowOpacity={0.1}
              shadowRadius={16}
              width="100%"
              mx="$0"
            >
              <YStack gap="$5">
                <XStack ai="center" gap="$3">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 12,
                      padding: 8,
                    }}
                  >
                    <Ionicons name="cube" size={20} color="#f093fb" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">
                    {t('requestPickup.itemDetails', { defaultValue: 'Item Details' })}
                  </H4>
                </XStack>

                <YStack gap="$4">
                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('requestPickup.packageType', { defaultValue: 'Package Type' })}<Text color="$red9">*</Text>
                    </Label>
                    <XStack gap="$2" flexWrap="wrap">
                      {['documents', 'electronics', 'clothing', 'food', 'gifts', 'general'].map((type) => (
                        <Button
                          key={type}
                          size="$3"
                          variant={itemDescription === type ? "outlined" : "outlined"}
                          backgroundColor={itemDescription === type ? "$primary" : "$background"}
                          borderColor={itemDescription === type ? "$primary" : "$gray6"}
                          color={itemDescription === type ? "white" : "$gray11"}
                          onPress={() => updateField('itemDescription', type)}
                          borderRadius="$6"
                          paddingHorizontal="$4"
                          pressStyle={{
                            backgroundColor: itemDescription === type ? "$primaryPress" : "$gray3",
                            scale: 0.95
                          }}
                        >
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </Button>
                      ))}
                    </XStack>
                    {!itemDescription && (
                      <Text color="$red9" fontSize="$3">
                        {t('requestPickup.selectPackageType', { defaultValue: 'Please select a package type' })}
                      </Text>
                    )}
                  </YStack>

                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('requestPickup.packageDescription', { defaultValue: 'Package Description' })}<Text color="$red9">*</Text>
                    </Label>
                    <XStack
                      alignItems="flex-start"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor={packageDescription.trim().length >= 2 ? "$gray6" : "$red6"}
                      borderRadius="$6"
                      backgroundColor="$background"
                      minHeight={80}
                      focusStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="document-text-outline" size={20} color="#f093fb" style={{ marginTop: 2 }} />
                      <Input
                        flex={1}
                        fontSize="$4"
                        placeholder={t('requestPickup.packageDescriptionPlaceholder', { defaultValue: 'Describe the package contents (minimum 2 characters)' })}
                        value={packageDescription}
                        onChangeText={setPackageDescription}
                        multiline
                        numberOfLines={3}
                        borderWidth={0}
                        backgroundColor="transparent"
                        focusStyle={{
                          borderWidth: 0,
                          backgroundColor: "transparent"
                        }}
                      />
                    </XStack>
                    {packageDescription.trim().length > 0 && packageDescription.trim().length < 2 && (
                      <Text color="$red9" fontSize="$3">
                        {t('requestPickup.descriptionMinLength', { defaultValue: 'Description must be at least 2 characters long' })}
                      </Text>
                    )}
                  </YStack>

                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('requestPickup.preferredTime', { defaultValue: 'Preferred Pickup Time' })}<Text color="$red9">*</Text>
                    </Label>
                    <XStack gap="$2" flexWrap="wrap">
                      {[
                        { label: 'In 1 Hour', value: () => new Date(Date.now() + 60 * 60 * 1000).toISOString() },
                        { label: 'In 2 Hours', value: () => new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() },
                        { label: 'Tomorrow Morning', value: () => {
                          const tomorrow = new Date();
                          tomorrow.setDate(tomorrow.getDate() + 1);
                          tomorrow.setHours(9, 0, 0, 0);
                          return tomorrow.toISOString();
                        }},
                        { label: 'Tomorrow Afternoon', value: () => {
                          const tomorrow = new Date();
                          tomorrow.setDate(tomorrow.getDate() + 1);
                          tomorrow.setHours(14, 0, 0, 0);
                          return tomorrow.toISOString();
                        }}
                      ].map((timeOption) => {
                        const timeValue = timeOption.value();
                        const isSelected = preferredTime === timeValue;
                        return (
                          <Button
                            key={timeOption.label}
                            size="$3"
                            variant="outlined"
                            backgroundColor={isSelected ? "$primary" : "$background"}
                            borderColor={isSelected ? "$primary" : "$gray6"}
                            color={isSelected ? "white" : "$gray11"}
                            onPress={() => updateField('preferredTime', timeValue)}
                            borderRadius="$6"
                            paddingHorizontal="$4"
                            pressStyle={{
                              backgroundColor: isSelected ? "$primaryPress" : "$gray3",
                              scale: 0.95
                            }}
                            flex={1}
                            minWidth="45%"
                          >
                            {timeOption.label}
                          </Button>
                        );
                      })}
                    </XStack>
                    {!preferredTime && (
                      <Text color="$red9" fontSize="$3">
                        {t('requestPickup.selectPreferredTime', { defaultValue: 'Please select a preferred time' })}
                      </Text>
                    )}
                  </YStack>

                  <YStack gap="$3">
                    <Label fontSize="$4" fontWeight="600" color="$gray11">
                      {t('requestPickup.notes', { defaultValue: 'Special Instructions' })}
                      <Text color="$gray8" fontSize="$3"> ({t('common.optional', { defaultValue: 'optional' })})</Text>
                    </Label>
                    <XStack
                      alignItems="flex-start"
                      gap="$3"
                      padding="$3"
                      borderWidth={2}
                      borderColor="$gray6"
                      borderRadius="$6"
                      backgroundColor="$background"
                      minHeight={80}
                      focusStyle={{ borderColor: '$primary' }}
                    >
                      <Ionicons name="chatbubble-outline" size={20} color="#f093fb" style={{ marginTop: 2 }} />
                      <Input
                        flex={1}
                        fontSize="$4"
                        placeholder={t('requestPickup.notesPlaceholder', { defaultValue: 'Optional instructions for the driver' })}
                        value={notes}
                        onChangeText={(text) => updateField('notes', text)}
                        multiline
                        numberOfLines={3}
                        borderWidth={0}
                        backgroundColor="transparent"
                        focusStyle={{
                          borderWidth: 0,
                          backgroundColor: "transparent"
                        }}
                      />
                    </XStack>
                  </YStack>
                </YStack>
              </YStack>
            </Card>
          </MotiView>

          {/* Enhanced Confirm Button */}
          <MotiView
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 800, type: 'spring', damping: 15 }}
          >
            <Button
              width="100%"
              height={60}
              size="$6"
              bg={isFormValid() ? "$primary" : "$gray8"}
              color="white"
              br="$8"
              fontSize="$5"
              fontWeight="700"
              icon={<Ionicons name="hand-left" size={24} color="white" />}
              disabled={!isFormValid()}
              opacity={isFormValid() ? 1 : 0.6}
              pressStyle={{
                bg: isFormValid() ? "$primaryPress" : "$gray8",
                scale: 0.98
              }}
              style={{
                shadowColor: isFormValid() ? '#f093fb' : '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: isFormValid() ? 0.3 : 0.1,
                shadowRadius: 8,
                elevation: 8,
              }}
            onPress={async () => {
              try {
                // Validate required fields
                if (!senderName.trim()) {
                  Alert.alert('Error', 'Please provide your full name');
                  return;
                }
                if (!senderPhone.trim()) {
                  Alert.alert('Error', 'Please provide your phone number');
                  return;
                }
                if (!pickup?.address?.trim()) {
                  Alert.alert('Error', 'Please provide a pickup address');
                  return;
                }
                if (!deliveryAddress?.address?.trim()) {
                  Alert.alert('Error', 'Please provide a delivery address');
                  return;
                }
                if (!recipientName.trim()) {
                  Alert.alert('Error', 'Please provide recipient name');
                  return;
                }
                if (!recipientPhone.trim()) {
                  Alert.alert('Error', 'Please provide recipient phone number');
                  return;
                }
                if (!itemDescription.trim() || !['documents', 'electronics', 'clothing', 'food', 'gifts', 'general'].includes(itemDescription)) {
                  Alert.alert('Error', 'Please select a valid package type');
                  return;
                }
                if (!packageDescription.trim() || packageDescription.trim().length < 2) {
                  Alert.alert('Error', 'Please provide a package description (minimum 2 characters)');
                  return;
                }
                if (!preferredTime.trim()) {
                  Alert.alert('Error', 'Please select a preferred pickup time');
                  return;
                }

                // Validate that preferredTime is a valid future date
                try {
                  const pickupDate = new Date(preferredTime);
                  const now = new Date();
                  const minTime = new Date(now.getTime() + 30 * 60 * 1000); // 30 minutes from now

                  if (pickupDate < minTime) {
                    Alert.alert('Error', 'Pickup time must be at least 30 minutes in the future');
                    return;
                  }
                } catch (error) {
                  Alert.alert('Error', 'Invalid pickup time selected');
                  return;
                }

                // Format phone numbers to ensure they start with +
                let formattedSenderPhone = senderPhone.trim();
                if (!formattedSenderPhone.startsWith('+')) {
                  // Assume Palestinian number if no country code
                  if (formattedSenderPhone.startsWith('0')) {
                    formattedSenderPhone = '+970' + formattedSenderPhone.substring(1);
                  } else {
                    formattedSenderPhone = '+970' + formattedSenderPhone;
                  }
                }

                let formattedRecipientPhone = recipientPhone.trim();
                if (!formattedRecipientPhone.startsWith('+')) {
                  // Assume Palestinian number if no country code
                  if (formattedRecipientPhone.startsWith('0')) {
                    formattedRecipientPhone = '+970' + formattedRecipientPhone.substring(1);
                  } else {
                    formattedRecipientPhone = '+970' + formattedRecipientPhone;
                  }
                }

                // Prepare pickup data for backend
                const pickupData = {
                  senderName: senderName.trim(),
                  senderPhone: formattedSenderPhone,
                  recipientName: recipientName.trim(),
                  recipientPhone: formattedRecipientPhone,
                  pickupAddress: {
                    street: pickup.address.trim(),
                    city: 'Nablus', // Default city
                    coordinates: {
                      lat: pickup.lat || 32.2211,
                      lng: pickup.lng || 35.2544
                    }
                  },
                  deliveryAddress: {
                    street: deliveryAddress.address.trim(),
                    city: 'Nablus', // Default city
                    coordinates: {
                      lat: deliveryAddress.lat || 32.2211,
                      lng: deliveryAddress.lng || 35.2544
                    }
                  },
                  packageDetails: {
                    type: itemDescription.trim(),
                    description: packageDescription.trim(),
                    size: 'medium' as const, // Default size
                    weight: '1' // Default weight as string (backend expects string)
                  },
                  preferredTime: preferredTime.trim(),
                  notes: notes?.trim() || ''
                };

                // Create pickup request via backend API
                const createdPickup = await requestPickup(pickupData);

                // Add to local store for immediate UI update
                addPickupRequest({
                  id: createdPickup.trackingNumber,
                  pickup,
                  itemDescription,
                  preferredTime,
                  notes,
                  driverName: 'Ali Alaa', // temporary
                  driverPhone: '0595959595',
                  status: 'pending',
                  createdAt: new Date().toISOString(),
                  estimatedTime: '45-60 mins',
                  cost: createdPickup.cost
                });

                // Reset all form fields
                reset();
                setSenderName('');
                setSenderPhone('');
                setRecipientName('');
                setRecipientPhone('');
                setDeliveryAddress(null);
                setPackageDescription('');
                router.push('/home/<USER>');
              } catch (error) {
                console.error('Error creating pickup request:', error);

                // Extract error message properly
                let errorMessage = 'Failed to create pickup request. Please try again.';
                if (error && typeof error === 'object') {
                  if ('message' in error && typeof error.message === 'string') {
                    errorMessage = error.message;
                  } else if ('errors' in error && Array.isArray(error.errors) && error.errors.length > 0) {
                    errorMessage = error.errors[0].msg || errorMessage;
                  }
                }

                Alert.alert('Error', errorMessage);
              }
            }}
            >
              {t('requestPickup.confirmRequest', { defaultValue: 'Confirm Request' })}
            </Button>
          </MotiView>
        </YStack>
      </ScrollView>
    </>
  );
}
