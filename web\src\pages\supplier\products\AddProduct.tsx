import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Plus,
  Save,
  Loader,
  CheckCircle2,
  AlertCircle,
  X,
  Upload,
  Camera,
  Image as ImageIcon,
  Tag,
  DollarSign,
  FileText,
  Trash2,
  ChevronDown,
  Rocket,
  ArrowRight,
  Sparkles,
  Crown,
  Target,
  Award,
  RefreshCw
} from 'lucide-react';
import { SafeImage } from '../../../components/common/SafeImage';
import { useNavigate } from 'react-router-dom';
import { useSupplierProductsStore, type Product } from '../../../stores/supplierProductsStore';
import { useSupplierCategories } from '../../../stores/supplierCategoriesStore';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';
import { detectErrorType, getErrorMessage, processApiResponse } from '../../../utils/errorHandler';
import { v4 as uuidv4 } from 'uuid';
import { EnhancedErrorDisplay } from '../../../components/common/EnhancedErrorDisplay';
import type { ErrorInfo } from '../../../utils/errorHandler';
import { useTranslation } from 'react-i18next';

// Modern Glass Card Component - Same as supplier home
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
    style={{
      zIndex: 10,
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component - Same as supplier home
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06,
      zIndex: -1,
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

// Enhanced Modal Component
const Modal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  gradient?: string;
}> = ({ isOpen, onClose, title, children, gradient = 'from-purple-600 to-blue-600' }) => (
  <AnimatePresence>
    {isOpen && (
      <>
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 50 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div className="relative bg-white rounded-3xl shadow-2xl max-w-md w-full overflow-hidden">
            {/* Header */}
            <div className={`bg-gradient-to-r ${gradient} p-6`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl border border-white/30">
                    <AlertCircle size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-white text-xl font-bold">{title}</h3>
                  </div>
                </div>

                <motion.button
                  onClick={onClose}
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-2 bg-white/20 rounded-xl border border-white/30 hover:bg-white/30 transition-colors"
                >
                  <X size={20} className="text-white" />
                </motion.button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {children}
            </div>
          </div>
        </motion.div>
      </>
    )}
  </AnimatePresence>
);

const AddProductPage: React.FC = () => {
  const navigate = useNavigate();
  const { addProduct } = useSupplierProductsStore();
  const { categories, loadCategories } = useSupplierCategories();
  const { user } = useCurrentUserData();
  const { t, i18n } = useTranslation();

  // Utility function to detect Arabic text and determine direction
  const detectTextDirection = (text: string): 'ltr' | 'rtl' => {
    if (!text) return i18n.language === 'ar' ? 'rtl' : 'ltr';

    // Arabic Unicode range: \u0600-\u06FF, \u0750-\u077F, \u08A0-\u08FF, \uFB50-\uFDFF, \uFE70-\uFEFF
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicRegex.test(text) ? 'rtl' : 'ltr';
  };

  // Get dynamic text direction classes
  const getTextDirectionClasses = (text: string): string => {
    const direction = detectTextDirection(text);
    return direction === 'rtl' ? 'text-right dir-rtl' : 'text-left dir-ltr';
  };

  // Enhanced state management
  const [product, setProduct] = useState<Omit<Product, 'id'> & { id?: string }>({
    id: uuidv4(),
    name: "",
    price: 0,
    discountPrice: 0,
    image: "",
    categoryId: "",
    categoryName: "",
    description: "",
    isAvailable: true,
    tags: [],
    nutritionInfo: {},
    allergens: [],
    preparationTime: '',
    restaurantOptions: {
      additions: [],
      without: [],
      sides: [],
    },
    clothingOptions: {
      sizes: [],
      colors: [],
      gallery: [],
    },
    customizations: [],
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [selectOpen, setSelectOpen] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Error handling state
  const [apiError, setApiError] = useState<ErrorInfo | null>(null);
  const [errorType, setErrorType] = useState<'network' | 'server' | 'auth' | 'validation' | 'notFound' | 'conflict' | 'forbidden' | 'unknown'>('unknown');
  const [categoriesError, setCategoriesError] = useState<ErrorInfo | null>(null);

  // Load categories and set supplier ID when component mounts
  useEffect(() => {
    if (user?.supplierId) {
      setCategoriesLoading(true);
      setCategoriesError(null);
      
      loadCategories(user?.supplierId)
        .then(() => {
          setCategoriesLoading(false);
        })
        .catch((error) => {
          console.error('Error loading categories:', error);
          const errorType = detectErrorType(error);
          setErrorType(errorType);
          setCategoriesError({
            type: errorType,
            message: getErrorMessage(errorType),
            userFriendlyMessage: getErrorMessage(errorType),
            statusCode: 500
          });
        })
        .finally(() => {
          setCategoriesLoading(false);
        });
      // Set supplier ID in the products store
      useSupplierProductsStore.getState().setSupplierId(user.supplierId);
    } else if (user && !user.supplierId) {
      // User is logged in but has no supplier ID
      setCategoriesError({
        type: 'auth',
        message: 'Your account is not linked to a supplier. Please contact support.',
        userFriendlyMessage: 'Your account is not linked to a supplier. Please contact support.',
        statusCode: 403
      });
      setErrorType('auth');
    }
  }, [user?.supplierId, loadCategories]);

  // Loading state for categories
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  
  // No fallback loading - if no categories exist, supplier should add them from index page

  // Form validation
  const validateField = useCallback((field: string, value: any) => {
    const newErrors = { ...errors };

    switch (field) {
      case 'name':
        if (!value || value.trim().length < 2) {
          newErrors.name = 'Product name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          newErrors.name = 'Product name must be less than 50 characters';
        } else {
          delete newErrors.name;
        }
        break;
      case 'price':
        if (!value || isNaN(value) || value <= 0) {
          newErrors.price = 'Please enter a valid price';
        } else if (value > 10000) {
          newErrors.price = 'Price seems too high (max: 10,000)';
        } else {
          delete newErrors.price;
        }
        break;
      case 'category':
        if (filteredCategories.length === 0) {
          // No categories available, so category is not required
          delete newErrors.category;
        } else if (!value) {
          newErrors.category = 'Please select a category';
        } else {
          delete newErrors.category;
        }
        break;
      case 'image':
        if (!value) {
          newErrors.image = 'Please upload a product image';
        } else {
          delete newErrors.image;
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [errors]);

  // Handle image upload (web version)
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setImageLoading(true);
    setApiError(null); // Clear previous errors
    const file = event.target.files?.[0];

    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setApiError({
          type: 'validation',
          message: 'Image file is too large. Please select an image under 5MB.',
          userFriendlyMessage: 'Image file is too large. Please select an image under 5MB.',
          statusCode: 400
        });
        setErrorType('validation');
        setImageLoading(false);
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        setApiError({
          type: 'validation',
          message: 'Please select a valid image file (JPEG, PNG, GIF, etc.).',
          userFriendlyMessage: 'Please select a valid image file (JPEG, PNG, GIF, etc.).',
          statusCode: 400
        });
        setErrorType('validation');
        setImageLoading(false);
        return;
      }

      // Additional validation for common image formats
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setApiError({
          type: 'validation',
          message: 'Please select a supported image format (JPEG, PNG, GIF, or WebP).',
          userFriendlyMessage: 'Please select a supported image format (JPEG, PNG, GIF, or WebP).',
          statusCode: 400
        });
        setErrorType('validation');
        setImageLoading(false);
        return;
      }

      const reader = new FileReader();
      
      // Add timeout for file reading
      const timeoutId = setTimeout(() => {
        reader.abort();
        setApiError({
          type: 'network',
          message: 'Image upload timed out. Please try again with a smaller image.',
          userFriendlyMessage: 'Image upload timed out. Please try again with a smaller image.',
          statusCode: 504
        });
        setErrorType('network');
        setImageLoading(false);
      }, 10000); // 10 second timeout
      
      reader.onload = (e) => {
        clearTimeout(timeoutId);
        const imageUrl = e.target?.result as string;
        setProduct({ ...product, image: imageUrl });
        validateField('image', imageUrl);
        setImageLoading(false);
      };
      reader.onerror = () => {
        clearTimeout(timeoutId);
        setApiError({
          type: 'unknown',
          message: 'Failed to read image file. Please try again.',
          userFriendlyMessage: 'Failed to read image file. Please try again.',
          statusCode: 500
        });
        setErrorType('unknown');
        setImageLoading(false);
      };
      reader.readAsDataURL(file);
    } else {
      setImageLoading(false);
    }
  };

  const validateAndContinue = async () => {
    // Clear previous API errors
    setApiError(null);
    setErrorType('unknown');
    
    // Validate all fields
    const nameValid = validateField('name', product.name);
    const priceValid = validateField('price', product.price);
    const categoryValid = filteredCategories.length === 0 ? true : validateField('categoryId', product.categoryId);
    const imageValid = validateField('image', product.image);

    if (!nameValid || !priceValid || !categoryValid || !imageValid) {
      // Show validation error summary
      const validationErrors = [];
      if (!nameValid) validationErrors.push('Product name');
      if (!priceValid) validationErrors.push('Price');
      if (!categoryValid && filteredCategories.length > 0) validationErrors.push('Category');
      if (!imageValid) validationErrors.push('Product image');
      
      setApiError({
        type: 'validation',
        message: `Please complete all required fields: ${validationErrors.join(', ')}.`,
        userFriendlyMessage: `Please complete all required fields: ${validationErrors.join(', ')}.`,
        statusCode: 400
      });
      setErrorType('validation');
      
      // Scroll to the first error field for better UX
      setTimeout(() => {
        const firstErrorField = document.querySelector('input[class*="border-red-400"], select[class*="border-red-400"]');
        if (firstErrorField) {
          firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
      
      return;
    }

    if (!user?.supplierId) {
      setApiError({
        type: 'auth',
        message: 'No supplier ID found. Please log in again.',
        userFriendlyMessage: 'No supplier ID found. Please log in again.',
        statusCode: 401
      });
      setErrorType('auth');
      return;
    }

    setIsLoading(true);

    try {
      // Format product for backend (remove id field as it will be generated by backend)
      const productForBackend = {
        name: product.name,
        image: product.image,
        price: product.price,
        discountPrice: product.discountPrice || undefined,
        categoryId: product.categoryId || '',
        categoryName: product.categoryName || '',
        description: product.description || undefined,
        isAvailable: product.isAvailable,
        tags: product.tags,
        nutritionInfo: product.nutritionInfo,
        allergens: product.allergens,
        preparationTime: product.preparationTime || undefined,
        restaurantOptions: product.restaurantOptions,
        clothingOptions: product.clothingOptions
      };

      // Add product to backend
      await addProduct(productForBackend);
      
      // Show success modal
      setShowSuccessModal(true);
      setIsLoading(false);

      // Navigate back to products page after a delay
      setTimeout(() => {
        navigate('/supplier/products');
      }, 2000);

    } catch (error) {
      console.error('Failed to create product:', error);
      
      // Use enhanced error detection
      const errorType = detectErrorType(error);
      setErrorType(errorType);
      
      // Provide more specific error messages based on error type
      let errorMessage = getErrorMessage(errorType);
      if (errorType === 'network') {
        errorMessage = 'Connection failed. Please check your internet and try again.';
      } else if (errorType === 'server') {
        errorMessage = 'Server is temporarily unavailable. Please try again in a few minutes.';
      } else if (errorType === 'auth') {
        errorMessage = 'Your session has expired. Please log in again.';
        // Redirect to login after showing error
        setTimeout(() => navigate('/authentication/login'), 3000);
      } else if (errorType === 'validation') {
        errorMessage = 'Please check your product information and try again.';
      } else if (errorType === 'conflict') {
        errorMessage = 'A product with this name already exists. Please choose a different name.';
      }
      
      setApiError({
        type: errorType,
        message: errorMessage,
        userFriendlyMessage: errorMessage,
        statusCode: 500
      });
      setIsLoading(false);
    }
  };

  // Retry functions for error handling
  const retryLoadCategories = () => {
    setCategoriesError(null);
    if (user?.supplierId) {
      setCategoriesLoading(true);
      loadCategories(user.supplierId)
        .then(() => {
          setCategoriesLoading(false);
        })
        .catch((error) => {
          console.error('Failed to load categories:', error);
          const errorType = detectErrorType(error);
          setErrorType(errorType);
          setCategoriesLoading(false);
          
          // Provide specific error messages for category loading
          let errorMessage = getErrorMessage(errorType);
          if (errorType === 'network') {
            errorMessage = 'Failed to load categories. Please check your connection and try again.';
          } else if (errorType === 'server') {
            errorMessage = 'Categories service is temporarily unavailable. Please try again later.';
          } else if (errorType === 'auth') {
            errorMessage = 'Authentication failed. Please log in again.';
            setTimeout(() => navigate('/authentication/login'), 3000);
          }
          
          setCategoriesError({
            type: errorType,
            message: errorMessage,
            userFriendlyMessage: errorMessage,
            statusCode: 500
          });
        });
    } else {
      setCategoriesError({
        type: 'auth',
        message: 'No supplier ID found. Please contact support.',
        userFriendlyMessage: 'No supplier ID found. Please contact support.',
        statusCode: 403
      });
    }
  };

  const retryOperation = () => {
    setApiError(null);
    setErrorType('unknown');
    // Retry the product creation
    validateAndContinue();
  };

  // Clear API errors when user makes changes
  const clearApiErrors = () => {
    if (apiError) {
      setApiError(null);
      setErrorType('unknown');
    }
  };

  const handleIncompleteSubmit = () => {
    // Show helpful message when user tries to submit incomplete form
    const missingFields = [];
    if (!product.name.trim()) missingFields.push('Product name');
    if (!product.price || product.price <= 0) missingFields.push('Price');
    if (filteredCategories.length > 0 && !product.categoryId) missingFields.push('Category');
    if (!product.image) missingFields.push('Product image');
    
    if (missingFields.length > 0) {
      setApiError({
        type: 'validation',
        message: `Please complete these required fields: ${missingFields.join(', ')}.`,
        userFriendlyMessage: `Please complete these required fields: ${missingFields.join(', ')}.`,
        statusCode: 400
      });
      setErrorType('validation');
      
      // Scroll to the first missing field
      setTimeout(() => {
        const firstMissingField = document.querySelector('input[placeholder*="name"], input[placeholder*="price"], select, input[type="file"]');
        if (firstMissingField) {
          firstMissingField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  };

  // Update product with error clearing
  const updateProduct = (updates: Partial<typeof product>) => {
    clearApiErrors();
    setProduct(prev => ({ ...prev, ...updates }));
  };

  // Calculate form completion percentage
  const getCompletionPercentage = () => {
    let completed = 0;
    const total = filteredCategories.length === 0 ? 3 : 4; // name, price, image (category only if categories exist)

    if (product.name.trim()) completed++;
    if (product.price > 0) completed++;
    if (filteredCategories.length === 0 || product.categoryId) completed++;
    if (product.image) completed++;

    return Math.round((completed / total) * 100);
  };

  // Calculate progress bar width to align with step indicators
  const getProgressBarWidth = () => {
    const steps = filteredCategories.length === 0 ? [
      !!product.name.trim(),
      product.price > 0,
      !!product.image
    ] : [
      !!product.name.trim(),
      product.price > 0,
      !!product.categoryId,
      !!product.image
    ];

    let completedSteps = 0;
    for (let i = 0; i < steps.length; i++) {
      if (steps[i]) {
        completedSteps = i + 1;
      } else {
        break;
      }
    }

    // Each step represents equal progress, but we want it to align with the step indicators
    // Step positions: 0%, 33.33%, 66.66%, 100% (for 3 steps) or 0%, 25%, 50%, 75%, 100% (for 4 steps)
    const stepPositions = filteredCategories.length === 0 ? [0, 33.33, 66.66, 100] : [0, 25, 50, 75, 100];
    return stepPositions[completedSteps] || 0;
  };

  const filteredCategories = categories.filter((cat) => cat.name !== "All");

  // Set default category to first available (excluding 'All') - only if categories exist
  useEffect(() => {
    if (!product.categoryId && filteredCategories.length > 0) {
      setProduct((prev) => ({ ...prev, categoryId: filteredCategories[0]._id, categoryName: filteredCategories[0].name }));
    } else if (!product.categoryId && filteredCategories.length === 0) {
      // Clear category if no categories exist
      setProduct((prev) => ({ ...prev, categoryId: "", categoryName: "" }));
    }
  }, [filteredCategories.length, product.categoryId]);

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }

        /* RTL Support */
        .dir-rtl {
          direction: rtl;
          text-align: right;
        }
        .dir-ltr {
          direction: ltr;
          text-align: left;
        }

        /* Ensure proper text alignment for RTL */
        input[dir="rtl"], textarea[dir="rtl"] {
          text-align: right;
        }
        input[dir="ltr"], textarea[dir="ltr"] {
          text-align: left;
        }

        /* Placeholder text alignment for RTL */
        input[dir="rtl"]::placeholder, textarea[dir="rtl"]::placeholder {
          text-align: right;
        }
        input[dir="ltr"]::placeholder, textarea[dir="ltr"]::placeholder {
          text-align: left;
        }
      `}</style>

      {/* Check for user authentication */}
      {!user ? (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          <div className="text-center text-white">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Loader size={48} className="mx-auto mb-4" />
            </motion.div>
            <h2 className="text-xl font-bold mb-2">Loading...</h2>
            <p className="text-white/70">Please wait while we verify your account.</p>
          </div>
        </div>
      ) : !user.supplierId ? (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          <div className="text-center text-white max-w-md mx-auto p-8">
            <AlertCircle size={64} className="mx-auto mb-6 text-red-400" />
            <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
            <p className="text-white/70 mb-6">
              You need to be a registered supplier to access this page. Please contact support or complete your supplier registration.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => navigate('/supplier/profile')}
                className="w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-colors font-semibold"
              >
                Go to Profile
              </button>
              <button
                onClick={() => navigate('/authentication/login')}
                className="w-full px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-colors font-semibold"
              >
                Login Again
              </button>
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* Success Modal */}
          <Modal
            isOpen={showSuccessModal}
            onClose={() => setShowSuccessModal(false)}
            title="Product Added Successfully!"
            gradient="from-green-600 to-emerald-600"
          >
            <div className="space-y-4">
              <div className="text-center">
                <CheckCircle2 size={48} className="text-green-500 mx-auto mb-4" />
                <p className="text-gray-700 text-lg font-semibold">
                  Your product has been added successfully!
                </p>
                <p className="text-gray-600 text-sm">
                  You will be redirected to the products page shortly.
                </p>
              </div>
              <button
                onClick={() => setShowSuccessModal(false)}
                className="w-full px-4 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors font-semibold"
              >
                Continue
              </button>
            </div>
          </Modal>

          <div className="min-h-screen relative overflow-hidden">
            {/* Background - Same as supplier home */}
            <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
              {/* Floating Orbs */}
              <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
              <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
              <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
              <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
              <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
              <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

              {/* Particle System */}
              <ParticleSystem />

              {/* Animated gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
            </div>

            {/* Main Content Container */}
            <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
              <div className="max-w-4xl mx-auto space-y-10">

                {/* Enhanced Header with Progress */}
                <motion.div
                  initial={{ opacity: 0, y: -30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <GlassCard gradient="from-purple-600/25 to-blue-600/25" className="p-10">
                    <div className="space-y-6">
                      {/* Header Content */}
                      <div className="flex items-center gap-6">
                        <motion.div
                          initial={{ scale: 0, rotate: -180 }}
                          animate={{ scale: 1, rotate: 0 }}
                          transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                          className="relative"
                        >
                          <div className="p-6 bg-white/25 border border-white/40 rounded-3xl">
                            <Plus size={32} className="text-white" />
                          </div>
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-purple-400/40 to-blue-400/40 rounded-3xl blur-2xl"
                            animate={{ opacity: [0.3, 0.6, 0.3] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          />
                        </motion.div>

                        <div className="flex-1">
                          <motion.h1
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3 }}
                            className="text-white text-4xl font-black mb-3 bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent"
                          >
                            Create New Product
                            <motion.span
                              animate={{ rotate: [0, 20, -20, 0] }}
                              transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
                              className="inline-block ml-2"
                            >
                              ✨
                            </motion.span>
                          </motion.h1>
                          <motion.p
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.4 }}
                            className="text-white/90 text-lg"
                          >
                            Build your product step by step
                          </motion.p>
                        </div>
                      </div>

                      {/* Progress Section */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-white/80 text-sm font-semibold">
                            Form Progress
                          </span>
                          <span className="text-white text-sm font-bold">
                            {getCompletionPercentage()}% Complete
                          </span>
                        </div>

                        {/* Progress Bar - aligned with step indicators */}
                        <div className="relative">
                          <div className="w-full bg-white/20 rounded-full h-3 overflow-hidden">
                            <motion.div
                              className="h-full bg-gradient-to-r from-white to-yellow-200 rounded-full"
                              initial={{ width: 0 }}
                              animate={{
                                width: `${getProgressBarWidth()}%`
                              }}
                              transition={{ duration: 0.5, ease: "easeOut" }}
                            />
                          </div>
                        </div>

                        {/* Step Indicators */}
                        <div className="flex justify-between items-center">
                          {(filteredCategories.length === 0 ? [
                            { icon: Tag, label: 'Name', completed: !!product.name.trim() },
                            { icon: DollarSign, label: 'Price', completed: product.price > 0 },
                            { icon: ImageIcon, label: 'Image', completed: !!product.image },
                          ] : [
                            { icon: Tag, label: 'Name', completed: !!product.name.trim() },
                            { icon: DollarSign, label: 'Price', completed: product.price > 0 },
                            { icon: Package, label: 'Category', completed: !!product.categoryId },
                            { icon: ImageIcon, label: 'Image', completed: !!product.image },
                          ]).map((step, index) => (
                            <div key={index} className="flex flex-col items-center gap-2 relative">
                              <motion.div
                                className={`p-3 rounded-2xl border-2 transition-all duration-300 relative z-10 ${
                                  step.completed
                                    ? 'bg-white border-white text-purple-600'
                                    : 'bg-white/20 border-white/30 text-white'
                                }`}
                                whileHover={{ scale: 1.1 }}
                                animate={step.completed ? { scale: [1, 1.2, 1] } : {}}
                                transition={{ duration: 0.3 }}
                              >
                                <step.icon size={20} />
                              </motion.div>
                              <span
                                className={`text-sm font-semibold transition-all duration-300 ${
                                  step.completed ? 'text-white' : 'text-white/70'
                                }`}
                              >
                                {step.label}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </GlassCard>
                </motion.div>

                {/* Error Displays and Loading States */}
                {categoriesLoading && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <GlassCard gradient="from-blue-600/25 to-cyan-600/25" className="p-8">
                      <div className="text-center">
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="flex justify-center mb-4"
                        >
                          <Loader size={32} className="text-blue-400" />
                        </motion.div>
                        <h3 className="text-white text-lg font-semibold mb-2">Loading Categories</h3>
                        <p className="text-white/70 text-sm">Please wait while we load your product categories...</p>
                      </div>
                    </GlassCard>
                  </motion.div>
                )}

                {categoriesError && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <EnhancedErrorDisplay
                      error={categoriesError}
                      onRetry={retryLoadCategories}
                      variant="floating"
                      showBackendDetails={true}
                      showValidationErrors={true}
                    />
                  </motion.div>
                )}

                {apiError && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <EnhancedErrorDisplay
                      error={apiError}
                      onRetry={retryOperation}
                      variant="floating"
                      showBackendDetails={true}
                      showValidationErrors={true}
                    />
                  </motion.div>
                )}

                {/* Enhanced Form Card */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                >
                  <GlassCard gradient="from-white/25 to-white/15" className="p-10">
                    <div className="space-y-8">

                      {/* Product Name Section */}
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 bg-purple-500/20 rounded-xl border border-purple-400/30">
                            <Tag size={20} className="text-purple-400" />
                          </div>
                          <h3 className="text-white text-xl font-bold">Product Name</h3>
                          <span className="text-red-400 text-lg">*</span>
                        </div>

                        <input
                          type="text"
                          placeholder="e.g., Delicious Chicken Burger"
                          value={product.name}
                          onChange={(e) => {
                            updateProduct({ name: e.target.value });
                            validateField('name', e.target.value);
                          }}
                          dir={detectTextDirection(product.name)}
                          className={`w-full px-6 py-4 bg-white/10 border-2 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-purple-400 transition-all duration-300 text-lg ${
                            errors.name ? 'border-red-400' : 'border-white/30'
                          } ${getTextDirectionClasses(product.name)}`}
                        />

                        {errors.name && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 text-red-400"
                          >
                            <AlertCircle size={16} />
                            <span className="text-sm">{errors.name}</span>
                          </motion.div>
                        )}

                        <p className="text-white/70 text-sm">
                          Choose a descriptive name that customers will love
                        </p>
                      </div>

                      {/* Category Section */}
                      {filteredCategories.length > 0 ? (
                        <div className="space-y-4">
                          <div className="flex items-center gap-3">
                            <div className="p-3 bg-blue-500/20 rounded-xl border border-blue-400/30">
                              <Package size={20} className="text-blue-400" />
                            </div>
                            <h3 className="text-white text-xl font-bold">Category</h3>
                          </div>
                          
                          <div className="relative">
                            <button
                              type="button"
                              onClick={() => setSelectOpen(!selectOpen)}
                              className={`w-full p-4 text-left bg-white/10 border rounded-2xl transition-all duration-300 flex items-center justify-between ${
                                selectOpen
                                  ? 'border-blue-400/50 bg-blue-500/10'
                                  : product.categoryId
                                  ? 'border-green-400/50 bg-green-500/10'
                                  : 'border-white/30 hover:border-white/50'
                              }`}
                            >
                              <span className={product.categoryId ? 'text-white' : 'text-white/60'}>
                                {product.categoryName || 'Select a category'}
                              </span>
                              <ChevronDown
                                size={20}
                                className={`text-white/60 transition-transform duration-300 ${
                                  selectOpen ? 'rotate-180' : ''
                                }`}
                              />
                            </button>

                            <AnimatePresence>
                              {selectOpen && (
                                <motion.div
                                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                  animate={{ opacity: 1, y: 0, scale: 1 }}
                                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                  transition={{ duration: 0.2 }}
                                  className="absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-xl border border-white/30 rounded-2xl overflow-hidden z-50"
                                >
                                  {filteredCategories.map((category) => (
                                    <button
                                      key={category._id}
                                      type="button"
                                      onClick={() => {
                                        updateProduct({ categoryId: category._id, categoryName: category.name });
                                        setSelectOpen(false);
                                        validateField('categoryId', category._id);
                                      }}
                                      className="w-full p-4 text-left text-white hover:bg-white/10 transition-colors duration-200 border-b border-white/10 last:border-b-0"
                                    >
                                      {category.name}
                                    </button>
                                  ))}
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                          
                          {errors.category && (
                            <motion.p
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              className="text-red-400 text-sm flex items-center gap-2"
                            >
                              <AlertCircle size={16} />
                              {errors.category}
                            </motion.p>
                          )}
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="flex items-center gap-3">
                            <div className="p-3 bg-yellow-500/20 rounded-xl border border-yellow-400/30">
                              <AlertCircle size={20} className="text-yellow-400" />
                            </div>
                            <h3 className="text-white text-xl font-bold">Category</h3>
                          </div>
                          
                          <div className="p-4 bg-yellow-500/10 border border-yellow-400/30 rounded-2xl">
                            <p className="text-yellow-200 text-sm">
                              No categories available. Please add categories from the products page first.
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Price Section */}
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 bg-green-500/20 rounded-xl border border-green-400/30">
                            <span className="text-green-400 text-xl">₪</span>
                          </div>
                          <h3 className="text-white text-xl font-bold">Price</h3>
                          <span className="text-red-400 text-lg">*</span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <label className="text-white/80 text-sm font-semibold">Regular Price (₪)</label>
                            <input
                              type="number"
                              placeholder="e.g., 25.00"
                              value={product.price || ''}
                              onChange={(e) => {
                                const price = parseFloat(e.target.value) || 0;
                                updateProduct({ price });
                                validateField('price', price);
                              }}
                              className={`w-full px-6 py-4 bg-white/10 border-2 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-green-400 transition-all duration-300 text-lg ${
                                errors.price ? 'border-red-400' : 'border-white/30'
                              }`}
                            />
                          </div>

                          <div className="space-y-2">
                            <label className="text-white/80 text-sm font-semibold">Discount Price (₪)</label>
                            <input
                              type="number"
                              placeholder="Optional"
                              value={product.discountPrice || ''}
                              onChange={(e) => {
                                const discountPrice = parseFloat(e.target.value) || 0;
                                updateProduct({ discountPrice });
                              }}
                              className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-green-400 transition-all duration-300 text-lg"
                            />
                          </div>
                        </div>

                        {errors.price && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 text-red-400"
                          >
                            <AlertCircle size={16} />
                            <span className="text-sm">{errors.price}</span>
                          </motion.div>
                        )}

                        <p className="text-white/70 text-sm">
                          Set competitive pricing. Discount price is optional for promotions.
                        </p>
                      </div>

                      {/* Enhanced Image Upload Section */}
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 bg-orange-500/20 rounded-xl border border-orange-400/30">
                            <ImageIcon size={20} className="text-orange-400" />
                          </div>
                          <h3 className="text-white text-xl font-bold">Product Image</h3>
                          <span className="text-red-400 text-lg">*</span>
                        </div>

                        {!product.image ? (
                          <div className={`relative border-2 border-dashed rounded-3xl p-12 text-center transition-all duration-300 ${
                            errors.image ? 'border-red-400 bg-red-500/10' : 'border-white/30 bg-white/5 hover:border-white/50 hover:bg-white/10'
                          }`}>
                            <div className="space-y-6">
                              <motion.div
                                className="mx-auto w-20 h-20 bg-gradient-to-r from-purple-600 to-blue-600 rounded-3xl flex items-center justify-center"
                                whileHover={{ scale: 1.1, rotate: 5 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                              >
                                <Upload size={32} className="text-white" />
                              </motion.div>

                              <div>
                                <h4 className="text-white text-xl font-bold mb-2">Upload Product Image</h4>
                                <p className="text-white/70 text-sm">
                                  Choose a high-quality image that showcases your product
                                </p>
                              </div>

                              <div className="flex gap-4 justify-center">
                                <motion.label
                                  whileHover={{ scale: 1.05, y: -2 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="cursor-pointer px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl border border-white/30 hover:from-purple-700 hover:to-blue-700 transition-all duration-300 flex items-center gap-3 font-bold shadow-xl"
                                >
                                  {imageLoading ? (
                                    <Loader size={20} className="animate-spin" />
                                  ) : (
                                    <Upload size={20} />
                                  )}
                                  {imageLoading ? 'Uploading...' : 'Choose File'}
                                  <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageUpload}
                                    className="hidden"
                                    disabled={imageLoading}
                                  />
                                </motion.label>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            <div className="relative bg-white/10 rounded-3xl p-6 border border-white/30">
                              <SafeImage
                                src={product.image}
                                alt={product.name}
                                className="w-full h-64 object-cover rounded-2xl"
                                fallbackIcon={
                                  <div className="flex flex-col items-center justify-center text-white/60 h-64">
                                    <ImageIcon size={48} />
                                    <span className="text-sm mt-2">Image not available</span>
                                  </div>
                                }
                              />
                            </div>

                            <div className="flex gap-4">
                              <motion.label
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                className="cursor-pointer flex-1 px-6 py-3 bg-white/15 text-white rounded-2xl border border-white/30 hover:bg-white/25 transition-all duration-300 flex items-center justify-center gap-3 font-bold"
                              >
                                <Upload size={18} />
                                Change Image
                                <input
                                  type="file"
                                  accept="image/*"
                                  onChange={handleImageUpload}
                                  className="hidden"
                                  disabled={imageLoading}
                                />
                              </motion.label>

                              <motion.button
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => {
                                  updateProduct({ image: '' });
                                  validateField('image', '');
                                }}
                                className="flex-1 px-6 py-3 bg-gradient-to-r from-red-500/30 to-pink-500/30 text-red-300 rounded-2xl border border-red-400/40 hover:from-red-500/40 hover:to-pink-500/40 transition-all duration-300 flex items-center justify-center gap-3 font-bold"
                              >
                                <Trash2 size={18} />
                                Remove
                              </motion.button>
                            </div>
                          </div>
                        )}

                        {errors.image && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 text-red-400"
                          >
                            <AlertCircle size={16} />
                            <span className="text-sm">{errors.image}</span>
                          </motion.div>
                        )}

                        <p className="text-white/70 text-sm">
                          High-quality images help customers make better decisions
                        </p>
                      </div>

                      {/* Optional Description Section */}
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="p-3 bg-indigo-500/20 rounded-xl border border-indigo-400/30">
                            <FileText size={20} className="text-indigo-400" />
                          </div>
                          <h3 className="text-white text-xl font-bold">Description</h3>
                          <span className="text-white/60 text-sm">(Optional)</span>
                        </div>

                        <textarea
                          placeholder="Describe your product (optional)"
                          value={product.description || ''}
                          onChange={(e) => updateProduct({ description: e.target.value })}
                          rows={4}
                          dir={detectTextDirection(product.description || '')}
                          className={`w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl text-white placeholder-white/60 focus:outline-none focus:border-indigo-400 transition-all duration-300 text-lg resize-none ${getTextDirectionClasses(product.description || '')}`}
                        />

                        <p className="text-white/70 text-sm">
                          Add details about ingredients, materials, or special features
                        </p>
                      </div>

                    </div>
                  </GlassCard>
                </motion.div>

                {/* Enhanced Submit Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                >
                  <GlassCard gradient="from-purple-600/25 to-blue-600/25" className="p-8" hoverEffect={false}>
                    <motion.button
                      onClick={getCompletionPercentage() === 100 ? validateAndContinue : handleIncompleteSubmit}
                      disabled={isLoading || apiError !== null}
                      whileHover={getCompletionPercentage() === 100 && apiError === null ? {
                        scale: 1.02,
                        y: -5,
                        boxShadow: "0 20px 40px -12px rgba(139, 92, 246, 0.4)"
                      } : getCompletionPercentage() < 100 ? {
                        scale: 1.02,
                        y: -2
                      } : {}}
                      whileTap={{ scale: 0.98 }}
                      className={`w-full p-8 rounded-3xl border-2 transition-all duration-300 flex items-center justify-center gap-6 font-bold text-xl ${
                        getCompletionPercentage() === 100 && apiError === null
                          ? 'bg-gradient-to-r from-purple-600 to-blue-600 border-white/30 text-white hover:from-purple-700 hover:to-blue-700 shadow-2xl'
                          : getCompletionPercentage() < 100
                          ? 'bg-gradient-to-r from-orange-500 to-red-500 border-white/30 text-white hover:from-orange-600 hover:to-red-600 shadow-xl'
                          : 'bg-white/10 border-white/20 text-white/50 cursor-not-allowed'
                      }`}
                    >
                      <div className="flex items-center gap-6">
                        {isLoading ? (
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          >
                            <Loader size={28} className="text-white" />
                          </motion.div>
                        ) : (
                          <motion.div
                            whileHover={{ scale: 1.2, rotate: 10 }}
                            transition={{ type: "spring", stiffness: 400, damping: 15 }}
                          >
                            <Rocket size={28} />
                          </motion.div>
                        )}

                        <div className="text-center">
                          <div className="text-2xl font-black">
                            {isLoading ? 'Creating Product...' : 
                             apiError ? 'Fix Errors to Continue' : 
                             getCompletionPercentage() === 100 ? 'Create Product & Add Options' : 'Complete Required Fields'}
                          </div>
                          {getCompletionPercentage() < 100 ? (
                            <div className="text-sm opacity-80 mt-1">
                              Click to see what needs to be completed
                            </div>
                          ) : apiError ? (
                            <div className="text-sm opacity-80 mt-1 text-red-300">
                              Please resolve the errors above
                            </div>
                          ) : (
                            <div className="text-sm opacity-80 mt-1">
                              Ready to create your amazing product!
                            </div>
                          )}
                        </div>

                        {!isLoading && getCompletionPercentage() === 100 && apiError === null && (
                          <motion.div
                            whileHover={{ x: 5 }}
                            transition={{ type: "spring", stiffness: 400, damping: 15 }}
                          >
                            <ArrowRight size={28} />
                          </motion.div>
                        )}
                      </div>
                    </motion.button>
                  </GlassCard>
                </motion.div>

              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default AddProductPage;
