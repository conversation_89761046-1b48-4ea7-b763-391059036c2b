import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Plus,
  Settings,
  Edit3,
  Trash2,
  Eye,
  Grid3X3,
  List,
  BarChart3,
  Save,
  Loader,
  CheckCircle2,
  AlertCircle,
  X,
  Search,
  Filter,
  Star,
  TrendingUp,
  Zap,
  Sparkles,
  Crown,
  Target,
  Award,
  ChevronRight,
  ShoppingBag,
  DollarSign,
  Tag,
  Image as ImageIcon,
  Palette,
  Shirt,
  Coffee,
  Utensils,
  Layers,
  Hash,
  Type,
  ToggleLeft,
  ToggleRight,
  Calendar,
  Clock,
  Store,
  RefreshCw
} from 'lucide-react';
import { SafeImage } from '../../../components/common/SafeImage';
import { ImageIssueNotification } from '../../../components/common/ImageIssueNotification';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';
import { useNavigate } from 'react-router-dom';

// Import the stores
import { useSupplierProductsStore } from '../../../stores/supplierProductsStore';
import { useSupplierCategories } from '../../../stores/supplierCategoriesStore';

// Import API service for supplier data
import { getSupplierById } from '../../../services/api';

// Import enhanced error handling
import { EnhancedErrorDisplay } from '../../../components/common/EnhancedErrorDisplay';
import type { ErrorInfo } from '../../../utils/errorHandler';
import { processApiResponse } from '../../../utils/errorHandler';

// Modern Glass Card Component - Proper z-index for header compatibility
const GlassCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  hoverEffect?: boolean;
}> = ({ children, className = '', gradient = 'from-white/10 to-white/5', hoverEffect = true }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={hoverEffect ? {
      y: -8,
      scale: 1.02,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)"
    } : {}}
    transition={{ type: "spring", stiffness: 300, damping: 30 }}
    className={`relative bg-gradient-to-br ${gradient} border border-white/30 rounded-3xl shadow-2xl overflow-hidden ${className}`}
    style={{
      zIndex: 10, // Lower z-index to not interfere with header
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);



// Success Display Component
const SuccessDisplay: React.FC<{ 
  message: string; 
  onDismiss: () => void; 
}> = ({ message, onDismiss }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="mb-6 p-6 bg-green-500/20 border border-green-400/30 rounded-2xl text-green-300 backdrop-blur-sm"
  >
    <div className="flex items-center gap-3">
      <motion.div
        animate={{ scale: [1, 1.1, 1] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <CheckCircle2 size={24} className="text-green-400" />
      </motion.div>
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-2">
          <span className="font-semibold text-green-200">Success:</span>
          <span className="text-sm bg-green-500/30 px-2 py-1 rounded-lg text-green-100">
            Operation Completed
          </span>
        </div>
        <p className="text-green-300 text-sm leading-relaxed">{message}</p>
      </div>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onDismiss}
        className="p-2 hover:bg-green-500/20 rounded-lg transition-colors"
        title="Dismiss"
      >
        <X size={16} />
      </motion.button>
    </div>
  </motion.div>
);

// Simple Background Orb Component - NO BLUR, LOW Z-INDEX
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06, // Very low opacity to prevent blur interference
      zIndex: -1, // Behind everything
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component - Behind everything
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

// Helper function to count options for a product
const getProductOptionsCount = (product: any, businessType?: string) => {
  let count = 0;
  
  // Only show relevant options based on business type
  if (businessType === 'restaurant') {
    // Restaurant-specific options
    if (product.restaurantOptions?.additions && Array.isArray(product.restaurantOptions.additions)) {
      count += product.restaurantOptions.additions.length;
    }
    
    if (product.restaurantOptions?.sides && Array.isArray(product.restaurantOptions.sides)) {
      count += product.restaurantOptions.sides.length;
    }
    
    if (product.restaurantOptions?.without && Array.isArray(product.restaurantOptions.without)) {
      count += product.restaurantOptions.without.length;
    }
  } else if (businessType === 'clothing') {
    // Clothing-specific options
    if (product.clothingOptions?.sizes && Array.isArray(product.clothingOptions.sizes)) {
      count += product.clothingOptions.sizes.length;
    }
    
    if (product.clothingOptions?.colors && Array.isArray(product.clothingOptions.colors)) {
      count += product.clothingOptions.colors.length;
    }
    
    if (product.clothingOptions?.gallery && Array.isArray(product.clothingOptions.gallery)) {
      count += product.clothingOptions.gallery.length;
    }
  } else {
    // For other business types, don't show any options, keep it for the restaurant and clothing business types
    count = 0;
  }
  
  // Safely check custom options (show for all business types)
  if (product.customOptions && Array.isArray(product.customOptions)) {
    count += product.customOptions.length;
  }
  
  return count;
};

// Helper function to get product options summary
const getProductOptionsSummary = (product: any, businessType?: string) => {
  const parts: string[] = [];
  
  // Only show relevant options based on business type
  if (businessType === 'restaurant') {
    // Restaurant-specific options
    if (product.restaurantOptions?.additions && Array.isArray(product.restaurantOptions.additions)) {
      parts.push(`${product.restaurantOptions.additions.length} additions`);
    }
    
    if (product.restaurantOptions?.sides && Array.isArray(product.restaurantOptions.sides)) {
      parts.push(`${product.restaurantOptions.sides.length} sides`);
    }
    
    if (product.restaurantOptions?.without && Array.isArray(product.restaurantOptions.without)) {
      parts.push(`${product.restaurantOptions.without.length} exclusions`);
    }
  } else if (businessType === 'clothing') {
    // Clothing-specific options
    if (product.clothingOptions?.sizes && Array.isArray(product.clothingOptions.sizes)) {
      parts.push(`${product.clothingOptions.sizes.length} sizes`);
    }
    
    if (product.clothingOptions?.colors && Array.isArray(product.clothingOptions.colors)) {
      parts.push(`${product.clothingOptions.colors.length} colors`);
    }
    
    if (product.clothingOptions?.gallery && Array.isArray(product.clothingOptions.gallery)) {
      parts.push(`${product.clothingOptions.gallery.length} gallery images`);
    }
  } else {
    // For other business types, don't show any options, keep it for the restaurant and clothing business types
    parts.push('No options');
  }
  
  // Safely check custom options (show for all business types)
  if (product.customOptions && Array.isArray(product.customOptions)) {
    parts.push(`${product.customOptions.length} custom options`);
  }
  
  return parts.length > 0 ? parts.join(', ') : 'No options';
};

// Enhanced Metric Card Component
const MetricCard: React.FC<{
  icon: React.ComponentType<any>;
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  gradient: string;
}> = ({ icon: Icon, title, value, change, changeType = 'neutral', gradient }) => (
  <GlassCard gradient={gradient} className="p-8 group">
    <div className="flex items-center justify-between mb-6">
      <motion.div
        className="p-4 bg-white/25 rounded-2xl border border-white/20 group-hover:bg-white/35 transition-all duration-300"
        whileHover={{ scale: 1.1, rotate: 5 }}
      >
        <Icon size={28} className="text-white drop-shadow-lg" />
      </motion.div>
      {change && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`flex items-center gap-2 px-3 py-2 rounded-xl border text-sm font-bold ${
            changeType === 'positive'
              ? 'text-green-200 bg-green-500/20 border-green-400/30' :
            changeType === 'negative'
              ? 'text-red-200 bg-red-500/20 border-red-400/30'
              : 'text-blue-200 bg-blue-500/20 border-blue-400/30'
          }`}
        >
          {change}
        </motion.div>
      )}
    </div>
    <div className="text-white">
      <motion.p
        className="text-4xl font-black mb-2 bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent"
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        {value}
      </motion.p>
      <p className="text-white/90 text-base font-semibold">{title}</p>
    </div>
  </GlassCard>
);

// Enhanced Quick Action Button Component
const QuickActionButton: React.FC<{
  icon: React.ComponentType<any>;
  label: string;
  onClick: () => void;
  gradient: string;
  isActive?: boolean;
}> = ({ icon: Icon, label, onClick, gradient, isActive = false }) => (
  <motion.button
    onClick={onClick}
    whileHover={{
      scale: 1.08,
      y: -8,
      rotateY: 5,
      boxShadow: "0 20px 40px -12px rgba(0, 0, 0, 0.3)"
    }}
    whileTap={{ scale: 0.95 }}
    transition={{ type: "spring", stiffness: 300, damping: 20 }}
    className={`relative group p-6 bg-gradient-to-br ${gradient} rounded-3xl shadow-2xl border border-white/30 overflow-hidden transform-gpu ${
      isActive ? 'ring-2 ring-white/50' : ''
    }`}
  >
    {/* Animated background overlay */}
    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-500 transform -translate-x-full group-hover:translate-x-full" />

    {/* Glow effect */}
    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

    <div className="relative z-10 text-center">
      <motion.div
        className="mb-3 flex justify-center"
        whileHover={{ scale: 1.2, rotate: 10 }}
        transition={{ type: "spring", stiffness: 400, damping: 15 }}
      >
        <div className="p-3 bg-white/20 rounded-2xl border border-white/30">
          <Icon size={24} className="text-white drop-shadow-lg" />
        </div>
      </motion.div>
      <p className="text-white font-bold text-sm tracking-wide">{label}</p>
    </div>
  </motion.button>
);

// Enhanced Modal Component
const Modal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  gradient?: string;
  icon?: React.ReactNode;
  subtitle?: string;
}> = ({ isOpen, onClose, title, children, gradient = 'from-purple-600 to-blue-600', icon, subtitle }) => (
  <AnimatePresence>
    {isOpen && (
      <>
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 50 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div className="relative bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-xl rounded-3xl shadow-2xl max-w-md w-full overflow-hidden border border-white/30">
            {/* Enhanced Header with Glass Effect */}
            <div className={`relative bg-gradient-to-r ${gradient} p-6 overflow-hidden`}>
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-0 left-0 w-32 h-32 bg-white/20 rounded-full -translate-x-16 -translate-y-16"></div>
                <div className="absolute bottom-0 right-0 w-24 h-24 bg-white/20 rounded-full translate-x-12 translate-y-12"></div>
              </div>
              
              <div className="relative flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <motion.div 
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 300, damping: 20 }}
                    className="p-3 bg-white/20 rounded-2xl border border-white/30 backdrop-blur-sm shadow-lg"
                  >
                    {icon || <Plus size={24} className="text-white" />}
                  </motion.div>
                  <div>
                    <motion.h3 
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-white text-2xl font-bold tracking-tight"
                    >
                      {title}
                    </motion.h3>
                    {subtitle && (
                      <motion.p 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                        className="text-white/90 text-sm font-medium"
                      >
                        {subtitle}
                      </motion.p>
                    )}
                  </div>
                </div>

                <motion.button
                  initial={{ scale: 0, rotate: 180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.5, type: "spring", stiffness: 300, damping: 20 }}
                  onClick={onClose}
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  className="p-3 bg-white/20 rounded-2xl border border-white/30 hover:bg-white/30 transition-all duration-300 backdrop-blur-sm shadow-lg"
                >
                  <X size={20} className="text-white" />
                </motion.button>
              </div>
            </div>

            {/* Enhanced Content with Glass Effect */}
            <div className="p-8 bg-gradient-to-br from-white/50 to-white/30 backdrop-blur-sm">
              {children}
            </div>
          </div>
        </motion.div>
      </>
    )}
  </AnimatePresence>
);

// Category Chip Component
const CategoryChip: React.FC<{
  category: string;
  isSelected: boolean;
  isAll?: boolean;
  onSelect: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}> = ({ category, isSelected, isAll = false, onSelect, onEdit, onDelete }) => (
  <motion.div
    whileHover={{ scale: 1.05, y: -2 }}
    whileTap={{ scale: 0.95 }}
    className={`relative group flex items-center gap-3 px-4 py-3 rounded-2xl border transition-all duration-300 cursor-pointer ${
      isSelected
        ? 'bg-gradient-to-r from-purple-600 to-blue-600 border-white/30 text-white shadow-lg'
        : 'bg-white/10 border-white/20 text-white/90 hover:bg-white/20'
    }`}
    onClick={onSelect}
  >
    <span className="font-semibold text-sm whitespace-nowrap">{category}</span>

    {!isAll && (
      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <motion.button
          onClick={(e) => {
            e.stopPropagation();
            onEdit?.();
          }}
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.8 }}
          className="p-1 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
        >
          <Edit3 size={12} className="text-white" />
        </motion.button>
        <motion.button
          onClick={(e) => {
            e.stopPropagation();
            onDelete?.();
          }}
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.8 }}
          className="p-1 bg-red-500/20 rounded-lg hover:bg-red-500/30 transition-colors"
        >
          <Trash2 size={12} className="text-red-300" />
        </motion.button>
      </div>
    )}
  </motion.div>
);

// Enhanced Product Card Component for List View
const ProductCard: React.FC<{ product: any; businessType?: string }> = ({ product, businessType }) => {
  const navigate = useNavigate();
  const hasDiscount = product.discountPrice && product.discountPrice > 0;
  const optionsCount = getProductOptionsCount(product, businessType);
  const optionsSummary = getProductOptionsSummary(product, businessType);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{
        boxShadow: "0 30px 60px -12px rgba(117, 41, 179, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.3), 0 0 30px rgba(117, 41, 179, 0.3)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      className="group relative bg-gradient-to-br from-white/20 via-white/15 to-white/10 border border-white/40 rounded-3xl p-8 shadow-2xl overflow-hidden product-card"
    >
      {/* Multiple layered shimmer effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1500" />
      <div className="absolute inset-0 bg-gradient-to-l from-transparent via-purple-400/20 to-transparent translate-x-full group-hover:-translate-x-full transition-transform duration-2000" />

      {/* Enhanced glow effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" />

      {/* Floating sparkles */}
      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <motion.div
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Sparkles size={16} className="text-yellow-300" />
        </motion.div>
      </div>

      <div className="relative z-10 flex gap-8 card-content">
        {/* Enhanced Product Image */}
        <div className="relative">
          <motion.div
            className="relative"
            whileHover={{ scale: 1.1, rotate: 3 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <div className={`absolute -inset-2 rounded-3xl blur-lg opacity-60 ${
              optionsCount > 0 ? 'bg-gradient-to-r from-green-400 to-emerald-500' : 'bg-gradient-to-r from-orange-400 to-red-500'
            }`} />
            <SafeImage
              src={product.image}
              alt={product.name}
              className={`relative w-28 h-28 rounded-3xl object-cover border-3 shadow-2xl ${
                optionsCount > 0 ? 'border-green-400/60' : 'border-orange-400/60'
              }`}
              fallbackIcon={
                <div className="flex flex-col items-center justify-center text-white/60">
                  <ImageIcon size={24} />
                  <span className="text-xs mt-1">No Image</span>
                </div>
              }
            />

            {/* Enhanced options badge */}
            {optionsCount > 0 && (
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                whileHover={{ scale: 1.2, rotate: 10 }}
                className="absolute -top-3 -right-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-sm font-black px-3 py-2 rounded-2xl border-3 border-white shadow-2xl"
              >
                <div className="flex items-center gap-1">
                  <Crown size={12} />
                  {optionsCount}
                </div>
              </motion.div>
            )}

            {/* Pulsing ring for products without options */}
            {optionsCount === 0 && (
              <motion.div
                animate={{ scale: [1, 1.3, 1], opacity: [0.5, 0.8, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute -inset-2 border-2 border-orange-400 rounded-3xl"
              />
            )}
          </motion.div>
        </div>

        {/* Enhanced Product Info */}
        <div className="flex-1 flex flex-col justify-between">
          <div>
            <div className="flex items-start justify-between mb-4">
              <div>
                <motion.h4
                  className="text-white text-2xl font-black mb-2 bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                  whileHover={{ scale: 1.05 }}
                >
                  {product.name}
                </motion.h4>

                <motion.div
                  className="flex items-center gap-3 mb-3"
                  whileHover={{ x: 5 }}
                >
                  <div className="p-2 bg-white/20 rounded-xl border border-white/30">
                    <Tag size={14} className="text-white/90" />
                  </div>
                  <span className="text-white/90 text-sm font-semibold bg-white/10 px-3 py-1 rounded-xl border border-white/20">
                    {product.categoryName}
                  </span>
                </motion.div>
              </div>

              {optionsCount === 0 && (
                <motion.div
                  initial={{ scale: 0, rotate: -10 }}
                  animate={{ scale: 1, rotate: 0 }}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-black px-4 py-2 rounded-2xl border-2 border-white/30 shadow-lg"
                >
                  <div className="flex items-center gap-2">
                    <AlertCircle size={14} />
                    NEEDS SETUP
                  </div>
                </motion.div>
              )}
            </div>

            {/* Enhanced Price Display */}
            <motion.div
              className="flex items-center gap-4 mb-4"
              whileHover={{ translateY: -5 }}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30">
                  <span className="text-green-400 text-lg">₪</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className={`text-3xl font-black ${hasDiscount ? 'text-red-400 line-through' : 'text-green-400'} drop-shadow-lg`}>
                    ₪{product.price}
                  </span>
                  {hasDiscount ? (
                    <span className="text-white/60 text-xl font-semibold">
                      ₪{product.discountPrice}
                    </span>
                  ) : null}
                </div>
              </div>

              {hasDiscount ? (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-xl border border-white/30"
                >
                  SALE!
                </motion.div>
              ) : null}
            </motion.div>

            {/* Enhanced Options Summary */}
            {optionsCount > 0 ? (
              <motion.div
                className="mb-6 p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-2xl border border-green-400/30"
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle2 size={16} className="text-green-400" />
                  <p className="text-green-400 text-sm font-bold">
                    {optionsCount} options configured
                  </p>
                </div>
                <p className="text-white/80 text-sm leading-relaxed">
                  {optionsSummary}
                </p>
              </motion.div>
            ) : (
              <motion.div
                className="mb-6 p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-2xl border border-orange-400/30"
                whileHover={{ scale: 1.02 }}
                animate={{
                  boxShadow: ["0 0 0 0 rgba(251, 146, 60, 0.4)", "0 0 0 10px rgba(251, 146, 60, 0)", "0 0 0 0 rgba(251, 146, 60, 0)"]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <div className="flex items-center gap-2">
                  <AlertCircle size={16} className="text-orange-400" />
                  <p className="text-orange-400 text-sm font-bold italic">
                    No options configured yet
                  </p>
                </div>
              </motion.div>
            )}
          </div>

          {/* Enhanced Action Buttons */}
          <div className="flex gap-4">
            <motion.button
              whileHover={{
                scale: 1.08,
                y: -3,
                boxShadow: "0 15px 30px -5px rgba(139, 92, 246, 0.4)"
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(`/supplier/products/${product.id}/manage-options`)}
              className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl border border-white/30 hover:from-purple-700 hover:to-blue-700 transition-all font-bold text-sm shadow-xl group"
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <Settings size={18} />
              </motion.div>
              Options
              <ChevronRight size={16} className="group-hover:translate-x-1 transition-transform" />
            </motion.button>

            <motion.button
              whileHover={{
                scale: 1.08,
                y: -3,
                boxShadow: "0 15px 30px -5px rgba(255, 255, 255, 0.2)"
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(`/supplier/products/${product.id}/edit`)}
              className="flex items-center gap-3 px-6 py-3 bg-white/15 text-white rounded-2xl border border-white/30 hover:bg-white/25 transition-all font-bold text-sm shadow-xl backdrop-blur-sm group"
            >
              <Edit3 size={18} />
              Edit
            </motion.button>

            <motion.button
              whileHover={{
                scale: 1.08,
                y: -3,
                boxShadow: "0 15px 30px -5px rgba(239, 68, 68, 0.4)"
              }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-red-500/30 to-pink-500/30 text-red-300 rounded-2xl border border-red-400/40 hover:from-red-500/40 hover:to-pink-500/40 transition-all font-bold text-sm shadow-xl group"
            >
              <Trash2 size={18} />
              Delete
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Enhanced Product Card Component for Grid View
const ProductCardGrid: React.FC<{ product: any; businessType?: string }> = ({ product, businessType }) => {
  const navigate = useNavigate();
  const hasDiscount = product.discountPrice && product.discountPrice > 0;
  const optionsCount = getProductOptionsCount(product, businessType);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, rotateY: -15 }}
      animate={{ opacity: 1, scale: 1, rotateY: 0 }}
      whileHover={{
        boxShadow: "0 35px 70px -12px rgba(103, 179, 41, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.3), 0 0 40px rgba(103, 179, 41, 0.3)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
      className="group relative bg-gradient-to-br from-white/25 via-white/20 to-white/15 border border-white/40 rounded-3xl p-6 shadow-2xl overflow-hidden h-96 product-card"
    >
      {/* Multiple layered shimmer effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1500" />
      <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 via-blue-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

      {/* Enhanced glow effects */}
      <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/30 to-blue-600/30 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" />
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* Floating elements */}
      <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <motion.div
          animate={{
            rotate: [0, 360],
            scale: [1, 1.3, 1]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Star size={14} className="text-yellow-300" />
        </motion.div>
      </div>

      <div className="relative z-10 flex flex-col h-full card-content">
        {/* Enhanced Product Image */}
        <div className="relative flex justify-center mb-6">
          <motion.div
            className="relative"
            whileHover={{ scale: 1.15, rotate: 8 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            {/* Image glow */}
            <div className={`absolute -inset-3 rounded-3xl blur-lg opacity-60 ${
              optionsCount > 0 ? 'bg-gradient-to-r from-green-400 to-emerald-500' : 'bg-gradient-to-r from-orange-400 to-red-500'
            }`} />

            <SafeImage
              src={product.image}
              alt={product.name}
              className={`relative w-24 h-24 rounded-3xl object-cover border-3 shadow-2xl ${
                optionsCount > 0 ? 'border-green-400/60' : 'border-orange-400/60'
              }`}
              fallbackIcon={
                <div className="flex flex-col items-center justify-center text-white/60">
                  <ImageIcon size={20} />
                  <span className="text-xs mt-1">No Image</span>
                </div>
              }
            />

            {/* Enhanced options badge */}
            {optionsCount > 0 && (
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                whileHover={{ scale: 1.3, rotate: 15 }}
                className="absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs font-black px-2 py-1 rounded-xl border-2 border-white shadow-xl"
              >
                <div className="flex items-center gap-1">
                  <Award size={10} />
                  {optionsCount}
                </div>
              </motion.div>
            )}

            {/* Status indicator ring */}
            {optionsCount === 0 && (
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.3, 0.7, 0.3],
                  rotate: [0, 360]
                }}
                transition={{ duration: 3, repeat: Infinity }}
                className="absolute -inset-3 border-2 border-dashed border-orange-400 rounded-3xl"
              />
            )}
          </motion.div>
        </div>

        {/* Enhanced Product Info */}
        <div className="flex-1 flex flex-col justify-between text-center">
          <div>
            <motion.h4
              className="text-white text-lg font-black mb-3 line-clamp-2 bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent leading-tight"
              whileHover={{ scale: 1.05 }}
            >
              {product.name}
            </motion.h4>

            <motion.div
              className="flex items-center justify-center gap-2 mb-4"
              whileHover={{ scale: 1.05 }}
            >
              <div className="p-1.5 bg-white/20 rounded-lg border border-white/30">
                <Tag size={12} className="text-white/90" />
              </div>
              <span className="text-white/90 text-xs font-semibold bg-white/15 px-2 py-1 rounded-lg border border-white/20">
                {product.categoryName}
              </span>
            </motion.div>

            {/* Enhanced Price Display */}
            <motion.div
              className="flex items-center justify-center gap-2 mb-4"
              whileHover={{ scale: 1.1 }}
            >
              <div className="flex items-center gap-2">
                <span className={`text-2xl font-black ${hasDiscount ? 'text-red-400 line-through' : 'text-green-400'} drop-shadow-lg`}>
                  ₪{product.price}
                </span>
                {hasDiscount ? (
                  <span className="text-white/60 text-sm font-semibold">
                    ₪{product.discountPrice}
                  </span>
                ) : null}
              </div>

              {hasDiscount ? (
                <motion.div
                  initial={{ scale: 0, rotate: -10 }}
                  animate={{ scale: 1, rotate: 0 }}
                  className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-2 py-1 rounded-lg border border-white/30"
                >
                  SALE!
                </motion.div>
              ) : null}
            </motion.div>

            {/* Enhanced Status */}
            {optionsCount > 0 ? (
              <motion.div
                className="mb-4 p-3 bg-gradient-to-r from-green-500/25 to-emerald-500/25 rounded-2xl border border-green-400/40"
                whileHover={{ scale: 1.05 }}
              >
                <div className="flex items-center justify-center gap-2">
                  <CheckCircle2 size={14} className="text-green-400" />
                  <p className="text-green-400 text-sm font-bold">
                    {optionsCount} options
                  </p>
                </div>
              </motion.div>
            ) : (
              <motion.div
                className="mb-4 p-3 bg-gradient-to-r from-orange-500/25 to-red-500/25 rounded-2xl border border-orange-400/40"
                whileHover={{ scale: 1.05 }}
                animate={{
                  boxShadow: ["0 0 0 0 rgba(251, 146, 60, 0.4)", "0 0 0 8px rgba(251, 146, 60, 0)", "0 0 0 0 rgba(251, 146, 60, 0)"]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <div className="flex items-center justify-center gap-2">
                  <AlertCircle size={14} className="text-orange-400" />
                  <p className="text-orange-400 text-sm font-bold">
                    No options
                  </p>
                </div>
              </motion.div>
            )}
          </div>

          {/* Enhanced Action Buttons */}
          <div className="space-y-3">
            <motion.button
              whileHover={{
                scale: 1.08,
                y: -3,
                boxShadow: "0 15px 30px -5px rgba(139, 92, 246, 0.5)"
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(`/supplier/products/${product.id}/manage-options`)}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl border border-white/30 hover:from-purple-700 hover:to-blue-700 transition-all font-bold text-sm shadow-xl group"
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <Settings size={16} />
              </motion.div>
              Options
              <ChevronRight size={14} className="group-hover:translate-x-1 transition-transform" />
            </motion.button>

            <div className="flex gap-2">
              <motion.button
                whileHover={{
                  scale: 1.08,
                  y: -2,
                  boxShadow: "0 10px 20px -5px rgba(255, 255, 255, 0.2)"
                }}
                whileTap={{ scale: 0.95 }}
                className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-white/15 text-white rounded-xl border border-white/30 hover:bg-white/25 transition-all font-bold text-xs shadow-lg backdrop-blur-sm"
              >
                <Edit3 size={12} />
                Edit
              </motion.button>

              <motion.button
                whileHover={{
                  scale: 1.08,
                  y: -2,
                  boxShadow: "0 10px 20px -5px rgba(239, 68, 68, 0.4)"
                }}
                whileTap={{ scale: 0.95 }}
                className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-gradient-to-r from-red-500/30 to-pink-500/30 text-red-300 rounded-xl border border-red-400/40 hover:from-red-500/40 hover:to-pink-500/40 transition-all font-bold text-xs shadow-lg"
              >
                <Trash2 size={12} />
                Delete
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const SupplierProductsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useCurrentUserData();
  const { 
    products, 
    setProducts, 
    saveProducts, 
    isSaving, 
    lastSaved, 
    loadProducts,
    setSupplierId,
    supplierId,
    updateProductsCategoryName
  } = useSupplierProductsStore();
  const { categories, loadCategories, addCategory, deleteCategory, selectCategory, selectedCategory, renameCategory, setSupplierId: setCategoriesSupplierId } = useSupplierCategories();

  // Enhanced state management
  const [newCatModal, setNewCatModal] = useState(false);
  const [newCat, setNewCat] = useState('');
  const [editCat, setEditCat] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showSummary, setShowSummary] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Loading states for category operations
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [isEditingCategory, setIsEditingCategory] = useState(false);
  const [isDeletingCategory, setIsDeletingCategory] = useState(false);

  // State for supplier data
  const [supplierData, setSupplierData] = useState<any>(null);
  const [isLoadingSupplier, setIsLoadingSupplier] = useState(false);
  const [error, setError] = useState<ErrorInfo | null>(null);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [productsError, setProductsError] = useState<ErrorInfo | null>(null);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [categoriesError, setCategoriesError] = useState<ErrorInfo | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    // Set the supplier ID in the store
    if (user?.supplierId) {
      setSupplierId(user.supplierId);
      setCategoriesSupplierId(user.supplierId);
    } else if (user && !user.supplierId) {
      setError({
        type: 'forbidden',
        message: 'You are not linked to any supplier. Please contact support to link your account.',
        userFriendlyMessage: 'You are not linked to any supplier. Please contact support to link your account.',
        statusCode: 403
      });
    } else if (!user) {
      setError({
        type: 'auth',
        message: 'Please log in to access the products page.',
        userFriendlyMessage: 'Please log in to access the products page.',
        statusCode: 401
      });
    }
  }, [user, setSupplierId, setCategoriesSupplierId]);

  // Load supplier data function
  const loadSupplierData = async () => {
    if (user?.supplierId) {
      setIsLoadingSupplier(true);
      setError(null);
      try {
        const response = await getSupplierById(user.supplierId);
        const processedResponse = processApiResponse(response);
        
        if (processedResponse.success && response.data) {
          setSupplierData(response.data);
        } else if (processedResponse.error) {
          setError(processedResponse.error);
        } else {
          throw new Error('Failed to load supplier data');
        }
      } catch (error) {
        console.error('Error loading supplier data:', error);
        setError({
          type: 'unknown',
          message: 'Failed to load supplier data',
          userFriendlyMessage: 'Failed to load supplier data. Please try again.',
          statusCode: 500
        });
      } finally {
        setIsLoadingSupplier(false);
      }
    }
  };

  // Load supplier data from backend
  useEffect(() => {
    loadSupplierData();
  }, [user?.supplierId]);

  useEffect(() => {
    // Load products from backend when supplierId is set
    if (supplierId) {
      setIsLoadingProducts(true);
      setProductsError(null);
      const loadProductsAsync = async () => {
        try {
          await loadProducts();
        } catch (error) {
          console.error('Error loading products:', error);
          setProductsError({
            type: 'unknown',
            message: 'Failed to load products',
            userFriendlyMessage: 'Failed to load products. Please try again.',
            statusCode: 500
          });
        } finally {
          setIsLoadingProducts(false);
        }
      };
      loadProductsAsync();
    }
  }, [supplierId, loadProducts]);

  // Load categories from backend when supplierId is set
  useEffect(() => {
    if (user?.supplierId) {
      setIsLoadingCategories(true);
      setCategoriesError(null);
      const loadCategoriesAsync = async () => {
        try {
          await loadCategories(user.supplierId!);
        } catch (error) {
          console.error('Error loading categories:', error);
          setCategoriesError({
            type: 'unknown',
            message: 'Failed to load categories',
            userFriendlyMessage: 'Failed to load categories. Please try again.',
            statusCode: 500
          });
        } finally {
          setIsLoadingCategories(false);
        }
      };
      loadCategoriesAsync();
    }
  }, [user?.supplierId, loadCategories]);

  // Set default category selection when categories are loaded
  useEffect(() => {
    if (categories.length > 0 && selectedCategory === null) {
      // Set the first category as selected by default, or null for all products
      selectCategory(null);
    }
  }, [categories, selectedCategory, selectCategory]);

  // Remove the old useEffect that was manually adding categories from products
  // Categories are now loaded directly from the backend

  // Save products function
  const handleSaveProducts = async () => {
    if (products.length === 0) {
      setError({
        type: 'validation',
        message: 'No products to save. Please add some products first.',
        userFriendlyMessage: 'No products to save. Please add some products first.',
        statusCode: 400
      });
      return;
    }
    
    try {
      setError(null);
      setSuccessMessage(null);
      await saveProducts();
      setSuccessMessage('Products saved successfully!');
      // Auto-dismiss success message after 5 seconds
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (error) {
      console.error('Failed to save products:', error);
      setError({
        type: 'unknown',
        message: 'Failed to save products',
        userFriendlyMessage: 'Failed to save products. Please try again.',
        statusCode: 500
      });
    }
  };

  // Get total options count across all products
  const getTotalOptionsCount = () => {
    return products.reduce((total: number, product: any) => total + getProductOptionsCount(product, user?.businessType), 0);
  };

  useEffect(() => {
    // Categories are now loaded directly from the backend store
    // No need to manually manage 'All' category or selection
  }, []);

  const filteredProducts = (selectedCategory && selectedCategory !== null)
    ? products.filter((p: any) => p.categoryName === selectedCategory)
    : products;

  const searchedProducts = searchQuery
    ? filteredProducts.filter((p: any) =>
        p.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        p.categoryName.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : filteredProducts;

  // Category management functions
  const handleAddCategory = async (categoryName: string) => {
    if (!categoryName.trim()) {
      setError({
        type: 'validation',
        message: 'Category name cannot be empty',
        userFriendlyMessage: 'Category name cannot be empty',
        statusCode: 400
      });
      return;
    }
    
    try {
      setIsAddingCategory(true);
      setError(null);
      setSuccessMessage(null);
      
      // Add category to the store (which will update the backend)
      const result = await addCategory(categoryName);
      
      if (result.success) {
        setSuccessMessage(result.message);
        setNewCatModal(false);
        setNewCat('');
        // Auto-dismiss success message after 3 seconds
        setTimeout(() => setSuccessMessage(null), 3000);
      } else {
        setError({
          type: 'validation',
          message: result.message,
          userFriendlyMessage: result.message,
          statusCode: 400
        });
      }
    } catch (error) {
      console.error('Failed to add category:', error);
      setError({
        type: 'unknown',
        message: 'Failed to add category',
        userFriendlyMessage: 'Failed to add category. Please try again.',
        statusCode: 500
      });
    } finally {
      setIsAddingCategory(false);
    }
  };

  const handleEditCategory = async (oldCategory: string, newCategory: string) => {
    if (!newCategory.trim()) {
      setError({
        type: 'validation',
        message: 'Category name cannot be empty',
        userFriendlyMessage: 'Category name cannot be empty',
        statusCode: 400
      });
      return;
    }
    
    if (oldCategory === newCategory.trim()) {
      setError({
        type: 'validation',
        message: 'New category name must be different from the current one',
        userFriendlyMessage: 'New category name must be different from the current one',
        statusCode: 400
      });
      return;
    }
    
    try {
      setIsEditingCategory(true);
      setError(null);
      setSuccessMessage(null);
      
      // Create a callback to update products when category is renamed
      const updateProductsCallback = (oldCat: string, newCat: string) => {
        // Update products in the products store
        updateProductsCategoryName(oldCat, newCat);
      };
      
      // Rename category in the store (which will update the backend and products)
      const result = await renameCategory(oldCategory, newCategory, updateProductsCallback);
      
      if (result.success) {
        setSuccessMessage(result.message);
        setEditCat(null);
        setEditedValue('');
        // Auto-dismiss success message after 3 seconds
        setTimeout(() => setSuccessMessage(null), 3000);
      } else {
        setError({
          type: 'validation',
          message: result.message,
          userFriendlyMessage: result.message,
          statusCode: 400
        });
      }
    } catch (error) {
      console.error('Failed to edit category:', error);
      setError({
        type: 'unknown',
        message: 'Failed to edit category',
        userFriendlyMessage: 'Failed to edit category. Please try again.',
        statusCode: 500
      });
    } finally {
      setIsEditingCategory(false);
    }
  };

  const handleDeleteCategory = async (categoryName: string) => {
    // Check if category has products
    const productsInCategory = products.filter((p: any) => p.categoryName === categoryName);
    if (productsInCategory.length > 0) {
      setError({
        type: 'conflict',
        message: `Cannot delete category "${categoryName}" because it contains ${productsInCategory.length} product(s). Please move or delete the products first.`,
        userFriendlyMessage: `Cannot delete category "${categoryName}" because it contains ${productsInCategory.length} product(s). Please move or delete the products first.`,
        statusCode: 409
      });
      return;
    }
    
    try {
      setIsDeletingCategory(true);
      setError(null);
      setSuccessMessage(null);
      
      // Delete category from the store (which will update the backend)
      const result = await deleteCategory(categoryName);
      
      if (result.success) {
        setSuccessMessage(result.message);
        // Auto-dismiss success message after 3 seconds
        setTimeout(() => setSuccessMessage(null), 3000);
      } else {
        setError({
          type: 'validation',
          message: result.message,
          userFriendlyMessage: result.message,
          statusCode: 400
        });
      }
    } catch (error) {
      console.error('Failed to delete category:', error);
      setError({
        type: 'unknown',
        message: 'Failed to delete category',
        userFriendlyMessage: 'Failed to delete category. Please try again.',
        statusCode: 500
      });
    } finally {
      setIsDeletingCategory(false);
    }
  };

  // Retry functions for error recovery
  const retryLoadSupplierData = async () => {
    if (user?.supplierId) {
      setIsLoadingSupplier(true);
      setError(null);
      try {
        const response = await getSupplierById(user.supplierId);
        const processedResponse = processApiResponse(response);
        
        if (processedResponse.success && response.data) {
          setSupplierData(response.data);
        } else if (processedResponse.error) {
          setError(processedResponse.error);
        } else {
          throw new Error('Failed to load supplier data');
        }
      } catch (error) {
        console.error('Error loading supplier data:', error);
        setError({
          type: 'unknown',
          message: 'Failed to load supplier data',
          userFriendlyMessage: 'Failed to load supplier data. Please try again.',
          statusCode: 500
        });
      } finally {
        setIsLoadingSupplier(false);
      }
    }
  };

  const retryLoadProducts = () => {
    if (supplierId) {
      setIsLoadingProducts(true);
      setProductsError(null);
      try {
        loadProducts();
      } catch (error) {
        console.error('Error loading products:', error);
        setProductsError({
          type: 'unknown',
          message: 'Failed to load products',
          userFriendlyMessage: 'Failed to load products. Please try again.',
          statusCode: 500
        });
      } finally {
        setIsLoadingProducts(false);
      }
    }
  };

  const retryLoadCategories = () => {
    if (user?.supplierId) {
      setIsLoadingCategories(true);
      setCategoriesError(null);
      try {
        loadCategories(user.supplierId);
      } catch (error) {
        console.error('Error loading categories:', error);
        setCategoriesError({
          type: 'unknown',
          message: 'Failed to load categories',
          userFriendlyMessage: 'Failed to load categories. Please try again.',
          statusCode: 500
        });
      } finally {
        setIsLoadingCategories(false);
      }
    }
  };

  const retryAll = () => {
    setError(null);
    retryLoadSupplierData();
    retryLoadCategories();
    retryLoadProducts();
  };

  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* Beautiful hover effects without scaling */
        .product-card {
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .product-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s ease;
          z-index: 1;
        }

        .product-card:hover::before {
          left: 100%;
        }

        .product-card:hover {
          border-color: rgba(255, 255, 255, 0.6);
          background: linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.15));
        }

        .product-card:hover .card-content {
          transform: translateY(-2px);
        }

        .card-content {
          transition: transform 0.3s ease;
          position: relative;
          z-index: 2;
        }
      `}</style>

      {/* Add Category Modal */}
      <Modal
        isOpen={newCatModal}
        onClose={() => setNewCatModal(false)}
        title="Add New Category"
        subtitle="Create a new product category to organize your products"
        icon={<Plus size={24} className="text-white" />}
        gradient="from-emerald-600 to-teal-600"
      >
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-6"
        >
          {/* Enhanced Input Field */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Tag size={16} className="text-emerald-600" />
              Category Name
            </label>
            <div className="relative group">
              <input
                type="text"
                placeholder="e.g., Electronics, Clothing, Food..."
                value={newCat}
                onChange={(e) => setNewCat(e.target.value)}
                className="w-full px-5 py-4 bg-white/80 border-2 border-gray-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:ring-4 focus:ring-emerald-500/20 focus:border-emerald-500 outline-none transition-all duration-300 shadow-lg group-hover:border-emerald-300"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && newCat.trim()) {
                    handleAddCategory(newCat.trim());
                  }
                }}
                autoFocus
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              
              {/* Focus indicator */}
              <motion.div 
                className="absolute inset-0 rounded-2xl border-2 border-emerald-500/0 pointer-events-none"
                animate={{
                  scale: [1, 1.02, 1],
                  opacity: [0, 0.3, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </div>
            <p className="text-xs text-gray-500">Press Enter to quickly add the category</p>
          </div>

          {/* Enhanced Buttons */}
          <div className="flex gap-4 pt-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setNewCatModal(false)}
              className="flex-1 px-6 py-4 bg-gray-100 border-2 border-gray-200 rounded-2xl text-gray-700 hover:bg-gray-200 hover:border-gray-300 transition-all duration-300 font-medium shadow-lg hover:shadow-xl"
            >
              Cancel
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                if (newCat.trim()) {
                  handleAddCategory(newCat.trim());
                }
              }}
              disabled={!newCat.trim() || isAddingCategory}
              className="flex-1 px-6 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-2xl hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
            >
              {isAddingCategory ? (
                <>
                  <Loader size={18} className="animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Plus size={18} />
                  Add Category
                </>
              )}
            </motion.button>
          </div>
        </motion.div>
      </Modal>

      {/* Edit Category Modal */}
      <Modal
        isOpen={editCat !== null}
        onClose={() => {
          setEditCat(null);
          setEditedValue('');
        }}
        title="Edit Category"
        subtitle={`Rename "${editCat}" to something new`}
        icon={<Edit3 size={24} className="text-white" />}
        gradient="from-blue-600 to-indigo-600"
      >
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-6"
        >
          {/* Current Category Display */}
          <div className="bg-blue-50 border-2 border-blue-200 rounded-2xl p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-xl">
                <Tag size={16} className="text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-800">Current Name</p>
                <p className="text-lg font-bold text-blue-900">{editCat}</p>
              </div>
            </div>
          </div>

          {/* Enhanced Input Field */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <Edit3 size={16} className="text-blue-600" />
              New Category Name
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Enter new name..."
                value={editedValue}
                onChange={(e) => setEditedValue(e.target.value)}
                className="w-full px-5 py-4 bg-white/80 border-2 border-gray-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 outline-none transition-all duration-300 shadow-lg"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && editedValue.trim()) {
                    handleEditCategory(editCat!, editedValue.trim());
                  }
                }}
                autoFocus
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/10 to-indigo-500/10 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
            <p className="text-xs text-gray-500">Press Enter to quickly update the category</p>
          </div>

          {/* Enhanced Buttons */}
          <div className="flex gap-4 pt-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setEditCat(null);
                setEditedValue('');
              }}
              className="flex-1 px-6 py-4 bg-gray-100 border-2 border-gray-200 rounded-2xl text-gray-700 hover:bg-gray-200 hover:border-gray-300 transition-all duration-300 font-medium shadow-lg hover:shadow-xl"
            >
              Cancel
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                if (editCat && editedValue.trim()) {
                  handleEditCategory(editCat, editedValue.trim());
                }
              }}
              disabled={!editedValue.trim() || isEditingCategory}
              className="flex-1 px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
            >
              {isEditingCategory ? (
                <>
                  <Loader size={18} className="animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save size={18} />
                  Update Category
                </>
              )}
            </motion.button>
          </div>
        </motion.div>
      </Modal>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Behind everything */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs - Behind everything */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container - Normal z-index to not interfere with header */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          {/* Enhanced Error Display */}
          {error && (
            <EnhancedErrorDisplay
              error={error}
              onRetry={retryAll}
              onDismiss={() => setError(null)}
              variant="floating"
              showBackendDetails={true}
              showValidationErrors={true}
            />
          )}

          {/* Success Message Display */}
          {successMessage && (
            <SuccessDisplay
              message={successMessage}
              onDismiss={() => setSuccessMessage(null)}
            />
          )}

          {/* Image Issue Notification */}
          <ImageIssueNotification
            products={products}
            onDismiss={() => {
              // Optional: Store dismissal in localStorage to not show again
              localStorage.setItem('imageIssueNotificationDismissed', 'true');
            }}
          />

          <div className="w-full space-y-10">

            {/* Enhanced Modern Header Section */}
            <GlassCard gradient="from-white/25 to-white/15" className="p-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  {/* Enhanced Store Icon */}
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                    className="relative"
                  >
                    <motion.div
                      className="relative p-6 bg-white/25 border border-white/40 rounded-3xl"
                      whileHover={{ scale: 1.05, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                    >
                      {isLoadingSupplier ? (
                        <div className="w-12 h-12 rounded-xl bg-white/20 animate-pulse" />
                      ) : supplierData?.logoUrl ? (
                        <SafeImage
                          src={supplierData.logoUrl}
                          alt={supplierData.name}
                          className="w-12 h-12 rounded-xl object-cover"
                          fallbackIcon={
                            <Store size={48} className="text-white/60" />
                          }
                        />
                      ) : (
                        <Store size={48} className="text-white/60" />
                      )}

                      {/* Enhanced Glow Effect */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-purple-400/40 to-blue-400/40 rounded-3xl blur-2xl"
                        animate={{ opacity: [0.3, 0.6, 0.3] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </motion.div>
                  </motion.div>

                  {/* Enhanced Welcome Text */}
                  <div>
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                      className="text-white text-4xl font-black mb-3 bg-gradient-to-r from-white via-yellow-200 to-orange-200 bg-clip-text text-transparent"
                    >
                      {isLoadingSupplier ? 'Loading...' : (supplierData?.name || 'My Store')} Products
                      <motion.span
                        animate={{ rotate: [0, 20, -20, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
                        className="inline-block ml-2"
                      >
                        📦
                      </motion.span>
                    </motion.h1>
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex items-center gap-6"
                    >
                      <motion.div
                        className="flex items-center gap-3 bg-white/15 px-4 py-2.5 rounded-2xl border border-white/30 shadow-lg"
                        whileHover={{ scale: 1.05 }}
                      >
                        <Package size={18} className="text-white/90" />
                        <span className="text-white text-base font-semibold">
                          Product Management Dashboard
                        </span>
                      </motion.div>
                      {lastSaved && (
                        <motion.div
                          className="flex items-center gap-3 bg-green-500/15 px-4 py-2.5 rounded-2xl border border-green-400/30 shadow-lg"
                          whileHover={{ scale: 1.05 }}
                        >
                          <Clock size={18} className="text-green-400" />
                          <span className="text-green-300 text-sm font-semibold">
                            Last saved: {lastSaved.toLocaleTimeString()}
                          </span>
                        </motion.div>
                      )}
                    </motion.div>
                  </div>
                </div>

                {/* Enhanced Save Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={{
                    scale: 1.1,
                    rotate: 5,
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.3)"
                  }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleSaveProducts}
                  disabled={isSaving}
                  className="group relative p-6 bg-white/25 border border-white/40 rounded-3xl hover:bg-white/35 transition-all duration-300 shadow-xl disabled:opacity-50"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <motion.div
                    className="relative z-10 flex items-center gap-3"
                    whileHover={{ rotate: isSaving ? 360 : 0 }}
                    transition={{ duration: 0.6 }}
                  >
                    {isSaving ? (
                      <Loader size={28} className="text-white drop-shadow-lg animate-spin" />
                    ) : (
                      <Save size={28} className="text-white drop-shadow-lg" />
                    )}
                                      <span className="text-white font-bold text-lg">
                    {isSaving ? 'Saving...' : 'Save All'}
                  </span>
                </motion.div>
              </motion.button>
              
              {/* Save Status Indicator */}
              {lastSaved && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center gap-2 text-green-400 text-sm"
                >
                  <CheckCircle2 size={16} />
                  <span>Last saved: {new Date(lastSaved).toLocaleTimeString()}</span>
                </motion.div>
              )}
            </div>
            </GlassCard>

            {/* Enhanced Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-10">
              <MetricCard
                icon={Package}
                title="Total Products"
                value={isLoadingSupplier ? '...' : products.length}
                change={isLoadingSupplier ? 'Loading...' : `${searchedProducts.length} visible`}
                changeType="neutral"
                gradient="from-blue-500/25 to-indigo-500/25"
              />
              <MetricCard
                icon={Settings}
                title="Total Options"
                value={isLoadingSupplier ? '...' : getTotalOptionsCount()}
                change={isLoadingSupplier ? 'Loading...' : `${products.filter(p => getProductOptionsCount(p, user?.businessType) > 0).length} configured`}
                changeType="positive"
                gradient="from-emerald-500/25 to-green-500/25"
              />
              <MetricCard
                icon={Tag}
                title="Total Categories"
                value={isLoadingSupplier ? '...' : categories.length}
                change={isLoadingSupplier ? 'Loading...' : (selectedCategory !== null ? `Viewing: ${selectedCategory}` : 'All categories')}
                changeType="neutral"
                gradient="from-purple-500 to-pink-600"
              />
            </div>

            {/* Enhanced Quick Actions Section */}
            <GlassCard gradient="from-white/20 to-white/10" className="p-10 mt-10">
              <div className="flex items-center gap-4 mb-8">
                <motion.div
                  className="p-3 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 10 }}
                >
                  <Zap size={28} className="text-white" />
                </motion.div>
                <h3 className="text-white text-3xl font-black bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent">
                  Quick Actions & View Options
                </h3>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-white/10 rounded-full"
                >
                  <Sparkles size={20} className="text-yellow-300" />
                </motion.div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-5 gap-6">
                <QuickActionButton
                  icon={Plus}
                  label="Add Product"
                  onClick={() => navigate('/supplier/products/add')}
                  gradient="from-emerald-500 to-green-600"
                />
                <QuickActionButton
                  icon={viewMode === 'list' ? Grid3X3 : List}
                  label={viewMode === 'list' ? 'Grid View' : 'List View'}
                  onClick={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                  gradient="from-blue-500 to-indigo-600"
                  isActive={true}
                />
                <QuickActionButton
                  icon={BarChart3}
                  label={showSummary ? 'Hide Summary' : 'Show Summary'}
                  onClick={() => setShowSummary(!showSummary)}
                  gradient="from-purple-500 to-pink-600"
                  isActive={showSummary}
                />
                <QuickActionButton
                  icon={Filter}
                  label="Add Category"
                  onClick={() => setNewCatModal(true)}
                  gradient="from-orange-500 to-red-600"
                />
                <QuickActionButton
                  icon={Settings}
                  label="Bulk Actions"
                  onClick={() => console.log('Bulk actions')}
                  gradient="from-gray-500 to-gray-600"
                />
              </div>
            </GlassCard>

            {/* Search Bar */}
            <GlassCard gradient="from-white/15 to-white/10" className="p-6">
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60" />
                  <input
                    type="text"
                    placeholder="Search products by name or category..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none transition-all"
                  />
                </div>
                {searchQuery && (
                  <motion.button
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setSearchQuery('')}
                    className="p-4 bg-red-500/20 border border-red-400/30 rounded-2xl text-red-300 hover:bg-red-500/30 transition-colors"
                  >
                    <X size={20} />
                  </motion.button>
                )}
              </div>
            </GlassCard>

            {/* Categories Section */}
            <GlassCard gradient="from-white/15 to-white/10" className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <motion.div
                  className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl shadow-lg"
                  whileHover={{ scale: 1.1, rotate: -10 }}
                >
                  <Tag size={28} className="text-white" />
                </motion.div>
                <h3 className="text-white text-2xl font-black bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                  Product Categories
                </h3>
                <div className="flex-1 h-0.5 bg-gradient-to-r from-white/40 via-white/20 to-transparent"></div>
              </div>

              {isLoadingCategories ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-white/20 rounded-full animate-pulse mx-auto mb-4" />
                  <p className="text-white/60 text-sm">Loading categories...</p>
                </div>
              ) : categoriesError ? (
                <div className="text-center py-8">
                  <EnhancedErrorDisplay
                    error={categoriesError}
                    onRetry={retryLoadCategories}
                    variant="floating"
                    showBackendDetails={true}
                    showValidationErrors={true}
                  />
                </div>
              ) : categories.length === 0 ? (
                <div className="text-center py-8">
                  <Tag size={48} className="text-white/30 mx-auto mb-4" />
                  <h4 className="text-white text-lg font-semibold mb-2">No categories yet</h4>
                  <p className="text-white/60 text-sm mb-4">
                    Create your first category to organize your products
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setNewCatModal(true)}
                    className="flex items-center gap-2 px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl border border-white/20 hover:from-purple-700 hover:to-blue-700 transition-all font-semibold mx-auto"
                  >
                    <Plus size={16} />
                    Create First Category
                  </motion.button>
                </div>
              ) : (
                <div className="flex flex-wrap gap-3">
                  {/* All Categories Option */}
                  <CategoryChip
                    key="all"
                    category="All"
                    isSelected={selectedCategory === null || selectedCategory === 'All'}
                    isAll={true}
                    onSelect={() => selectCategory(null)}
                    onEdit={undefined}
                    onDelete={undefined}
                  />
                  
                  {categories.map((cat, i) => (
                    <CategoryChip
                      key={i}
                      category={cat.name}
                      isSelected={selectedCategory === cat.name}
                      isAll={false}
                      onSelect={() => selectCategory(cat.name)}
                      onEdit={() => {
                        setEditCat(cat.name);
                        setEditedValue(cat.name);
                      }}
                      onDelete={() => handleDeleteCategory(cat.name)}
                    />
                  ))}

                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setNewCatModal(true)}
                    className="flex items-center gap-2 px-4 py-3 bg-white/10 border border-white/20 text-white/90 rounded-2xl hover:bg-white/20 transition-all duration-300"
                  >
                    <Plus size={16} />
                    <span className="font-semibold text-sm">Add Category</span>
                  </motion.button>
                </div>
              )}
            </GlassCard>

            {/* Enhanced Options Summary Section - Only show when there are products */}
            {showSummary && products.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0, scale: 0.95 }}
                animate={{ opacity: 1, height: 'auto', scale: 1 }}
                exit={{ opacity: 0, height: 0, scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              >
                <GlassCard gradient="from-white/25 to-white/15" className="p-10 overflow-hidden">
                  {/* Enhanced Header with Floating Elements */}
                  <div className="relative flex items-center gap-6 mb-10">
                    {/* Animated Icon Container */}
                    <motion.div
                      className="relative p-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-3xl shadow-2xl border border-white/30"
                      whileHover={{ scale: 1.15, rotate: 15, y: -5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 15 }}
                    >
                      {/* Glow effect */}
                      <div className="absolute -inset-2 bg-gradient-to-r from-green-400 to-emerald-500 rounded-3xl blur-xl opacity-60" />
                      <BarChart3 size={32} className="relative text-white drop-shadow-lg" />

                      {/* Floating sparkles */}
                      <motion.div
                        className="absolute -top-1 -right-1"
                        animate={{ rotate: [0, 360], scale: [1, 1.2, 1] }}
                        transition={{ duration: 3, repeat: Infinity }}
                      >
                        <Sparkles size={14} className="text-yellow-300" />
                      </motion.div>
                    </motion.div>

                    {/* Enhanced Title */}
                    <div className="flex-1">
                      <motion.h3
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.2 }}
                        className="text-white text-3xl font-black mb-2 bg-gradient-to-r from-white via-green-200 to-emerald-200 bg-clip-text text-transparent"
                      >
                        📊 Products Options Summary
                      </motion.h3>
                      <motion.p
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                        className="text-white/80 text-base font-medium"
                      >
                        Comprehensive overview of all product configurations
                      </motion.p>
                    </div>

                    {/* Floating decoration */}
                    <motion.div
                      className="absolute top-0 right-0 opacity-20"
                      animate={{ rotate: [0, 360], scale: [1, 1.1, 1] }}
                      transition={{ duration: 8, repeat: Infinity }}
                    >
                      <Target size={40} className="text-white" />
                    </motion.div>
                  </div>

                  {products.length === 0 ? (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="text-center py-16 bg-gradient-to-br from-white/10 to-white/5 rounded-3xl border border-white/20"
                    >
                      <motion.div
                        animate={{ y: [0, -10, 0] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Package size={80} className="text-white/40 mx-auto mb-6" />
                      </motion.div>
                      <p className="text-white/70 text-xl font-semibold">No products to summarize yet.</p>
                      <p className="text-white/50 text-sm mt-2">Add some products to see detailed analytics here</p>
                    </motion.div>
                  ) : (
                    <div className="space-y-6">
                      {products.map((product, index) => {
                          const optionsCount = getProductOptionsCount(product, user?.businessType);
  const optionsSummary = getProductOptionsSummary(product, user?.businessType);
                        const hasDiscount = product.discountPrice && product.discountPrice > 0;

                        return (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, x: -30, rotateY: -15 }}
                            animate={{ opacity: 1, x: 0, rotateY: 0 }}
                            whileHover={{
                              scale: 1.02,
                              y: -5,
                              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.2)"
                            }}
                            transition={{
                              delay: index * 0.1,
                              type: "spring",
                              stiffness: 300,
                              damping: 25
                            }}
                            className="group relative bg-gradient-to-br from-white/15 via-white/10 to-white/5 border border-white/30 rounded-3xl p-8 shadow-xl overflow-hidden"
                          >
                            {/* Enhanced shimmer effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

                            {/* Glow effect */}
                            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                            <div className="relative z-10 flex items-center gap-8">
                              {/* Enhanced Product Image */}
                              <motion.div
                                className="relative"
                                whileHover={{ scale: 1.1, rotate: 5 }}
                                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                              >
                                {/* Image glow */}
                                <div className={`absolute -inset-3 rounded-3xl blur-lg opacity-60 ${
                                  optionsCount > 0 ? 'bg-gradient-to-r from-green-400 to-emerald-500' : 'bg-gradient-to-r from-orange-400 to-red-500'
                                }`} />

                                <SafeImage
                                  src={product.image}
                                  alt={product.name}
                                  className={`relative w-20 h-20 rounded-3xl object-cover border-3 shadow-2xl ${
                                    optionsCount > 0 ? 'border-green-400/60' : 'border-orange-400/60'
                                  }`}
                                  fallbackIcon={
                                    <div className="flex flex-col items-center justify-center text-white/60">
                                      <ImageIcon size={16} />
                                      <span className="text-xs mt-1">No Image</span>
                                    </div>
                                  }
                                />

                                {/* Enhanced status badge */}
                                {optionsCount > 0 ? (
                                  <motion.div
                                    initial={{ scale: 0, rotate: -180 }}
                                    animate={{ scale: 1, rotate: 0 }}
                                    whileHover={{ scale: 1.2, rotate: 10 }}
                                    className="absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs font-black px-2 py-1 rounded-xl border-2 border-white shadow-xl"
                                  >
                                    <div className="flex items-center gap-1">
                                      <Crown size={10} />
                                      {optionsCount}
                                    </div>
                                  </motion.div>
                                ) : (
                                  <motion.div
                                    animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0.8, 0.5] }}
                                    transition={{ duration: 2, repeat: Infinity }}
                                    className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-black px-2 py-1 rounded-xl border-2 border-white shadow-xl"
                                  >
                                    <AlertCircle size={10} />
                                  </motion.div>
                                )}
                              </motion.div>

                              {/* Enhanced Product Info */}
                              <div className="flex-1">
                                <div className="flex items-start justify-between mb-4">
                                  <div>
                                    <motion.h4
                                      className="text-white text-xl font-black mb-2 bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                                      whileHover={{ scale: 1.05 }}
                                    >
                                      {product.name}
                                    </motion.h4>

                                    <div className="flex items-center gap-4 mb-3">
                                      <motion.div
                                        className="flex items-center gap-2"
                                        whileHover={{ x: 3 }}
                                      >
                                        <div className="p-1.5 bg-white/20 rounded-lg border border-white/30">
                                          <Tag size={12} className="text-white/90" />
                                        </div>
                                        <span className="text-white/90 text-sm font-semibold bg-white/10 px-3 py-1 rounded-xl border border-white/20">
                                          {product.categoryName}
                                        </span>
                                      </motion.div>

                                      {/* Price display */}
                                      <motion.div
                                        className="flex items-center gap-2"
                                        whileHover={{ scale: 1.05 }}
                                      >
                                        <div className="p-1.5 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-lg border border-green-400/30">
                                          <DollarSign size={12} className="text-green-400" />
                                        </div>
                                        <span className={`text-lg font-black ${hasDiscount ? 'text-red-400' : 'text-green-400'}`}>
                                          ₪{product.price}
                                        </span>
                                        {hasDiscount ? (
                                          <span className="text-white/60 line-through text-sm">
                                            ₪{product.discountPrice}
                                          </span>
                                        ) : null}
                                      </motion.div>
                                    </div>
                                  </div>
                                </div>

                                {/* Enhanced Options Status */}
                                {optionsCount > 0 ? (
                                  <motion.div
                                    className="mb-4 p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-2xl border border-green-400/30"
                                    whileHover={{ scale: 1.02 }}
                                  >
                                    <div className="flex items-center gap-2 mb-2">
                                      <CheckCircle2 size={16} className="text-green-400" />
                                      <p className="text-green-400 text-sm font-bold">
                                        {optionsCount} options configured
                                      </p>
                                      <motion.div
                                        animate={{ rotate: [0, 360] }}
                                        transition={{ duration: 3, repeat: Infinity }}
                                      >
                                        <Award size={14} className="text-green-300" />
                                      </motion.div>
                                    </div>
                                    <p className="text-white/80 text-sm leading-relaxed">
                                      {optionsSummary}
                                    </p>
                                  </motion.div>
                                ) : (
                                  <motion.div
                                    className="mb-4 p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-2xl border border-orange-400/30"
                                    whileHover={{ scale: 1.02 }}
                                    animate={{
                                      boxShadow: ["0 0 0 0 rgba(251, 146, 60, 0.4)", "0 0 0 8px rgba(251, 146, 60, 0)", "0 0 0 0 rgba(251, 146, 60, 0)"]
                                    }}
                                    transition={{ duration: 2, repeat: Infinity }}
                                  >
                                    <div className="flex items-center gap-2">
                                      <AlertCircle size={16} className="text-orange-400" />
                                      <p className="text-orange-400 text-sm font-bold italic">
                                        ⚠️ No options configured yet - Setup required
                                      </p>
                                      <motion.div
                                        animate={{ scale: [1, 1.2, 1] }}
                                        transition={{ duration: 1.5, repeat: Infinity }}
                                      >
                                        <Zap size={14} className="text-orange-300" />
                                      </motion.div>
                                    </div>
                                  </motion.div>
                                )}
                              </div>

                              {/* Enhanced Action Button */}
                              <motion.button
                                whileHover={{
                                  scale: 1.08,
                                  y: -3,
                                  boxShadow: "0 15px 30px -5px rgba(139, 92, 246, 0.4)"
                                }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => navigate(`/supplier/products/${product.id}/manage-options`)}
                                className="group relative flex items-center gap-3 px-6 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl border border-white/30 hover:from-purple-700 hover:to-blue-700 transition-all font-bold text-sm shadow-xl"
                              >
                                {/* Button glow */}
                                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                                <motion.div
                                  whileHover={{ rotate: 360 }}
                                  transition={{ duration: 0.5 }}
                                  className="relative z-10"
                                >
                                  <Settings size={18} />
                                </motion.div>
                                <span className="relative z-10">Manage Options</span>
                                <ChevronRight size={16} className="relative z-10 group-hover:translate-x-1 transition-transform" />
                              </motion.button>
                            </div>
                          </motion.div>
                        );
                      })}

                      {/* Enhanced Summary Statistics */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5 }}
                        className="border-t border-white/30 pt-8 mt-8"
                      >
                        <motion.div
                          className="relative bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-600 rounded-3xl p-8 shadow-2xl border border-white/30 overflow-hidden"
                          whileHover={{ scale: 1.02 }}
                          transition={{ type: "spring", stiffness: 300, damping: 25 }}
                        >
                          {/* Background effects */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-[shimmer_3s_infinite]" />
                          <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full blur-3xl" />
                          <div className="absolute bottom-0 left-0 w-24 h-24 bg-purple-300/10 rounded-full blur-2xl" />

                          {/* Header */}
                          <div className="relative z-10 flex items-center justify-center gap-3 mb-8">
                            <motion.div
                              animate={{ rotate: [0, 360] }}
                              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                            >
                              <BarChart3 size={24} className="text-white" />
                            </motion.div>
                            <h4 className="text-white text-2xl font-black bg-gradient-to-r from-white to-yellow-200 bg-clip-text text-transparent">
                              📊 Summary Statistics
                            </h4>
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 2, repeat: Infinity }}
                            >
                              <TrendingUp size={24} className="text-green-300" />
                            </motion.div>
                          </div>

                          {/* Enhanced Statistics Grid */}
                          <div className="relative z-10 grid grid-cols-1 md:grid-cols-3 gap-6">
                            {/* With Options Card */}
                            <motion.div
                              className="group relative bg-white/15 backdrop-blur-sm border border-white/30 rounded-2xl p-6 text-center overflow-hidden"
                              whileHover={{ scale: 1.05, y: -5 }}
                              transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                              <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="relative z-10">
                                <motion.div
                                  className="flex items-center justify-center gap-2 mb-3"
                                  whileHover={{ scale: 1.1 }}
                                >
                                  <CheckCircle2 size={20} className="text-green-400" />
                                  <p className="text-white/90 text-sm font-bold">With Options</p>
                                </motion.div>
                                <motion.p
                                  className="text-white text-4xl font-black mb-2 bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent"
                                  whileHover={{ scale: 1.1 }}
                                >
                                  {products.filter(p => getProductOptionsCount(p, user?.businessType) > 0).length}
                                </motion.p>
                                <p className="text-green-300 text-xs font-semibold">Products Ready</p>
                              </div>
                            </motion.div>

                            {/* Need Setup Card */}
                            <motion.div
                              className="group relative bg-white/15 backdrop-blur-sm border border-white/30 rounded-2xl p-6 text-center overflow-hidden"
                              whileHover={{ scale: 1.05, y: -5 }}
                              transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-red-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="relative z-10">
                                <motion.div
                                  className="flex items-center justify-center gap-2 mb-3"
                                  whileHover={{ scale: 1.1 }}
                                >
                                  <AlertCircle size={20} className="text-orange-400" />
                                  <p className="text-white/90 text-sm font-bold">Need Setup</p>
                                </motion.div>
                                <motion.p
                                  className="text-white text-4xl font-black mb-2 bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent"
                                  whileHover={{ scale: 1.1 }}
                                  animate={products.filter(p => getProductOptionsCount(p, user?.businessType) === 0).length > 0 ? {
                                    scale: [1, 1.1, 1]
                                  } : {}}
                                  transition={{ duration: 2, repeat: Infinity }}
                                >
                                  {products.filter(p => getProductOptionsCount(p, user?.businessType) === 0).length}
                                </motion.p>
                                <p className="text-orange-300 text-xs font-semibold">Require Attention</p>
                              </div>
                            </motion.div>

                            {/* Completion Card */}
                            <motion.div
                              className="group relative bg-white/15 backdrop-blur-sm border border-white/30 rounded-2xl p-6 text-center overflow-hidden"
                              whileHover={{ scale: 1.05, y: -5 }}
                              transition={{ type: "spring", stiffness: 300, damping: 20 }}
                            >
                              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="relative z-10">
                                <motion.div
                                  className="flex items-center justify-center gap-2 mb-3"
                                  whileHover={{ scale: 1.1 }}
                                >
                                  <Target size={20} className="text-blue-400" />
                                  <p className="text-white/90 text-sm font-bold">Completion</p>
                                </motion.div>
                                <motion.p
                                  className="text-white text-4xl font-black mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
                                  whileHover={{ scale: 1.1 }}
                                >
                                  {Math.round((products.filter(p => getProductOptionsCount(p, user?.businessType) > 0).length / products.length) * 100) || 0}%
                                </motion.p>
                                <p className="text-blue-300 text-xs font-semibold">Overall Progress</p>
                              </div>
                            </motion.div>
                          </div>

                          {/* Progress Bar */}
                          <motion.div
                            initial={{ opacity: 0, scaleX: 0 }}
                            animate={{ opacity: 1, scaleX: 1 }}
                            transition={{ delay: 0.8, duration: 0.8 }}
                            className="relative z-10 mt-8"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-white/80 text-sm font-semibold">Setup Progress</span>
                              <span className="text-white text-sm font-bold">
                                {products.filter(p => getProductOptionsCount(p, user?.businessType) > 0).length} / {products.length}
                              </span>
                            </div>
                            <div className="relative h-3 bg-white/20 rounded-full overflow-hidden">
                              <motion.div
                                initial={{ width: 0 }}
                                animate={{
                                  width: `${Math.round((products.filter(p => getProductOptionsCount(p, user?.businessType) > 0).length / products.length) * 100) || 0}%`
                                }}
                                transition={{ delay: 1, duration: 1.5, ease: "easeOut" }}
                                className="h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full relative overflow-hidden"
                              >
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-[shimmer_2s_infinite]" />
                              </motion.div>
                            </div>
                          </motion.div>
                        </motion.div>
                      </motion.div>
                    </div>
                  )}
                </GlassCard>
              </motion.div>
            )}

            {/* Products Section */}
            <GlassCard gradient="from-white/15 to-white/10" className="p-8">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-4">
                  <motion.div
                    className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg"
                    whileHover={{ scale: 1.1, rotate: -10 }}
                  >
                    <ShoppingBag size={28} className="text-white" />
                  </motion.div>
                  <div>
                    <h3 className="text-white text-2xl font-black bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                      {isLoadingSupplier ? 'Loading Products...' : (selectedCategory === null ? 'All Products' : `${selectedCategory} Products`)}
                    </h3>
                    <p className="text-white/80 text-sm">
                      {isLoadingSupplier ? 'Please wait...' : (
                        <>
                          {searchedProducts.length} product{searchedProducts.length !== 1 ? 's' : ''}
                          {searchQuery && ` matching "${searchQuery}"`}
                        </>
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setViewMode('list')}
                    className={`p-3 rounded-xl border transition-all ${
                      viewMode === 'list'
                        ? 'bg-purple-600 border-white/30 text-white'
                        : 'bg-white/10 border-white/20 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    <List size={20} />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setViewMode('grid')}
                    className={`p-3 rounded-xl border transition-all ${
                      viewMode === 'grid'
                        ? 'bg-purple-600 border-white/30 text-white'
                        : 'bg-white/10 border-white/20 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    <Grid3X3 size={20} />
                  </motion.button>
                </div>
              </div>

              {isLoadingSupplier || isLoadingProducts ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-16"
                >
                  <div className="w-20 h-20 bg-white/20 rounded-full animate-pulse mx-auto mb-6" />
                  <h4 className="text-white text-xl font-bold mb-2">
                    {isLoadingSupplier ? 'Loading supplier data...' : 'Loading products...'}
                  </h4>
                  <p className="text-white/60 mb-6">
                    {isLoadingSupplier ? 'Please wait while we fetch your supplier information' : 'Please wait while we fetch your products'}
                  </p>
                  <div className="flex items-center justify-center gap-2 text-white/40">
                    <Loader size={16} className="animate-spin" />
                    <span className="text-sm">Please wait...</span>
                  </div>
                </motion.div>
              ) : productsError ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-16"
                >
                  <EnhancedErrorDisplay
                    error={productsError}
                    onRetry={retryLoadProducts}
                    variant="floating"
                    showBackendDetails={true}
                    showValidationErrors={true}
                  />
                </motion.div>
              ) : products.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-16"
                >
                  <Package size={80} className="text-white/30 mx-auto mb-6" />
                  <h4 className="text-white text-xl font-bold mb-2">No products yet</h4>
                  <p className="text-white/60 mb-6">
                    Start by adding your first product to get started.
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate('/supplier/products/add')}
                    className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl border border-white/20 hover:from-purple-700 hover:to-blue-700 transition-all font-semibold mx-auto"
                  >
                    <Plus size={20} />
                    Add Your First Product
                  </motion.button>
                </motion.div>
              ) : searchedProducts.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-16"
                >
                  <Package size={80} className="text-white/30 mx-auto mb-6" />
                  <h4 className="text-white text-xl font-bold mb-2">No products found</h4>
                  <p className="text-white/60 mb-6">
                    {searchQuery ? `No products match "${searchQuery}". Try a different search term or clear your search.` : 
                     selectedCategory ? `No products found in the "${selectedCategory}" category.` : 
                     'No products available. Please add some products to get started.'}
                  </p>
                  <div className="flex gap-3 justify-center">
                    {searchQuery && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setSearchQuery('')}
                        className="flex items-center gap-2 px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all font-semibold"
                      >
                        <X size={20} />
                        Clear Search
                      </motion.button>
                    )}
                    {selectedCategory && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => selectCategory(null)}
                        className="flex items-center gap-2 px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all font-semibold"
                      >
                        <Package size={20} />
                        View All Products
                      </motion.button>
                    )}
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => navigate('/supplier/products/add')}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl border border-white/20 hover:from-purple-700 hover:to-blue-700 transition-all font-semibold"
                    >
                      <Plus size={20} />
                      Add Product
                    </motion.button>
                  </div>
                </motion.div>
              ) : viewMode === 'list' ? (
                // List View
                <div className="space-y-6">
                  {searchedProducts.map((product, i) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <ProductCard product={product} businessType={user?.businessType} />
                    </motion.div>
                  ))}
                </div>
              ) : (
                // Grid View
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {searchedProducts.map((product, i) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <ProductCardGrid product={product} businessType={user?.businessType} />
                    </motion.div>
                  ))}
                </div>
              )}
            </GlassCard>
          </div>
        </div>

        {/* Floating Action Button */}
        <motion.button
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          whileHover={{
            scale: 1.1,
            rotate: 10,
            boxShadow: "0 20px 40px -12px rgba(139, 92, 246, 0.6)"
          }}
          whileTap={{ scale: 0.9 }}
          onClick={() => navigate('/supplier/products/add')}
          className="fixed bottom-8 right-8 p-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full shadow-2xl border border-white/30 z-20"
          style={{ zIndex: 20 }}
        >
          <Plus size={32} className="drop-shadow-lg" />
        </motion.button>
      </div>
    </>
  );
};

export default SupplierProductsPage;