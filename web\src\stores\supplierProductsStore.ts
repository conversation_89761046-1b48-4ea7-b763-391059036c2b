import { create } from 'zustand';
import { 
  addSupplierProduct, 
  updateSupplierProduct, 
  deleteSupplierProduct, 
  toggleSupplierProductAvailability,
  getSupplierProducts,
  type Product
} from '../services/api';

type Addition = {
  id: string;
  name: string;
  price: number;
};

type SupplierProductsStore = {
  products: Product[];
  supplierId: string | null;
  isLoading: boolean;
  error: string | null;
  setProducts: (products: Product[]) => void;
  setSupplierId: (supplierId: string) => void;
  loadProducts: () => Promise<void>;
  addProduct: (product: Omit<Product, 'id'>) => Promise<void>;
  updateProduct: (id: string, updated: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  toggleProductAvailability: (id: string) => Promise<void>;
  saveProducts: () => Promise<void>;
  lastSaved: Date | null;
  isSaving: boolean;
  updateProductsCategoryName: (oldCategoryName: string, newCategoryName: string) => void;
  getProduct: (id: string) => Product | undefined;
  loadProductsFromStorage: () => void;
};

export const useSupplierProductsStore = create<SupplierProductsStore>((set, get) => ({
  products: [],
  supplierId: null,
  isLoading: false,
  error: null,
  lastSaved: null,
  isSaving: false,

  setProducts: (products) => set({ products }),

  setSupplierId: (supplierId) => set({ supplierId }),

  loadProducts: async () => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isLoading: true, error: null });
    try {
      const response = await getSupplierProducts(supplierId);
      if (response.success && response.data) {
        set({ 
          products: response.data.products || [],
          isLoading: false,
          error: null
        });
      } else {
        throw new Error(response.message || 'Failed to load products');
      }
    } catch (error) {
      console.error('Error loading products:', error);
      set({
        error: 'Failed to load products',
        isLoading: false
      });
    }
  },

  addProduct: async (productData) => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      const response = await addSupplierProduct(supplierId, productData);
      if (response.success && response.data) {
        const newProduct = response.data;
        set((state) => ({
          products: [...state.products, newProduct],
          isSaving: false,
          error: null
        }));
      } else {
        throw new Error(response.message || 'Failed to add product');
      }
    } catch (error) {
      console.error('Error adding product:', error);
      set({
        error: 'Failed to add product',
        isSaving: false
      });
      throw error;
    }
  },

  updateProduct: async (id, updated) => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      const response = await updateSupplierProduct(supplierId, id, updated);
      if (response.success && response.data) {
        const updatedProduct = response.data;
        const newState = {
          products: get().products.map((p) =>
            p.id === id ? updatedProduct : p
          ),
          isSaving: false,
          error: null
        };

        set(newState);

        // Save to localStorage for persistence
        try {
          localStorage.setItem('supplier-products', JSON.stringify({
            products: newState.products,
            lastSaved: new Date().toISOString()
          }));
        } catch (error) {
          console.error('Failed to save to localStorage:', error);
        }
      } else {
        throw new Error(response.message || 'Failed to update product');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      set({
        error: 'Failed to update product',
        isSaving: false
      });
      // Re-throw the error so the component can handle it
      throw error;
    }
  },

  deleteProduct: async (id) => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      const response = await deleteSupplierProduct(supplierId, id);
      if (response.success) {
        set((state) => ({
          products: state.products.filter((p) => p.id !== id),
          isSaving: false,
          error: null
        }));
      } else {
        throw new Error(response.message || 'Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      set({
        error: 'Failed to delete product',
        isSaving: false
      });
    }
  },

  toggleProductAvailability: async (id) => {
    const { supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      const response = await toggleSupplierProductAvailability(supplierId, id);
      if (response.success && response.data) {
        const updatedProduct = response.data;
        set((state) => ({
          products: state.products.map((p) => 
            p.id === id ? updatedProduct : p
          ),
          isSaving: false,
          error: null
        }));
      } else {
        throw new Error(response.message || 'Failed to toggle product availability');
      }
    } catch (error) {
      console.error('Error toggling product availability:', error);
      set({
        error: 'Failed to toggle product availability',
        isSaving: false
      });
    }
  },

  saveProducts: async () => {
    const { products, supplierId } = get();
    if (!supplierId) {
      set({ error: 'No supplier ID set' });
      return;
    }

    set({ isSaving: true, error: null });
    try {
      // Save all products to backend
      const savePromises = products.map(async (product) => {
        try {
          // If product has an ID, update it; otherwise, add it
          if (product.id) {
            await updateSupplierProduct(supplierId, product.id, product);
          } else {
            await addSupplierProduct(supplierId, product);
          }
        } catch (error) {
          console.error(`Failed to save product ${product.name}:`, error);
          throw error;
        }
      });

      // Wait for all products to be saved
      await Promise.all(savePromises);

      // Reload products from backend to ensure consistency
      await get().loadProducts();

      set({
        lastSaved: new Date(),
        isSaving: false,
        error: null
      });

      return Promise.resolve();

    } catch (error) {
      console.error('Error saving products:', error);
      set({ 
        isSaving: false,
        error: 'Failed to save products to backend'
      });
      throw error;
    }
  },

  updateProductsCategoryName: (oldCategoryName, newCategoryName) => {
    set((state) => ({
      products: state.products.map((product) =>
        product.categoryName === oldCategoryName ? { ...product, categoryName: newCategoryName } : product
      )
    }));
  },

  getProduct: (id) => {
    const { products } = get();
    return products.find((product) => product.id === id);
  },

  loadProductsFromStorage: () => {
    try {
      const stored = localStorage.getItem('supplier-products');
      if (stored) {
        const parsedData = JSON.parse(stored);
        if (parsedData.products) {
          set({ products: parsedData.products });
        }
      }
    } catch (error) {
      console.error('Failed to load products from localStorage:', error);
    }
  }
}));
export type { Product, Addition };

