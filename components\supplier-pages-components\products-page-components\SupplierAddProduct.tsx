import { Ionicons } from "@expo/vector-icons"
import { Dimensions, Image, Alert, Pressable } from "react-native"
import {
  Button,
  Card,
  Input,
  Label,
  ScrollView,
  Text,
  YStack,
  XStack,
  Select,
  View,
  H2,
  H4,
  Separator,
  Progress,
  Spinner
} from "tamagui"
import * as ImagePicker from "expo-image-picker"
import { useRouter } from "expo-router"
import { useSupplierProducts } from "./useSupplierProducts"
import uuid from 'react-native-uuid';
import { useSupplierCategories } from "./useSupplierCategories"
import { useEffect, useState, useCallback } from "react";
import { AnimatePresence, MotiView } from "moti"
import { LinearGradient } from "expo-linear-gradient"

export default function AddProductScreen() {
  const { addProduct } = useSupplierProducts();
  const router = useRouter();
  const width = Dimensions.get("window").width;

  // Enhanced state management - Remove ID generation (backend will handle it)
  const [product, setProduct] = useState({
    name: "",
    price: 0,
    discountPrice: 0,
    image: "",
    category: "",
    description: "",
    isAvailable: true,
    isPromotion: false,
    restaurantOptions: {
      additions: [],
      without: [],
      sides: [],
    },
    clothingOptions: {
      sizes: [],
      colors: [],
      gallery: [],
    },
    customOptions: [],
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);

  // Form validation
  const validateField = useCallback((field: string, value: any) => {
    const newErrors = { ...errors };

    switch (field) {
      case 'name':
        if (!value || value.trim().length < 2) {
          newErrors.name = 'Product name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          newErrors.name = 'Product name must be less than 50 characters';
        } else {
          delete newErrors.name;
        }
        break;
      case 'price':
        if (!value || isNaN(value) || value <= 0) {
          newErrors.price = 'Please enter a valid price';
        } else if (value > 10000) {
          newErrors.price = 'Price seems too high (max: 10,000)';
        } else {
          delete newErrors.price;
        }
        break;
      case 'category':
        if (!value) {
          newErrors.category = 'Please select a category';
        } else {
          delete newErrors.category;
        }
        break;
      case 'image':
        if (!value) {
          newErrors.image = 'Please upload a product image';
        } else {
          delete newErrors.image;
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [errors]);

  const handleImagePick = async () => {
    setImageLoading(true);
    try {
      const res = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!res.canceled && res.assets[0]?.uri) {
        // Convert local file URI to base64 data URL for cross-platform compatibility
        const response = await fetch(res.assets[0].uri);
        const blob = await response.blob();
        const reader = new FileReader();

        reader.onload = () => {
          const base64DataUrl = reader.result as string;
          setProduct({ ...product, image: base64DataUrl });
          validateField('image', base64DataUrl);
          setImageLoading(false);
        };

        reader.onerror = () => {
          Alert.alert('Error', 'Failed to process image. Please try again.');
          setImageLoading(false);
        };

        reader.readAsDataURL(blob);
        return; // Don't set loading to false here, let the reader callbacks handle it
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    } finally {
      setImageLoading(false);
    }
  };

  const handleCameraPick = async () => {
    setImageLoading(true);
    try {
      const res = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!res.canceled && res.assets[0]?.uri) {
        // Convert local file URI to base64 data URL for cross-platform compatibility
        const response = await fetch(res.assets[0].uri);
        const blob = await response.blob();
        const reader = new FileReader();

        reader.onload = () => {
          const base64DataUrl = reader.result as string;
          setProduct({ ...product, image: base64DataUrl });
          validateField('image', base64DataUrl);
          setImageLoading(false);
        };

        reader.onerror = () => {
          Alert.alert('Error', 'Failed to process image. Please try again.');
          setImageLoading(false);
        };

        reader.readAsDataURL(blob);
        return; // Don't set loading to false here, let the reader callbacks handle it
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setImageLoading(false);
    }
  };

  const validateAndContinue = async () => {
    // Validate all fields
    const nameValid = validateField('name', product.name);
    const priceValid = validateField('price', product.price);
    const categoryValid = validateField('category', product.category);
    const imageValid = validateField('image', product.image);

    if (!nameValid || !priceValid || !categoryValid || !imageValid) {
      Alert.alert('Validation Error', 'Please fix the errors before continuing.');
      return;
    }

    setIsLoading(true);

    try {
      console.log('📦 Adding product to backend:', product);

      // Add product using backend API (this will generate the ID)
      await addProduct(product);

      // Show success message
      Alert.alert(
        'Success!',
        'Product created successfully and is now available to customers!',
        [
          {
            text: 'View Products',
            onPress: () => {
              router.push("/(supplier-pages)/products");
            }
          },
          {
            text: 'Add Another',
            onPress: () => {
              // Reset form for adding another product
              setProduct({
                name: "",
                price: 0,
                discountPrice: 0,
                image: "",
                category: "",
                description: "",
                isAvailable: true,
                restaurantOptions: {
                  additions: [],
                  without: [],
                  sides: [],
                },
                clothingOptions: {
                  sizes: [],
                  colors: [],
                  gallery: [],
                },
                customOptions: [],
              });
              setErrors({});
            }
          }
        ]
      );
    } catch (error) {
      console.error('❌ Error adding product:', error);
      Alert.alert('Error', 'Failed to create product. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate form completion percentage
  const getCompletionPercentage = () => {
    let completed = 0;
    const total = 4; // name, price, category, image

    if (product.name.trim()) completed++;
    if (product.price > 0) completed++;
    if (product.category) completed++;
    if (product.image) completed++;

    return Math.round((completed / total) * 100);
  };

  const { categories } = useSupplierCategories();

  const filteredCategories = categories.filter((cat) => cat !== "All");

  // Set default category to first available (excluding 'All')
  useEffect(() => {
    if (!product.category && filteredCategories.length > 0) {
      setProduct((prev) => ({ ...prev, category: filteredCategories[0] }));
    }
    // eslint-disable-next-line
  }, [filteredCategories.length]);

  const [selectOpen, setSelectOpen] = useState(false);

  return (
    <>
      <ScrollView
        padding={16}
        contentContainerStyle={{paddingBottom: 120}}
        width={width}
        showsVerticalScrollIndicator={false}
      >
        {/* Enhanced Header with Progress */}
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ duration: 600 }}
        >
          <LinearGradient
            colors={["#7529B3", "#8F3DD2"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={{
              borderRadius: 24,
              padding: 24,
              marginBottom: 24,
              shadowColor: "#7529B3",
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.3,
              shadowRadius: 16,
            }}
          >
            <YStack gap="$4">
              {/* Header Content */}
              <XStack ai="center" gap="$3">
                <View
                  style={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    borderRadius: 16,
                    padding: 12,
                  }}
                >
                  <Ionicons name="add-circle-outline" size={32} color="white" />
                </View>
                <YStack flex={1}>
                  <H2 color="white" fontWeight="800">
                    Create New Product
                  </H2>
                  <Text color="white" opacity={0.9} fontSize="$4">
                    Build your product step by step
                  </Text>
                </YStack>
              </XStack>

              {/* Progress Section */}
              <YStack gap="$3">
                <XStack ai="center" jc="space-between">
                  <Text color="white" fontSize="$3" opacity={0.8}>
                    Form Progress
                  </Text>
                  <Text color="white" fontSize="$3" fontWeight="600">
                    {getCompletionPercentage()}% Complete
                  </Text>
                </XStack>

                <Progress
                  value={getCompletionPercentage()}
                  bg="rgba(255,255,255,0.2)"
                  height={8}
                  br="$4"
                >
                  <Progress.Indicator
                    bg="white"
                    br="$4"
                    animation="bouncy"
                  />
                </Progress>

                {/* Step Indicators */}
                <XStack jc="space-between" mt="$2">
                  {[
                    { icon: 'pricetag-outline', label: 'Name', completed: !!product.name.trim() },
                    { icon: 'cash-outline', label: 'Price', completed: product.price > 0 },
                    { icon: 'list-outline', label: 'Category', completed: !!product.category },
                    { icon: 'image-outline', label: 'Image', completed: !!product.image },
                  ].map((step, index) => (
                    <YStack key={index} ai="center" gap="$1">
                      <View
                        style={{
                          backgroundColor: step.completed ? 'white' : 'rgba(255,255,255,0.3)',
                          borderRadius: 20,
                          padding: 8,
                        }}
                      >
                        <Ionicons
                          name={step.icon as any}
                          size={16}
                          color={step.completed ? '#7529B3' : 'white'}
                        />
                      </View>
                      <Text
                        color="white"
                        fontSize="$1"
                        opacity={step.completed ? 1 : 0.7}
                        fontWeight={step.completed ? "600" : "normal"}
                      >
                        {step.label}
                      </Text>
                    </YStack>
                  ))}
                </XStack>
              </YStack>
            </YStack>
          </LinearGradient>
        </MotiView>

        {/* Enhanced Form Card */}
        <MotiView
          from={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 300, duration: 500 }}
        >
          <Card
            elevate
            p="$6"
            br="$10"
            bg="white"
            borderWidth={2}
            borderColor="$gray4"
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 8 }}
            shadowOpacity={0.1}
            shadowRadius={16}
          >
            <YStack gap="$5">
              {/* Product Name Section */}
              <YStack gap="$3">
                <XStack ai="center" gap="$2">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 8,
                      padding: 6,
                    }}
                  >
                    <Ionicons name="pricetag-outline" size={18} color="#7529B3" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">Product Name</H4>
                  <Text color="$red10" fontSize="$3">*</Text>
                </XStack>

                <Input
                  size="$4"
                  placeholder="e.g., Delicious Chicken Burger"
                  value={product.name}
                  onChangeText={(text) => {
                    setProduct({ ...product, name: text });
                    validateField('name', text);
                  }}
                  bg="$gray1"
                  borderColor={errors.name ? "$red8" : "$gray6"}
                  focusStyle={{ borderColor: "$primary" }}
                  br="$6"
                  fontSize="$4"
                />

                {errors.name && (
                  <XStack ai="center" gap="$2">
                    <Ionicons name="warning-outline" size={14} color="#ef4444" />
                    <Text color="$red10" fontSize="$3">{errors.name}</Text>
                  </XStack>
                )}

                <Text color="$gray9" fontSize="$2">
                  Choose a descriptive name that customers will love
                </Text>
              </YStack>

              {/* Category Section */}
              <YStack gap="$3">
                <XStack ai="center" gap="$2">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 8,
                      padding: 6,
                    }}
                  >
                    <Ionicons name="list-outline" size={18} color="#7529B3" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">Category</H4>
                  <Text color="$red10" fontSize="$3">*</Text>
                </XStack>

                <Select
                  open={selectOpen}
                  onOpenChange={setSelectOpen}
                  value={product.category}
                  onValueChange={(value) => {
                    setProduct({ ...product, category: value });
                    validateField('category', value);
                  }}
                >
                  <Select.Trigger
                    iconAfter={<Ionicons name="chevron-down" size={16} color="#7529B3" />}
                    onPress={() => setSelectOpen((v) => !v)}
                    style={{
                      borderWidth: 2,
                      borderColor: errors.category ? "#ef4444" : "#e5e7eb",
                      borderRadius: 12,
                      paddingVertical: 16,
                      paddingHorizontal: 16,
                      backgroundColor: "#f9fafb",
                    }}
                  >
                    <Text
                      fontSize="$4"
                      color={product.category ? "#7529B3" : "#9ca3af"}
                      fontWeight={product.category ? "600" : "normal"}
                    >
                      {product.category || "Select a category"}
                    </Text>
                  </Select.Trigger>

                  <AnimatePresence>
                    {selectOpen && (
                      <Select.Content zIndex={100}>
                        <MotiView
                          from={{ opacity: 0, translateY: -10, scale: 0.97 }}
                          animate={{ opacity: 1, translateY: 0, scale: 1 }}
                          exit={{ opacity: 0, translateY: -10, scale: 0.97 }}
                          transition={{ type: "timing", duration: 250 }}
                          style={{
                            backgroundColor: "white",
                            borderRadius: 16,
                            paddingVertical: 8,
                            paddingHorizontal: 8,
                            shadowColor: "#000",
                            shadowOpacity: 0.15,
                            shadowRadius: 20,
                            shadowOffset: { width: 0, height: 8 },
                            elevation: 12,
                            borderWidth: 1,
                            borderColor: "#e5e7eb",
                          }}
                        >
                          {filteredCategories.map((cat, idx) => {
                            const selected = product.category === cat;
                            return (
                              <Select.Item
                                key={cat}
                                value={cat}
                                index={idx}
                                style={{
                                  backgroundColor: selected ? "#7529B3" : "transparent",
                                  paddingVertical: 14,
                                  paddingHorizontal: 16,
                                  borderRadius: 10,
                                  marginBottom: 4,
                                }}
                              >
                                <Select.ItemText
                                  style={{
                                    fontSize: 16,
                                    color: selected ? "#fff" : "#374151",
                                    fontWeight: selected ? "600" : "normal",
                                  }}
                                >
                                  {cat}
                                </Select.ItemText>
                                {selected && (
                                  <View style={{ position: "absolute", right: 16 }}>
                                    <Ionicons name="checkmark-circle" size={18} color="#fff" />
                                  </View>
                                )}
                              </Select.Item>
                            );
                          })}
                        </MotiView>
                      </Select.Content>
                    )}
                  </AnimatePresence>
                </Select>

                {errors.category && (
                  <XStack ai="center" gap="$2">
                    <Ionicons name="warning-outline" size={14} color="#ef4444" />
                    <Text color="$red10" fontSize="$3">{errors.category}</Text>
                  </XStack>
                )}

                <Text color="$gray9" fontSize="$2">
                  Choose the category that best fits your product
                </Text>
              </YStack>

              {/* Price Section */}
              <YStack gap="$3">
                <XStack ai="center" gap="$2">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 8,
                      padding: 6,
                    }}
                  >
                    <Ionicons name="cash-outline" size={18} color="#7529B3" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">Price</H4>
                  <Text color="$red10" fontSize="$3">*</Text>
                </XStack>

                <XStack gap="$3">
                  <YStack flex={1}>
                    <Text color="$gray10" fontSize="$3" mb="$2">Regular Price (₪)</Text>
                    <Input
                      size="$4"
                      keyboardType="numeric"
                      placeholder="e.g., 25.00"
                      value={product.price?.toString()}
                      onChangeText={(text) => {
                        const price = parseFloat(text) || 0;
                        setProduct({ ...product, price });
                        validateField('price', price);
                      }}
                      bg="$gray1"
                      borderColor={errors.price ? "$red8" : "$gray6"}
                      focusStyle={{ borderColor: "$primary" }}
                      br="$6"
                      fontSize="$4"
                    />
                  </YStack>

                  <YStack flex={1}>
                    <Text color="$gray10" fontSize="$3" mb="$2">Discount Price (₪)</Text>
                    <Input
                      size="$4"
                      keyboardType="numeric"
                      placeholder="Optional"
                      value={product.discountPrice?.toString()}
                      onChangeText={(text) => {
                        const discountPrice = parseFloat(text) || 0;
                        setProduct({ ...product, discountPrice });
                      }}
                      bg="$gray1"
                      borderColor="$gray6"
                      focusStyle={{ borderColor: "$primary" }}
                      br="$6"
                      fontSize="$4"
                    />
                  </YStack>
                </XStack>

                {errors.price && (
                  <XStack ai="center" gap="$2">
                    <Ionicons name="warning-outline" size={14} color="#ef4444" />
                    <Text color="$red10" fontSize="$3">{errors.price}</Text>
                  </XStack>
                )}

                <Text color="$gray9" fontSize="$2">
                  Set competitive pricing. Discount price is optional for promotions.
                </Text>
              </YStack>

              {/* Enhanced Image Upload Section */}
              <YStack gap="$3">
                <XStack ai="center" gap="$2">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 8,
                      padding: 6,
                    }}
                  >
                    <Ionicons name="image-outline" size={18} color="#7529B3" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">Product Image</H4>
                  <Text color="$red10" fontSize="$3">*</Text>
                </XStack>

                {!product.image ? (
                  <Card
                    p="$4"
                    br="$8"
                    borderWidth={2}
                    borderStyle="dashed"
                    borderColor={errors.image ? "$red8" : "$gray6"}
                    bg="$gray1"
                  >
                    <YStack ai="center" gap="$3">
                      <View
                        style={{
                          backgroundColor: '#7529B3',
                          borderRadius: 20,
                          padding: 16,
                        }}
                      >
                        <Ionicons name="cloud-upload-outline" size={32} color="white" />
                      </View>
                      <YStack ai="center" gap="$1">
                        <H4 color="$gray12">Upload Product Image</H4>
                        <Text color="$gray9" fontSize="$3" textAlign="center">
                          Choose from gallery or take a photo
                        </Text>
                      </YStack>
                      <XStack gap="$2">
                        <Button
                          bg="$primary"
                          color="white"
                          size="$3"
                          br="$6"
                          icon={<Ionicons name="images-outline" size={16} color="white" />}
                          disabled={imageLoading}
                          onPress={handleImagePick}
                          flex={1}
                        >
                          {imageLoading ? 'Loading...' : 'Gallery'}
                        </Button>
                        <Button
                          variant="outlined"
                          borderColor="$primary"
                          color="$primary"
                          size="$3"
                          br="$6"
                          icon={<Ionicons name="camera-outline" size={16} />}
                          disabled={imageLoading}
                          onPress={handleCameraPick}
                          flex={1}
                        >
                          Camera
                        </Button>
                      </XStack>
                    </YStack>
                  </Card>
                ) : (
                  <YStack gap="$3">
                    <Card p="$3" br="$8" bg="$gray1">
                      <Image
                        source={{ uri: product.image }}
                        style={{
                          width: '100%',
                          height: 200,
                          borderRadius: 12,
                        }}
                        resizeMode="cover"
                      />
                    </Card>

                    <XStack gap="$2">
                      <Button
                        flex={1}
                        variant="outlined"
                        borderColor="$primary"
                        color="$primary"
                        size="$3"
                        br="$6"
                        icon={<Ionicons name="images-outline" size={16} />}
                        onPress={handleImagePick}
                        disabled={imageLoading}
                      >
                        Gallery
                      </Button>

                      <Button
                        flex={1}
                        variant="outlined"
                        borderColor="$primary"
                        color="$primary"
                        size="$3"
                        br="$6"
                        icon={<Ionicons name="camera-outline" size={16} />}
                        onPress={handleCameraPick}
                        disabled={imageLoading}
                      >
                        Camera
                      </Button>

                      <Button
                        flex={1}
                        variant="outlined"
                        borderColor="$red8"
                        color="$red10"
                        size="$3"
                        br="$6"
                        icon={<Ionicons name="trash-outline" size={16} />}
                        onPress={() => {
                          setProduct({ ...product, image: '' });
                          validateField('image', '');
                        }}
                      >
                        Remove
                      </Button>
                    </XStack>
                  </YStack>
                )}

                {errors.image && (
                  <XStack ai="center" gap="$2">
                    <Ionicons name="warning-outline" size={14} color="#ef4444" />
                    <Text color="$red10" fontSize="$3">{errors.image}</Text>
                  </XStack>
                )}

                <Text color="$gray9" fontSize="$2">
                  High-quality images help customers make better decisions
                </Text>
              </YStack>

              {/* Optional Description Section */}
              <YStack gap="$3">
                <XStack ai="center" gap="$2">
                  <View
                    style={{
                      backgroundColor: '#f3f4f6',
                      borderRadius: 8,
                      padding: 6,
                    }}
                  >
                    <Ionicons name="document-text-outline" size={18} color="#7529B3" />
                  </View>
                  <H4 color="$gray12" fontWeight="700">Description</H4>
                  <Text color="$gray9" fontSize="$3">(Optional)</Text>
                </XStack>

                <Input
                  size="$4"
                  placeholder="Describe your product (optional)"
                  value={product.description}
                  onChangeText={(text) => setProduct({ ...product, description: text })}
                  bg="$gray1"
                  borderColor="$gray6"
                  focusStyle={{ borderColor: "$primary" }}
                  br="$6"
                  fontSize="$4"
                  multiline
                  numberOfLines={3}
                />

                <Text color="$gray9" fontSize="$2">
                  Add details about ingredients, materials, or special features
                </Text>
              </YStack>
            </YStack>
          </Card>
        </MotiView>

        {/* Enhanced Submit Button */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 600, duration: 500 }}
        >
          <Card
            p="$4"
            br="$10"
            bg="$primary"
            borderWidth={0}
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 8 }}
            shadowOpacity={0.3}
            shadowRadius={16}
            pressStyle={{ scale: 0.98 }}
            onPress={validateAndContinue}
            disabled={isLoading || getCompletionPercentage() < 100}
          >
            <XStack ai="center" jc="center" gap="$3">
              {isLoading ? (
                <Spinner size="small" color="white" />
              ) : (
                <Ionicons name="rocket-outline" size={24} color="white" />
              )}
              <YStack ai="center">
                <Text color="white" fontWeight="800" fontSize="$5">
                  {isLoading ? 'Creating Product...' : 'Create Product & Add Options'}
                </Text>
                {getCompletionPercentage() < 100 ? (
                  <Text color="white" opacity={0.8} fontSize="$3">
                    Complete {100 - getCompletionPercentage()}% more to continue
                  </Text>
                ) : (
                  <Text color="white" opacity={0.8} fontSize="$3">
                    Ready to create your product!
                  </Text>
                )}
              </YStack>
              {!isLoading && (
                <Ionicons name="arrow-forward" size={20} color="white" />
              )}
            </XStack>
          </Card>
        </MotiView>
      </ScrollView>


    </>
  );
}
