import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, AlertTriangle, Info, XCircle, ChevronDown, ChevronUp, X } from 'lucide-react';
import type { ErrorInfo } from '../../utils/errorHandler';

interface EnhancedErrorDisplayProps {
  error: ErrorInfo;
  onRetry?: () => void;
  onDismiss?: () => void;
  showBackendDetails?: boolean;
  showValidationErrors?: boolean;
  variant?: 'inline' | 'card' | 'alert' | 'fullscreen' | 'floating';
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const EnhancedErrorDisplay: React.FC<EnhancedErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  showBackendDetails = true,
  showValidationErrors = true,
  variant = 'card',
  size = 'medium',
  className = ''
}) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!error) return null;

  const hasBackendDetails = showBackendDetails && error.backendMessage && 
                           error.backendMessage !== error.userFriendlyMessage;
  const hasValidationErrors = showValidationErrors && error.validationErrors && 
                             error.validationErrors.length > 0;
  const hasAdditionalDetails = hasBackendDetails || hasValidationErrors;

  const getIcon = () => {
    switch (error.type) {
      case 'validation':
        return <AlertTriangle size={getIconSize()} className="text-orange-500" />;
      case 'auth':
        return <XCircle size={getIconSize()} className="text-red-500" />;
      case 'network':
        return <AlertCircle size={getIconSize()} className="text-blue-500" />;
      case 'server':
        return <XCircle size={getIconSize()} className="text-red-600" />;
      default:
        return <XCircle size={getIconSize()} className="text-red-500" />;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 24;
      default: return 20;
    }
  };

  const getTitleSize = () => {
    switch (size) {
      case 'small': return 'text-sm';
      case 'large': return 'text-xl';
      default: return 'text-base';
    }
  };

  const getMessageSize = () => {
    switch (size) {
      case 'small': return 'text-xs';
      case 'large': return 'text-lg';
      default: return 'text-sm';
    }
  };

  const renderInlineError = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`inline-flex items-center gap-2 flex-wrap ${className}`}
    >
      {getIcon()}
      <span className={`text-red-600 flex-1 ${getMessageSize()}`}>
        {error.userFriendlyMessage}
      </span>
      {hasAdditionalDetails && (
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-blue-600 hover:text-blue-800 text-xs font-medium flex items-center gap-1"
        >
          {showDetails ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
          Details
        </button>
      )}
    </motion.div>
  );

  const renderCardError = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-start gap-3">
        {getIcon()}
        <div className="flex-1 min-w-0">
          <h3 className={`font-semibold text-red-800 mb-1 ${getTitleSize()}`}>
            {getErrorTitle(error.type)}
          </h3>
          <p className={`text-red-700 mb-3 ${getMessageSize()}`}>
            {error.userFriendlyMessage}
          </p>

          {hasAdditionalDetails && (
            <>
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-blue-600 hover:text-blue-800 text-xs font-medium flex items-center gap-1 mb-3"
              >
                {showDetails ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
                {showDetails ? 'Hide Details' : 'Show Details'}
              </button>

              {showDetails && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-red-100 rounded-md p-3 space-y-3"
                >
                  {hasBackendDetails && (
                    <div>
                      <h4 className="text-xs font-semibold text-red-800 mb-1 flex items-center gap-1">
                        🔍 Technical Details
                      </h4>
                      <p className="text-xs text-red-700 font-mono bg-red-50 p-2 rounded border">
                        {error.backendMessage}
                      </p>
                    </div>
                  )}

                  {hasValidationErrors && (
                    <div>
                      <h4 className="text-xs font-semibold text-red-800 mb-2 flex items-center gap-1">
                        ⚠️ Please fix these issues
                      </h4>
                      <div className="space-y-1">
                        {error.validationErrors!.map((err, index) => (
                          <div key={index} className="flex items-start gap-2 text-xs">
                            <span className="text-red-600 mt-0.5">•</span>
                            <div className="flex-1">
                              <span className="font-medium text-red-700">{err.field}:</span>
                              <span className="text-red-600 ml-1">{err.message}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </>
          )}

          {(onRetry || onDismiss) && (
            <div className="flex gap-2 mt-3">
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors"
                >
                  Try Again
                </button>
              )}
              {onDismiss && (
                <button
                  onClick={onDismiss}
                  className="px-3 py-1.5 border border-red-300 text-red-700 hover:bg-red-50 text-xs font-medium rounded-md transition-colors"
                >
                  Dismiss
                </button>
              )}
            </div>
          )}
        </div>
        
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-red-400 hover:text-red-600 transition-colors"
          >
            <X size={16} />
          </button>
        )}
      </div>
    </motion.div>
  );

  const renderAlertError = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-red-100 border border-red-300 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-start gap-3">
        {getIcon()}
        <div className="flex-1">
          <h3 className={`font-semibold text-red-800 mb-1 ${getTitleSize()}`}>
            {getErrorTitle(error.type)}
          </h3>
          <p className={`text-red-700 mb-2 ${getMessageSize()}`}>
            {error.userFriendlyMessage}
          </p>

          {hasAdditionalDetails && (
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-blue-600 hover:text-blue-800 text-xs font-medium flex items-center gap-1"
            >
              {showDetails ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
              {showDetails ? 'Hide Details' : 'Show Details'}
            </button>
          )}

          {showDetails && hasAdditionalDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 bg-red-50 rounded-md p-3 space-y-3"
            >
              {hasBackendDetails && (
                <div>
                  <h4 className="text-xs font-semibold text-red-800 mb-1 flex items-center gap-1">
                    🔍 Technical Details
                  </h4>
                  <p className="text-xs text-red-700 font-mono bg-white p-2 rounded border">
                    {error.backendMessage}
                  </p>
                </div>
              )}

              {hasValidationErrors && (
                <div>
                  <h4 className="text-xs font-semibold text-red-800 mb-2 flex items-center gap-1">
                    ⚠️ Please fix these issues
                  </h4>
                  <div className="space-y-1">
                    {error.validationErrors!.map((err, index) => (
                      <div key={index} className="flex items-start gap-2 text-xs">
                        <span className="text-red-600 mt-0.5">•</span>
                        <div className="flex-1">
                          <span className="font-medium text-red-700">{err.field}:</span>
                          <span className="text-red-600 ml-1">{err.message}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </div>
      </div>

      {(onRetry || onDismiss) && (
        <div className="flex gap-2 mt-3">
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors"
            >
              Try Again
            </button>
          )}
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="px-4 py-2 border border-red-300 text-red-700 hover:bg-red-50 text-sm font-medium rounded-md transition-colors"
            >
              Dismiss
            </button>
          )}
        </div>
      )}
    </motion.div>
  );

  const renderFullscreenError = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"
    >
      <div className="bg-red-500/20 backdrop-blur-xl rounded-2xl p-8 border border-red-500/30 max-w-md mx-auto text-center">
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="flex justify-center mb-4"
        >
          {getIcon()}
        </motion.div>

        <h3 className="text-white text-xl font-bold mb-3">
          {getErrorTitle(error.type)}
        </h3>

        <p className="text-red-200 mb-6 leading-relaxed">
          {error.userFriendlyMessage}
        </p>

        {hasAdditionalDetails && (
          <>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-blue-300 hover:text-blue-200 text-sm font-medium mb-4 flex items-center gap-1 mx-auto"
            >
              {showDetails ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              {showDetails ? 'Hide Details' : 'Show Details'}
            </button>

            {showDetails && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-white/10 rounded-lg p-4 mb-6 text-left"
              >
                {hasBackendDetails && (
                  <div className="mb-4">
                    <h4 className="text-white/80 text-sm font-semibold mb-2 flex items-center gap-1">
                      🔍 Technical Details
                    </h4>
                    <p className="text-white/70 text-sm font-mono bg-black/20 p-2 rounded border border-white/20">
                      {error.backendMessage}
                    </p>
                  </div>
                )}

                {hasValidationErrors && (
                  <div>
                    <h4 className="text-white/80 text-sm font-semibold mb-2 flex items-center gap-1">
                      ⚠️ Please fix these issues
                    </h4>
                    <div className="space-y-1">
                      {error.validationErrors!.map((err, index) => (
                        <div key={index} className="flex items-start gap-2 text-sm">
                          <span className="text-white/60 mt-0.5">•</span>
                          <div className="flex-1">
                            <span className="font-medium text-white/80">{err.field}:</span>
                            <span className="text-white/60 ml-1">{err.message}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </>
        )}

        {onRetry && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onRetry}
            className="bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 mx-auto"
          >
            <AlertCircle size={20} />
            Try Again
          </motion.button>
        )}
      </div>
    </motion.div>
  );

  const renderFloatingError = () => (
    <motion.div
      initial={{ opacity: 0, y: 100 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 100 }}
      className={`fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto ${className}`}
    >
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg backdrop-blur-sm">
        <div className="flex items-start gap-3">
          {getIcon()}
          <div className="flex-1 min-w-0">
            <h3 className={`font-semibold text-red-800 mb-1 ${getTitleSize()}`}>
              {getErrorTitle(error.type)}
            </h3>
            <p className={`text-red-700 mb-3 ${getMessageSize()}`}>
              {error.userFriendlyMessage}
            </p>

            {hasAdditionalDetails && (
              <>
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="text-blue-600 hover:text-blue-800 text-xs font-medium flex items-center gap-1 mb-3 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded transition-colors"
                >
                  {showDetails ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                  {showDetails ? 'Hide Details' : 'Show Details'}
                </button>

                {showDetails && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="bg-red-100 rounded-md p-3 space-y-3 max-h-40 overflow-y-auto"
                  >
                    {hasBackendDetails && (
                      <div>
                        <h4 className="text-xs font-semibold text-red-800 mb-1 flex items-center gap-1">
                          🔍 Technical Details
                        </h4>
                        <p className="text-xs text-red-700 font-mono bg-red-50 p-2 rounded border">
                          {error.backendMessage}
                        </p>
                      </div>
                    )}

                    {hasValidationErrors && (
                      <div>
                        <h4 className="text-xs font-semibold text-red-800 mb-2 flex items-center gap-1">
                          ⚠️ Please fix these issues
                        </h4>
                        <div className="space-y-1">
                          {error.validationErrors!.map((err, index) => (
                            <div key={index} className="flex items-start gap-2 text-xs">
                              <span className="text-red-600 mt-0.5">•</span>
                              <div className="flex-1">
                                <span className="font-medium text-red-700">{err.field}:</span>
                                <span className="text-red-600 ml-1">{err.message}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
              </>
            )}

            {(onRetry || onDismiss) && (
              <div className="flex gap-2 mt-3">
                {onRetry && (
                  <button
                    onClick={onRetry}
                    className="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors"
                  >
                    Try Again
                  </button>
                )}
                {onDismiss && (
                  <button
                    onClick={onDismiss}
                    className="px-3 py-1.5 border border-red-300 text-red-700 hover:bg-red-50 text-xs font-medium rounded-md transition-colors"
                  >
                    Dismiss
                  </button>
                )}
              </div>
            )}
          </div>

          {onDismiss && (
            <button
              onClick={onDismiss}
              className="text-red-400 hover:text-red-600 transition-colors"
            >
              <X size={16} />
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );

  const getErrorTitle = (type: string) => {
    switch (type) {
      case 'network':
        return 'Connection Problem';
      case 'server':
        return 'Service Unavailable';
      case 'auth':
        return 'Authentication Required';
      case 'validation':
        return 'Validation Error';
      case 'notFound':
        return 'Not Found';
      case 'conflict':
        return 'Conflict';
      case 'forbidden':
        return 'Access Denied';
      default:
        return 'Something went wrong';
    }
  };

  switch (variant) {
    case 'inline':
      return renderInlineError();
    case 'alert':
      return renderAlertError();
    case 'fullscreen':
      return renderFullscreenError();
    case 'floating':
      return renderFloatingError();
    default:
      return renderCardError();
  }
};
