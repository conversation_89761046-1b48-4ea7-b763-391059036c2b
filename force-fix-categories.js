// Force fix all product categories to proper names
// This will update ALL products regardless of current category

const API_BASE_URL = 'http://192.168.1.9:3000/api';

async function forceFixCategories() {
  try {
    console.log('🔧 Force updating ALL product categories...');
    
    // First get all suppliers
    const suppliersResponse = await fetch(`${API_BASE_URL}/suppliers`);
    const suppliersData = await suppliersResponse.json();
    
    if (!suppliersData.success) {
      console.error('❌ Failed to get suppliers:', suppliersData.message);
      return;
    }
    
    const suppliers = suppliersData.data;
    console.log(`📋 Found ${suppliers.length} suppliers`);
    
    // Smart category mapping
    const getCategoryFromProductName = (productName) => {
      const name = productName.toLowerCase();
      
      if (name.includes('شاورما') || name.includes('shawarma')) {
        return 'Shawarma';
      }
      if (name.includes('ساندويش') || name.includes('sandwich') || name.includes('سندويش')) {
        return 'Sandwiches';
      }
      if (name.includes('مشاوي') || name.includes('مشوي') || name.includes('grill') || name.includes('grilled')) {
        return 'Grills';
      }
      if (name.includes('عصير') || name.includes('juice') || name.includes('مشروب') || name.includes('drink')) {
        return 'Beverages';
      }
      if (name.includes('سلطة') || name.includes('salad') || name.includes('حمص') || name.includes('hummus')) {
        return 'Appetizers';
      }
      if (name.includes('حلويات') || name.includes('dessert') || name.includes('كنافة') || name.includes('بقلاوة')) {
        return 'Desserts';
      }
      
      return 'Main Dishes';
    };
    
    let totalUpdated = 0;
    
    for (const supplier of suppliers) {
      console.log(`\n🏪 Processing supplier: ${supplier.name}`);
      
      for (const product of supplier.products) {
        const newCategory = getCategoryFromProductName(product.name);
        const oldCategory = product.category;
        
        if (oldCategory !== newCategory) {
          console.log(`   📦 "${product.name}": ${oldCategory} → ${newCategory}`);
          
          // Update the product
          try {
            const updateResponse = await fetch(`${API_BASE_URL}/suppliers/${supplier.id}/products/${product.id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                ...product,
                category: newCategory
              })
            });
            
            if (updateResponse.ok) {
              totalUpdated++;
            } else {
              console.error(`     ❌ Failed to update ${product.name}`);
            }
          } catch (error) {
            console.error(`     ❌ Error updating ${product.name}:`, error.message);
          }
        } else {
          console.log(`   ✅ "${product.name}": already correct (${oldCategory})`);
        }
      }
    }
    
    console.log(`\n🎉 Done! Updated ${totalUpdated} products with proper categories.`);
    
  } catch (error) {
    console.error('❌ ERROR:', error.message);
    console.log('💡 Make sure your backend server is running on http://192.168.1.9:3000');
  }
}

// Run the fix
forceFixCategories();
