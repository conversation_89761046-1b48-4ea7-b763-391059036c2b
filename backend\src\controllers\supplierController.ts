import { Request, Response } from 'express';
import { Supplier } from '../models/Supplier';
import User from '../models/User';
import { validationResult } from 'express-validator';
import { AuthenticatedRequest } from '../types';
import { SupplierProductCategory } from '../models/SupplierProductCategory';

export class SupplierController {
  // Validate and sanitize product options
  static validateProductOptions(productData: any) {
    const result: {
      restaurantOptions: {
        additions?: Array<{ id: string; name: string; price: number }>;
        without?: string[];
        sides?: Array<{ id: string; name: string; price: number }>;
      };
      clothingOptions: {
        sizes?: string[];
        colors?: string[];
        gallery?: string[];
      };
      customOptions: Array<{
        id: string;
        title: string;
        type: 'text' | 'number' | 'select' | 'multi-select';
        values: Array<{
          id: string;
          name: string;
          price?: number;
        }>;
      }>;
    } = {
      restaurantOptions: {},
      clothingOptions: {},
      customOptions: []
    };

    // Validate Restaurant Options
    if (productData.restaurantOptions) {
      const { additions, without, sides } = productData.restaurantOptions;

      // Validate additions
      if (additions && Array.isArray(additions)) {
        result.restaurantOptions.additions = additions
          .filter(item => item && typeof item === 'object')
          .map(item => ({
            id: String(item.id || Date.now() + Math.random()),
            name: String(item.name || '').trim().substring(0, 100),
            price: Math.max(0, Math.min(10000, Number(item.price) || 0))
          }))
          .filter(item => item.name.length > 0);
      }

      // Validate without options (array of strings)
      if (without && Array.isArray(without)) {
        result.restaurantOptions.without = without
          .filter(item => typeof item === 'string' && item.trim().length > 0)
          .map(item => String(item).trim().substring(0, 100))
          .slice(0, 50); // Limit to 50 items
      }

      // Validate sides
      if (sides && Array.isArray(sides)) {
        result.restaurantOptions.sides = sides
          .filter(item => item && typeof item === 'object')
          .map(item => ({
            id: String(item.id || Date.now() + Math.random()),
            name: String(item.name || '').trim().substring(0, 100),
            price: Math.max(0, Math.min(10000, Number(item.price) || 0))
          }))
          .filter(item => item.name.length > 0);
      }
    }

    // Validate Clothing Options
    if (productData.clothingOptions) {
      const { sizes, colors, gallery } = productData.clothingOptions;

      // Validate sizes
      if (sizes && Array.isArray(sizes)) {
        result.clothingOptions.sizes = sizes
          .filter(item => typeof item === 'string' && item.trim().length > 0)
          .map(item => String(item).trim().substring(0, 50))
          .slice(0, 20); // Limit to 20 sizes
      }

      // Validate colors
      if (colors && Array.isArray(colors)) {
        result.clothingOptions.colors = colors
          .filter(item => typeof item === 'string' && item.trim().length > 0)
          .map(item => String(item).trim().substring(0, 50))
          .slice(0, 30); // Limit to 30 colors
      }

      // Validate gallery (URLs)
      if (gallery && Array.isArray(gallery)) {
        result.clothingOptions.gallery = gallery
          .filter(item => typeof item === 'string' && item.trim().length > 0)
          .map(item => String(item).trim())
          .filter(url => {
            try {
              new URL(url);
              return true;
            } catch {
              return url.startsWith('data:image/'); // Allow base64 images
            }
          })
          .slice(0, 10); // Limit to 10 gallery images
      }
    }

    // Validate Custom Options
    if (productData.customOptions && Array.isArray(productData.customOptions)) {
      result.customOptions = productData.customOptions
        .filter((option: any) => option && typeof option === 'object')
        .map((option: any) => {
          const validatedOption = {
            id: String(option.id || Date.now() + Math.random()),
            title: String(option.title || '').trim().substring(0, 100),
            type: ['text', 'number', 'select', 'multi-select'].includes(option.type)
              ? option.type as 'text' | 'number' | 'select' | 'multi-select'
              : 'text' as const,
            values: [] as Array<{
              id: string;
              name: string;
              price?: number;
            }>
          };

          // Validate values for select/multi-select types
          if ((option.type === 'select' || option.type === 'multi-select') &&
              option.values && Array.isArray(option.values)) {
            validatedOption.values = option.values
              .filter((value: any) => value && (typeof value === 'string' || typeof value === 'object'))
              .map((value: any) => {
                // Handle both old string format and new object format
                if (typeof value === 'string') {
                  return {
                    id: Date.now() + Math.random().toString(),
                    name: String(value).trim().substring(0, 100),
                    price: 0
                  };
                } else {
                  return {
                    id: String(value.id || Date.now() + Math.random()),
                    name: String(value.name || '').trim().substring(0, 100),
                    price: Math.max(0, Math.min(1000, Number(value.price) || 0))
                  };
                }
              })
              .filter((value: any) => value.name.length > 0)
              .slice(0, 50); // Limit to 50 values per option
          }

          return validatedOption;
        })
        .filter((option: any) => option.title.length > 0)
        .slice(0, 20); // Limit to 20 custom options
    }

    return result;
  }

  // Additional security validation for product options
  static validateOptionsSecurely(validatedOptions: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for excessive data that might indicate an attack
    const totalOptionsCount =
      (validatedOptions.restaurantOptions?.additions?.length || 0) +
      (validatedOptions.restaurantOptions?.without?.length || 0) +
      (validatedOptions.restaurantOptions?.sides?.length || 0) +
      (validatedOptions.clothingOptions?.sizes?.length || 0) +
      (validatedOptions.clothingOptions?.colors?.length || 0) +
      (validatedOptions.clothingOptions?.gallery?.length || 0) +
      (validatedOptions.customOptions?.length || 0);

    if (totalOptionsCount > 200) {
      errors.push('Too many options. Maximum 200 total options allowed.');
    }

    // Validate restaurant options pricing
    if (validatedOptions.restaurantOptions?.additions) {
      for (const addition of validatedOptions.restaurantOptions.additions) {
        if (addition.price > 1000) {
          errors.push(`Addition "${addition.name}" price is too high. Maximum 1000 allowed.`);
        }
      }
    }

    if (validatedOptions.restaurantOptions?.sides) {
      for (const side of validatedOptions.restaurantOptions.sides) {
        if (side.price > 1000) {
          errors.push(`Side "${side.name}" price is too high. Maximum 1000 allowed.`);
        }
      }
    }

    // Validate custom options complexity
    if (validatedOptions.customOptions) {
      for (const option of validatedOptions.customOptions) {
        if (option.values && option.values.length > 100) {
          errors.push(`Custom option "${option.title}" has too many values. Maximum 100 allowed.`);
        }

        // Validate prices for custom option values
        if (option.values) {
          for (const value of option.values) {
            if (value.price && value.price > 1000) {
              errors.push(`Custom option value "${value.name}" price is too high. Maximum 1000 allowed.`);
            }
          }
        }
      }
    }

    // Check for suspicious patterns (potential XSS/injection attempts)
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:text\/html/i,
      /vbscript:/i
    ];

    const checkForSuspiciousContent = (text: string, fieldName: string) => {
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(text)) {
          errors.push(`Suspicious content detected in ${fieldName}. Please remove any script tags or JavaScript.`);
          break;
        }
      }
    };

    // Check all text fields for suspicious content
    if (validatedOptions.restaurantOptions?.additions) {
      validatedOptions.restaurantOptions.additions.forEach((item: any) => {
        checkForSuspiciousContent(item.name, 'restaurant addition name');
      });
    }

    if (validatedOptions.restaurantOptions?.without) {
      validatedOptions.restaurantOptions.without.forEach((item: string) => {
        checkForSuspiciousContent(item, 'restaurant without option');
      });
    }

    if (validatedOptions.restaurantOptions?.sides) {
      validatedOptions.restaurantOptions.sides.forEach((item: any) => {
        checkForSuspiciousContent(item.name, 'restaurant side name');
      });
    }

    if (validatedOptions.clothingOptions?.sizes) {
      validatedOptions.clothingOptions.sizes.forEach((item: string) => {
        checkForSuspiciousContent(item, 'clothing size');
      });
    }

    if (validatedOptions.clothingOptions?.colors) {
      validatedOptions.clothingOptions.colors.forEach((item: string) => {
        checkForSuspiciousContent(item, 'clothing color');
      });
    }

    if (validatedOptions.customOptions) {
      validatedOptions.customOptions.forEach((option: any) => {
        checkForSuspiciousContent(option.title, 'custom option title');
        if (option.values) {
          option.values.forEach((value: any) => {
            checkForSuspiciousContent(value.name, `custom option "${option.title}" value`);
          });
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get all active suppliers with optional filtering
  static async getSuppliers(req: Request, res: Response) {
    try {
      const { 
        category, 
        search, 
        lat, 
        lng, 
        radius = 10, 
        page = 1, 
        limit = 20,
        sortBy = 'rating',
        sortOrder = 'desc'
      } = req.query;

      // Build query - include all suppliers regardless of isActive status
      // Frontend will handle showing open/closed status
      const query: any = {};

      // Filter by category
      if (category) {
        query.category = category;
      }

      // Search by name or product names
      if (search) {
        query.$text = { $search: search as string };
      }

      // Location-based filtering (if lat/lng provided)
      if (lat && lng) {
        const latitude = parseFloat(lat as string);
        const longitude = parseFloat(lng as string);
        const radiusInKm = parseFloat(radius as string);

        // Using MongoDB's geospatial query (approximate)
        query.lat = {
          $gte: latitude - (radiusInKm / 111), // 1 degree ≈ 111 km
          $lte: latitude + (radiusInKm / 111)
        };
        query.lng = {
          $gte: longitude - (radiusInKm / (111 * Math.cos(latitude * Math.PI / 180))),
          $lte: longitude + (radiusInKm / (111 * Math.cos(latitude * Math.PI / 180)))
        };
      }

      // Pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      // Sorting
      const sort: any = {};
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

      const suppliers = await Supplier.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Supplier.countDocuments(query);

      res.json({
        success: true,
        data: suppliers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch suppliers',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier by ID
  static async getSupplierById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const supplier = await Supplier.findOne({ id })
        .select('-__v');

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      res.json({
        success: true,
        data: supplier
      });
    } catch (error) {
      console.error('Error fetching supplier:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch supplier',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get suppliers by category
  static async getSuppliersByCategory(req: Request, res: Response) {
    try {
      const { category } = req.params;
      const { search, page = 1, limit = 20 } = req.query;

      const query: any = { category };

      if (search) {
        query.name = { $regex: search, $options: 'i' };
      }

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const suppliers = await Supplier.find(query)
        .sort({ rating: -1, name: 1 })
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Supplier.countDocuments(query);

      res.json({
        success: true,
        data: suppliers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching suppliers by category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch suppliers',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier products
  static async getSupplierProducts(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { category, search, page = 1, limit = 20 } = req.query;

      const supplier = await Supplier.findOne({ id });

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      let products = supplier.products.filter(p => p.isAvailable);

      // Filter by product category
      if (category && category !== 'All') {
        products = products.filter(p => p.categoryName === category);
      }

      // Search in product names
      if (search) {
        const searchTerm = (search as string).toLowerCase();
        products = products.filter(p => 
          p.name.toLowerCase().includes(searchTerm)
        );
      }

      // Pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;
      const paginatedProducts = products.slice(skip, skip + limitNum);

      // Get unique categories from products
      const categories = ['All', ...new Set(supplier.products.map(p => p.categoryName))];

      res.json({
        success: true,
        data: {
          supplier: {
            id: supplier.id,
            name: supplier.name,
            rating: supplier.rating,
            ratings: supplier.ratings,
            deliveryTime: supplier.deliveryTime,
            openHours: supplier.openHours
          },
          products: paginatedProducts,
          categories,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: products.length,
            pages: Math.ceil(products.length / limitNum)
          }
        }
      });
    } catch (error) {
      console.error('Error fetching supplier products:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch supplier products',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get product by ID from supplier
  static async getProductById(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId, productId } = req.params;

      const supplier = await Supplier.findOne({ id: supplierId });

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const product = supplier.products.find(p => p.id === productId && p.isAvailable);

      if (!product) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          supplier: {
            id: supplier.id,
            name: supplier.name,
            rating: supplier.rating,
            ratings: supplier.ratings,
            deliveryTime: supplier.deliveryTime
          },
          product
        }
      });
    } catch (error) {
      console.error('Error fetching product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Link user to existing supplier
  static async linkToExistingSupplier(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { existingSupplierId, userEmail } = req.body;

      // If no user in request (no auth), find user by email
      let user = req.user;
      if (!user && userEmail) {
        const foundUser = await User.findOne({ email: userEmail, role: 'supplier-admin' });
        user = foundUser || undefined;
      }

      if (!user || user.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      if (!existingSupplierId) {
        res.status(400).json({
          success: false,
          message: 'Existing supplier ID is required'
        });
        return;
      }

      // Check if the existing supplier profile exists
      const existingSupplier = await Supplier.findOne({ id: existingSupplierId });
      if (!existingSupplier) {
        res.status(404).json({
          success: false,
          message: 'Existing supplier profile not found'
        });
        return;
      }

      // Map supplier category to valid businessType enum
      let businessType = 'other';
      if (existingSupplier.category === 'restaurants') {
        businessType = 'restaurant';
      } else if (existingSupplier.category === 'clothings') {
        businessType = 'clothing';
      } else if (existingSupplier.category === 'grocery') {
        businessType = 'grocery';
      } else if (existingSupplier.category === 'pharmacy') {
        businessType = 'pharmacy';
      } else if (existingSupplier.category === 'electronics') {
        businessType = 'electronics';
      }

      // Update the user's supplier ID to link to existing supplier
      const updatedUser = await User.findByIdAndUpdate(
        user._id,
        {
          $set: {
            supplierId: existingSupplierId,
            storeName: existingSupplier.name,
            businessType: businessType
          }
        },
        { new: true }
      );

      if (!updatedUser) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Optional: Remove auto-generated supplier profile if it exists and is different
      if (user.supplierId && user.supplierId !== existingSupplierId) {
        try {
          await Supplier.deleteOne({ id: user.supplierId });
          console.log(`🗑️  Removed auto-generated supplier profile: ${user.supplierId}`);
        } catch (deleteError) {
          console.log(`⚠️  Could not remove auto-generated supplier profile: ${deleteError}`);
        }
      }

      res.json({
        success: true,
        message: 'Successfully linked to existing supplier profile',
        data: {
          user: {
            id: updatedUser._id,
            email: updatedUser.email,
            supplierId: updatedUser.supplierId,
            storeName: updatedUser.storeName
          },
          supplier: {
            id: existingSupplier.id,
            name: existingSupplier.name,
            category: existingSupplier.category,
            productsCount: existingSupplier.products?.length || 0,
            rating: existingSupplier.rating
          }
        }
      });
    } catch (error) {
      console.error('Error linking to existing supplier:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to link to existing supplier',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Add product to supplier
  static async addProduct(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { supplierId } = req.params;
      const productData = req.body;
      const user = req.user;

      // CRITICAL SECURITY: Verify user is authenticated and is a supplier
      if (!user || user.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      // CRITICAL SECURITY: Verify user owns this supplier account
      if (user.supplierId !== supplierId) {
        res.status(403).json({
          success: false,
          message: 'Access denied. You can only manage your own products.'
        });
        return;
      }

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      // Additional security validations (express-validator handles basic validation)

      // Sanitize product name
      productData.name = productData.name.trim();

      // Validate image is base64 format
      if (!productData.image.startsWith('data:image/')) {
        res.status(400).json({
          success: false,
          message: 'Image must be a valid base64 image format'
        });
        return;
      }

      // Validate price ranges
      if (productData.price <= 0 || productData.price > 10000) {
        res.status(400).json({
          success: false,
          message: 'Price must be between 0.01 and 10000'
        });
        return;
      }

      // Validate discount price if provided
      if (productData.discountPrice && (productData.discountPrice < 0 || productData.discountPrice >= productData.price)) {
        res.status(400).json({
          success: false,
          message: 'Discount price must be less than regular price and greater than 0'
        });
        return;
      }

      // Check if category exists and belongs to this supplier
      const category = await SupplierProductCategory.findOne({
        name: productData.categoryName,
        supplierId: supplierId,
        isActive: true
      });
      if (!category) {
        res.status(404).json({
          success: false,
          message: `Category '${productData.categoryName}' not found or not active for this supplier`
        });
        return;
      }

      // Verify categoryId matches the found category
      if (String(category._id) !== productData.categoryId) {
        res.status(400).json({
          success: false,
          message: 'Category ID does not match category name'
        });
        return;
      }

      // Check if product name is already taken (case-insensitive)
      const existingProductName = supplier.products.find(p =>
        p.name.toLowerCase() === productData.name.toLowerCase()
      );
      if (existingProductName) {
        res.status(400).json({
          success: false,
          message: 'Product name already exists'
        });
        return;
      }

      // Generate secure unique product ID
      const productId = `${supplierId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Validate and sanitize product options
      const validatedOptions = SupplierController.validateProductOptions(productData);

      // Additional security validation
      const securityCheck = SupplierController.validateOptionsSecurely(validatedOptions);
      if (!securityCheck.isValid) {
        res.status(400).json({
          success: false,
          message: 'Product options validation failed',
          errors: securityCheck.errors
        });
        return;
      }

      const newProduct = {
        id: productId,
        name: productData.name,
        image: productData.image,
        price: productData.price,
        discountPrice: productData.discountPrice || 0,
        categoryId: productData.categoryId,
        categoryName: productData.categoryName,
        description: productData.description || '',
        isAvailable: productData.isAvailable !== false,
        restaurantOptions: validatedOptions.restaurantOptions,
        clothingOptions: validatedOptions.clothingOptions,
        customOptions: validatedOptions.customOptions
      };

      supplier.products.push(newProduct);
      await supplier.save();

      // Audit log with options details
      const optionsCount = {
        restaurant: (validatedOptions.restaurantOptions?.additions?.length || 0) +
                   (validatedOptions.restaurantOptions?.without?.length || 0) +
                   (validatedOptions.restaurantOptions?.sides?.length || 0),
        clothing: (validatedOptions.clothingOptions?.sizes?.length || 0) +
                 (validatedOptions.clothingOptions?.colors?.length || 0) +
                 (validatedOptions.clothingOptions?.gallery?.length || 0),
        custom: validatedOptions.customOptions?.length || 0
      };

      console.log(`🔒 AUDIT: User ${user.email} (${user._id}) added product "${newProduct.name}" (${newProduct.id}) to supplier ${supplierId}. Options: ${optionsCount.restaurant} restaurant, ${optionsCount.clothing} clothing, ${optionsCount.custom} custom`);

      res.status(201).json({
        success: true,
        message: 'Product added successfully',
        data: newProduct
      });
    } catch (error) {
      console.error('Error adding product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update product
  static async updateProduct(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.error('🔍 Validation errors:', errors.array());
        console.error('🔍 Request body:', req.body);
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { supplierId, productId } = req.params;
      const updateData = req.body;
      const user = req.user;

      // CRITICAL SECURITY: Verify user is authenticated and is a supplier
      if (!user || user.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      // CRITICAL SECURITY: Verify user owns this supplier account
      if (user.supplierId !== supplierId) {
        res.status(403).json({
          success: false,
          message: 'Access denied. You can only manage your own products.'
        });
        return;
      }

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const productIndex = supplier.products.findIndex(p => p.id === productId);
      if (productIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      // Additional security validations (express-validator handles basic validation)

      // Sanitize product name
      updateData.name = updateData.name.trim();

      // Validate image is base64 format
      if (!updateData.image.startsWith('data:image/')) {
        res.status(400).json({
          success: false,
          message: 'Image must be a valid base64 image format'
        });
        return;
      }

      // Validate price ranges
      if (updateData.price <= 0 || updateData.price > 10000) {
        res.status(400).json({
          success: false,
          message: 'Price must be between 0.01 and 10000'
        });
        return;
      }

      // Validate discount price if provided
      if (updateData.discountPrice && (updateData.discountPrice < 0 || updateData.discountPrice >= updateData.price)) {
        res.status(400).json({
          success: false,
          message: 'Discount price must be less than regular price and greater than 0'
        });
        return;
      }

      // Check if category exists and belongs to this supplier
      const category = await SupplierProductCategory.findOne({
        name: updateData.categoryName,
        supplierId: supplierId,
        isActive: true
      });
      if (!category) {
        res.status(404).json({
          success: false,
          message: `Category '${updateData.categoryName}' not found or not active for this supplier`
        });
        return;
      }

      // Verify categoryId matches the found category
      if (String(category._id) !== updateData.categoryId) {
        res.status(400).json({
          success: false,
          message: 'Category ID does not match category name'
        });
        return;
      }

      // Check if product name is already taken - except for the current product (case-insensitive)
      const existingProductName = supplier.products.find(p =>
        p.name.toLowerCase() === updateData.name.toLowerCase() && p.id !== productId
      );
      if (existingProductName) {
        res.status(400).json({
          success: false,
          message: 'Product name already exists'
        });
        return;
      }

      // Validate and sanitize product options
      const validatedOptions = SupplierController.validateProductOptions(updateData);

      // Additional security validation
      const securityCheck = SupplierController.validateOptionsSecurely(validatedOptions);
      if (!securityCheck.isValid) {
        res.status(400).json({
          success: false,
          message: 'Product options validation failed',
          errors: securityCheck.errors
        });
        return;
      }

      // Update product fields with validated data
      const currentProduct = supplier.products[productIndex];

      // Update basic fields
      if (updateData.name !== undefined) currentProduct.name = updateData.name;
      if (updateData.image !== undefined) currentProduct.image = updateData.image;
      if (updateData.price !== undefined) currentProduct.price = updateData.price;
      if (updateData.discountPrice !== undefined) currentProduct.discountPrice = updateData.discountPrice;
      if (updateData.categoryId !== undefined) currentProduct.categoryId = updateData.categoryId;
      if (updateData.categoryName !== undefined) currentProduct.categoryName = updateData.categoryName;
      if (updateData.description !== undefined) currentProduct.description = updateData.description;
      if (updateData.isAvailable !== undefined) currentProduct.isAvailable = updateData.isAvailable;

      // Update options with validated data
      if (updateData.restaurantOptions !== undefined) {
        currentProduct.restaurantOptions = validatedOptions.restaurantOptions;
      }
      if (updateData.clothingOptions !== undefined) {
        currentProduct.clothingOptions = validatedOptions.clothingOptions;
      }
      if (updateData.customOptions !== undefined) {
        currentProduct.customOptions = validatedOptions.customOptions;
      }

      await supplier.save();

      // Audit log with options details
      const optionsCount = {
        restaurant: (validatedOptions.restaurantOptions?.additions?.length || 0) +
                   (validatedOptions.restaurantOptions?.without?.length || 0) +
                   (validatedOptions.restaurantOptions?.sides?.length || 0),
        clothing: (validatedOptions.clothingOptions?.sizes?.length || 0) +
                 (validatedOptions.clothingOptions?.colors?.length || 0) +
                 (validatedOptions.clothingOptions?.gallery?.length || 0),
        custom: validatedOptions.customOptions?.length || 0
      };

      console.log(`🔒 AUDIT: User ${user.email} (${user._id}) updated product "${supplier.products[productIndex].name}" (${productId}) in supplier ${supplierId}. Options: ${optionsCount.restaurant} restaurant, ${optionsCount.clothing} clothing, ${optionsCount.custom} custom`);

      res.json({
        success: true,
        message: 'Product updated successfully',
        data: supplier.products[productIndex]
      });
    } catch (error) {
      console.error('Error updating product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Delete product
  static async deleteProduct(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { supplierId, productId } = req.params;
      const user = req.user;

      // CRITICAL SECURITY: Verify user is authenticated and is a supplier
      if (!user || user.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      // CRITICAL SECURITY: Verify user owns this supplier account
      if (user.supplierId !== supplierId) {
        res.status(403).json({
          success: false,
          message: 'Access denied. You can only manage your own products.'
        });
        return;
      }

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const productIndex = supplier.products.findIndex(p => p.id === productId);
      if (productIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      // Store product name for audit log before deletion
      const deletedProductName = supplier.products[productIndex].name;

      supplier.products.splice(productIndex, 1);
      await supplier.save();

      // Audit log
      console.log(`🔒 AUDIT: User ${user.email} (${user._id}) deleted product "${deletedProductName}" (${productId}) from supplier ${supplierId}`);

      res.json({
        success: true,
        message: 'Product deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Debug: Check current product categories
  static async checkProductCategories(req: Request, res: Response): Promise<void> {
    try {
      const suppliers = await Supplier.find({});
      const categoryReport: any = {};

      for (const supplier of suppliers) {
        categoryReport[supplier.name] = {};
        for (const product of supplier.products) {
          const category = product.category || 'undefined';
          if (!categoryReport[supplier.name][category]) {
            categoryReport[supplier.name][category] = [];
          }
          categoryReport[supplier.name][category].push(product.name);
        }
      }

      console.log('📊 Current product categories:', JSON.stringify(categoryReport, null, 2));

      res.json({
        success: true,
        message: 'Category report generated',
        data: categoryReport
      });
    } catch (error) {
      console.error('❌ Error checking categories:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to check categories',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Fix missing categories for all suppliers (admin utility)
  static async fixMissingCategories(req: Request, res: Response): Promise<void> {
    try {
      console.log('🔧 Starting fix for missing product categories...');

      const suppliers = await Supplier.find({});
      let totalFixed = 0;
      let suppliersUpdated = 0;

      // Smart category mapping based on product names
      const getCategoryFromProductName = (productName: string): string => {
        const name = productName.toLowerCase();

        // Arabic and English patterns for different categories
        if (name.includes('شاورما') || name.includes('shawarma') || name.includes('شاورما لحم') || name.includes('شاورما دجاج')) {
          return 'Shawarma';
        }
        if (name.includes('ساندويش') || name.includes('sandwich') || name.includes('سندويش')) {
          return 'Sandwiches';
        }
        if (name.includes('مشاوي') || name.includes('مشوي') || name.includes('grill') || name.includes('grilled') || name.includes('مشاوي مشكلة')) {
          return 'Grills';
        }
        if (name.includes('عصير') || name.includes('juice') || name.includes('مشروب') || name.includes('drink')) {
          return 'Beverages';
        }
        if (name.includes('سلطة') || name.includes('salad') || name.includes('حمص') || name.includes('hummus')) {
          return 'Appetizers';
        }
        if (name.includes('حلويات') || name.includes('dessert') || name.includes('كنافة') || name.includes('بقلاوة')) {
          return 'Desserts';
        }

        // Default fallback
        return 'Main Dishes';
      };

      for (const supplier of suppliers) {
        let hasUpdates = false;

        console.log(`🔍 Checking supplier: ${supplier.name}`);

        for (const product of supplier.products) {
          console.log(`   Product: "${product.name}" - Current category: "${product.category}"`);

          // Fix products that have missing categories OR have the wrong "restaurants" category
          if (!product.category || product.category.trim() === '' || product.category === 'restaurants') {
            // Smart categorization based on product name
            const smartCategory = getCategoryFromProductName(product.name);
            const oldCategory = product.category || 'undefined';
            product.category = smartCategory;
            hasUpdates = true;
            totalFixed++;
            console.log(`📦 ✅ Fixed product "${product.name}" in supplier "${supplier.name}" - changed from "${oldCategory}" to "${smartCategory}"`);
          } else {
            console.log(`   ⏭️  Skipped "${product.name}" - already has category: "${product.category}"`);
          }
        }

        if (hasUpdates) {
          await supplier.save();
          suppliersUpdated++;
          console.log(`✅ Updated supplier: ${supplier.name}`);
        }
      }

      console.log(`🎉 Fix completed: ${totalFixed} products fixed across ${suppliersUpdated} suppliers`);

      res.json({
        success: true,
        message: `Successfully fixed ${totalFixed} products with missing categories across ${suppliersUpdated} suppliers`,
        data: {
          productsFixed: totalFixed,
          suppliersUpdated: suppliersUpdated
        }
      });
    } catch (error) {
      console.error('❌ Error fixing missing categories:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fix missing categories',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Toggle product availability
  static async toggleProductAvailability(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { supplierId, productId } = req.params;
      const user = req.user;

      // CRITICAL SECURITY: Verify user is authenticated and is a supplier
      if (!user || user.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      // CRITICAL SECURITY: Verify user owns this supplier account
      if (user.supplierId !== supplierId) {
        res.status(403).json({
          success: false,
          message: 'Access denied. You can only manage your own products.'
        });
        return;
      }

      const supplier = await Supplier.findOne({ id: supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const product = supplier.products.find(p => p.id === productId);
      if (!product) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      product.isAvailable = !product.isAvailable;
      await supplier.save();

      // Audit log
      console.log(`🔒 AUDIT: User ${user.email} (${user._id}) ${product.isAvailable ? 'enabled' : 'disabled'} product "${product.name}" (${productId}) in supplier ${supplierId}`);

      res.json({
        success: true,
        message: `Product ${product.isAvailable ? 'enabled' : 'disabled'} successfully`,
        data: product
      });
    } catch (error) {
      console.error('Error toggling product availability:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to toggle product availability',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier profile (for authenticated supplier)
  static async getSupplierProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const user = req.user;

      if (!user || user.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      if (!user.supplierId) {
        res.status(404).json({
          success: false,
          message: 'Supplier profile not found'
        });
        return;
      }

      const supplier = await Supplier.findOne({ id: user.supplierId });
      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          user: {
            id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            phoneNumber: user.phoneNumber,
            storeName: user.storeName,
            businessType: user.businessType,
            address: user.address,
            city: user.city,
            country: user.country
          },
          supplier: {
            id: supplier.id,
            name: supplier.name,
            description: supplier.description,
            category: supplier.category,
            rating: supplier.rating,
            ratings: supplier.ratings,
            openHours: supplier.openHours,
            deliveryTime: supplier.deliveryTime,
            phone: supplier.phone,
            address: supplier.address,
            lat: supplier.lat,
            lng: supplier.lng,
            logoUrl: supplier.logoUrl,
            banner: supplier.banner,
            isActive: supplier.isActive,
            productsCount: supplier.products?.length || 0
          }
        }
      });
    } catch (error) {
      console.error('Error getting supplier profile:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get supplier profile',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update supplier profile
  static async updateSupplierProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const user = req.user;
      const updateData = req.body;

      if (!user || user.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Supplier account required.'
        });
        return;
      }

      if (!user.supplierId) {
        res.status(404).json({
          success: false,
          message: 'Supplier profile not found'
        });
        return;
      }

      // Update user data
      const userUpdateData: any = {};
      if (updateData.firstName) userUpdateData.firstName = updateData.firstName;
      if (updateData.lastName) userUpdateData.lastName = updateData.lastName;
      if (updateData.phoneNumber) userUpdateData.phoneNumber = updateData.phoneNumber;
      if (updateData.storeName) userUpdateData.storeName = updateData.storeName;
      if (updateData.address) userUpdateData.address = updateData.address;
      if (updateData.city) userUpdateData.city = updateData.city;
      if (updateData.country) userUpdateData.country = updateData.country;

      if (Object.keys(userUpdateData).length > 0) {
        await User.findByIdAndUpdate(user._id, { $set: userUpdateData });
      }

      // Update supplier data
      const supplierUpdateData: any = {};
      if (updateData.storeName) supplierUpdateData.name = updateData.storeName;
      if (updateData.description) supplierUpdateData.description = updateData.description;
      if (updateData.openHours) supplierUpdateData.openHours = updateData.openHours;
      if (updateData.deliveryTime) supplierUpdateData.deliveryTime = updateData.deliveryTime;
      if (updateData.phoneNumber) supplierUpdateData.phone = updateData.phoneNumber;
      if (updateData.address || updateData.city || updateData.country) {
        supplierUpdateData.address = `${updateData.address || ''}, ${updateData.city || ''}, ${updateData.country || ''}`.trim();
      }
      if (updateData.lat && updateData.lng) {
        supplierUpdateData.lat = updateData.lat;
        supplierUpdateData.lng = updateData.lng;
      }
      if (updateData.logoUrl) supplierUpdateData.logoUrl = updateData.logoUrl;
      if (updateData.banner) supplierUpdateData.banner = updateData.banner;
      if (updateData.isActive !== undefined) supplierUpdateData.isActive = updateData.isActive;

      const updatedSupplier = await Supplier.findOneAndUpdate(
        { id: user.supplierId },
        { $set: supplierUpdateData },
        { new: true }
      );

      if (!updatedSupplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier profile not found'
        });
        return;
      }

      // Get updated user data
      const updatedUser = await User.findById(user._id);

      res.json({
        success: true,
        message: 'Supplier profile updated successfully',
        data: {
          user: {
            id: updatedUser?._id,
            firstName: updatedUser?.firstName,
            lastName: updatedUser?.lastName,
            email: updatedUser?.email,
            phoneNumber: updatedUser?.phoneNumber,
            storeName: updatedUser?.storeName,
            businessType: updatedUser?.businessType,
            address: updatedUser?.address,
            city: updatedUser?.city,
            country: updatedUser?.country
          },
          supplier: {
            id: updatedSupplier.id,
            name: updatedSupplier.name,
            description: updatedSupplier.description,
            category: updatedSupplier.category,
            rating: updatedSupplier.rating,
            ratings: updatedSupplier.ratings,
            openHours: updatedSupplier.openHours,
            deliveryTime: updatedSupplier.deliveryTime,
            phone: updatedSupplier.phone,
            address: updatedSupplier.address,
            lat: updatedSupplier.lat,
            lng: updatedSupplier.lng,
            logoUrl: updatedSupplier.logoUrl,
            banner: updatedSupplier.banner,
            isActive: updatedSupplier.isActive,
            productsCount: updatedSupplier.products?.length || 0
          }
        }
      });
    } catch (error) {
      console.error('Error updating supplier profile:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update supplier profile',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
