const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wasel');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Supplier Schema (simplified for this script)
const SupplierSchema = new mongoose.Schema({
  id: String,
  name: String,
  category: String,
  products: [{
    id: String,
    name: String,
    image: String,
    price: Number,
    categoryId: String,
    categoryName: String,
    isAvailable: Boolean
  }]
});

const Supplier = mongoose.model('Supplier', SupplierSchema);

async function checkImageUrls() {
  try {
    await connectDB();

    console.log('🔍 Checking all products for image URL issues...\n');

    // Find all suppliers
    const suppliers = await Supplier.find({});
    
    let totalProducts = 0;
    let fileUrlProducts = 0;
    let dataUrlProducts = 0;
    let httpUrlProducts = 0;
    let emptyImageProducts = 0;
    let invalidUrlProducts = 0;

    const problematicProducts = [];

    for (const supplier of suppliers) {
      console.log(`📦 Checking supplier: ${supplier.name} (${supplier.id})`);
      
      if (!supplier.products || supplier.products.length === 0) {
        console.log('   No products found\n');
        continue;
      }

      for (const product of supplier.products) {
        totalProducts++;
        
        if (!product.image) {
          emptyImageProducts++;
          problematicProducts.push({
            supplier: supplier.name,
            supplierId: supplier.id,
            product: product.name,
            productId: product.id,
            issue: 'Empty image URL',
            imageUrl: product.image
          });
        } else if (product.image.startsWith('file://')) {
          fileUrlProducts++;
          problematicProducts.push({
            supplier: supplier.name,
            supplierId: supplier.id,
            product: product.name,
            productId: product.id,
            issue: 'File URL (not accessible in web)',
            imageUrl: product.image.substring(0, 100) + (product.image.length > 100 ? '...' : '')
          });
        } else if (product.image.startsWith('data:image/')) {
          dataUrlProducts++;
          // These are fine - base64 data URLs work cross-platform
        } else if (product.image.startsWith('http://') || product.image.startsWith('https://')) {
          httpUrlProducts++;
          // These are fine - HTTP URLs work cross-platform
        } else if (product.image.startsWith('/')) {
          httpUrlProducts++;
          // These are fine - relative URLs work
        } else {
          invalidUrlProducts++;
          problematicProducts.push({
            supplier: supplier.name,
            supplierId: supplier.id,
            product: product.name,
            productId: product.id,
            issue: 'Invalid URL format',
            imageUrl: product.image.substring(0, 100) + (product.image.length > 100 ? '...' : '')
          });
        }
      }
      
      console.log(`   Products: ${supplier.products.length}\n`);
    }

    // Summary
    console.log('📊 SUMMARY:');
    console.log(`   Total products: ${totalProducts}`);
    console.log(`   ✅ Data URLs (base64): ${dataUrlProducts}`);
    console.log(`   ✅ HTTP/HTTPS URLs: ${httpUrlProducts}`);
    console.log(`   ❌ File URLs: ${fileUrlProducts}`);
    console.log(`   ❌ Empty images: ${emptyImageProducts}`);
    console.log(`   ❌ Invalid URLs: ${invalidUrlProducts}`);
    console.log(`   🔧 Total issues: ${problematicProducts.length}\n`);

    if (problematicProducts.length > 0) {
      console.log('🚨 PROBLEMATIC PRODUCTS:');
      problematicProducts.forEach((item, index) => {
        console.log(`${index + 1}. ${item.supplier} - ${item.product}`);
        console.log(`   Issue: ${item.issue}`);
        console.log(`   Supplier ID: ${item.supplierId}`);
        console.log(`   Product ID: ${item.productId}`);
        console.log(`   Image URL: ${item.imageUrl || 'N/A'}`);
        console.log('');
      });

      console.log('💡 RECOMMENDATIONS:');
      console.log('1. Products with file:// URLs need to be re-uploaded using the mobile app');
      console.log('2. The mobile app has been updated to convert images to base64 data URLs');
      console.log('3. Users should edit these products and re-select their images');
      console.log('4. The web interface now shows a fallback for problematic images');
    } else {
      console.log('✅ All product images are in compatible formats!');
    }

  } catch (error) {
    console.error('❌ Error checking image URLs:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n📤 Disconnected from MongoDB');
  }
}

// Run the check
// Note: This is a maintenance script for checking image URL health
// Can be removed if no longer needed for debugging/monitoring
checkImageUrls();
