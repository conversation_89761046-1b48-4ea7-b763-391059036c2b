// Simple tests for error handling utility
// Run with: npm test errorHandler.test.ts

import { detectErrorType, getErrorMessage, processApiResponse, handleFetchError } from './errorHandler';

describe('Error Handler Tests', () => {
  describe('detectErrorType', () => {
    it('should detect network errors', () => {
      const networkError = new TypeError('fetch failed');
      expect(detectErrorType(networkError)).toBe('network');
    });

    it('should detect auth errors', () => {
      const authError = new Error('401 Unauthorized');
      expect(detectErrorType(authError)).toBe('auth');
    });

    it('should detect server errors', () => {
      const serverError = new Error('500 Internal Server Error');
      expect(detectErrorType(serverError)).toBe('server');
    });

    it('should detect validation errors', () => {
      const validationError = new Error('400 Bad Request');
      expect(detectErrorType(validationError)).toBe('validation');
    });

    it('should detect not found errors', () => {
      const notFoundError = new Error('404 Not Found');
      expect(detectErrorType(notFoundError)).toBe('notFound');
    });

    it('should detect conflict errors', () => {
      const conflictError = new Error('409 Conflict');
      expect(detectErrorType(conflictError)).toBe('conflict');
    });

    it('should detect forbidden errors', () => {
      const forbiddenError = new Error('403 Forbidden');
      expect(detectErrorType(forbiddenError)).toBe('forbidden');
    });

    it('should detect errors by status code', () => {
      expect(detectErrorType({}, 400)).toBe('validation');
      expect(detectErrorType({}, 401)).toBe('auth');
      expect(detectErrorType({}, 403)).toBe('forbidden');
      expect(detectErrorType({}, 404)).toBe('notFound');
      expect(detectErrorType({}, 409)).toBe('conflict');
      expect(detectErrorType({}, 422)).toBe('validation');
      expect(detectErrorType({}, 500)).toBe('server');
      expect(detectErrorType({}, 502)).toBe('server');
      expect(detectErrorType({}, 503)).toBe('server');
      expect(detectErrorType({}, 504)).toBe('server');
    });

    it('should return unknown for other errors', () => {
      const unknownError = new Error('Some other error');
      expect(detectErrorType(unknownError)).toBe('unknown');
    });
  });

  describe('getErrorMessage', () => {
    it('should return user-friendly network error message', () => {
      const message = getErrorMessage('network');
      expect(message).toBe('Connection problem. Please check your internet.');
    });

    it('should return user-friendly server error message', () => {
      const message = getErrorMessage('server');
      expect(message).toBe('Service temporarily unavailable. Please try again.');
    });

    it('should return user-friendly auth error message', () => {
      const message = getErrorMessage('auth');
      expect(message).toBe('Please log in again to continue.');
    });

    it('should return user-friendly validation error message', () => {
      const message = getErrorMessage('validation');
      expect(message).toBe('Please check your input and try again.');
    });

    it('should return user-friendly not found error message', () => {
      const message = getErrorMessage('notFound');
      expect(message).toBe('The requested information was not found.');
    });

    it('should return user-friendly conflict error message', () => {
      const message = getErrorMessage('conflict');
      expect(message).toBe('This item already exists. Please try something different.');
    });

    it('should return user-friendly forbidden error message', () => {
      const message = getErrorMessage('forbidden');
      expect(message).toBe('You don\'t have permission to access this.');
    });

    it('should return user-friendly unknown error message', () => {
      const message = getErrorMessage('unknown');
      expect(message).toBe('Something went wrong. Please try again.');
    });
  });

  describe('processApiResponse', () => {
    it('should return success for valid response', () => {
      const response = { success: true, data: {} };
      const result = processApiResponse(response);
      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should return error for failed response', () => {
      const response = { success: false, message: 'Server error' };
      const result = processApiResponse(response);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Server error');
    });

    it('should handle null response', () => {
      const result = processApiResponse(null);
      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('unknown');
    });
  });

  describe('handleFetchError', () => {
    it('should handle network errors', () => {
      const error = new TypeError('fetch failed');
      const result = handleFetchError(error);
      expect(result.type).toBe('network');
      expect(result.userFriendlyMessage).toBe('Connection problem. Please check your internet.');
    });

    it('should handle auth errors', () => {
      const error = new Error('401 Unauthorized');
      const result = handleFetchError(error);
      expect(result.type).toBe('auth');
      expect(result.userFriendlyMessage).toBe('Please log in again to continue.');
    });
  });
});

// Manual testing instructions:
/*
To test error handling manually:

1. **Network Error Test:**
   - Disconnect your internet
   - Try to load the homepage
   - Should show: "Connection problem. Please check your internet."

2. **Server Error Test:**
   - Stop your backend server
   - Try to load the homepage
   - Should show: "Service temporarily unavailable. Please try again."

3. **Auth Error Test:**
   - Clear your browser's local storage
   - Try to access protected pages
   - Should show: "Please log in again to continue."

4. **Retry Functionality:**
   - Trigger any error
   - Click "Try Again" button
   - Should retry the failed operation

5. **Fallback Content:**
   - When API fails, should still show static services
   - User can still navigate and use basic features

6. **Loading States:**
   - Should show loading spinner during API calls
   - Should show appropriate loading messages

7. **Error Recovery:**
   - After fixing the issue (reconnect internet, restart server, etc.)
   - Click "Try Again" should work normally
*/ 