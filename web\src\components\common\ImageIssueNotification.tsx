import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X, RefreshCw } from 'lucide-react';

interface ImageIssueNotificationProps {
  products: any[];
  onDismiss?: () => void;
}

/**
 * Component that checks for products with problematic image URLs and shows a notification
 */
export const ImageIssueNotification: React.FC<ImageIssueNotificationProps> = ({ 
  products, 
  onDismiss 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [problematicProducts, setProblematicProducts] = useState<any[]>([]);

  useEffect(() => {
    // Check for products with file:// URLs or other problematic image formats
    const problematic = products.filter(product => {
      if (!product.image) return true; // Empty image
      if (product.image.startsWith('file://')) return true; // File URL
      if (!product.image.startsWith('data:image/') && 
          !product.image.startsWith('http://') && 
          !product.image.startsWith('https://') && 
          !product.image.startsWith('/')) return true; // Invalid format
      return false;
    });

    setProblematicProducts(problematic);
    setIsVisible(problematic.length > 0);
  }, [products]);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  if (!isVisible || problematicProducts.length === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.95 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        className="mb-6 p-6 bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-400/40 rounded-2xl backdrop-blur-sm"
      >
        <div className="flex items-start gap-4">
          <motion.div
            animate={{ 
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="flex-shrink-0 p-3 bg-orange-500/30 rounded-2xl border border-orange-400/50"
          >
            <AlertTriangle size={24} className="text-orange-300" />
          </motion.div>

          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <h4 className="text-orange-200 text-lg font-bold">
                Image Display Issues Detected
              </h4>
              <span className="bg-orange-500/40 text-orange-200 text-sm font-bold px-3 py-1 rounded-xl border border-orange-400/50">
                {problematicProducts.length} product{problematicProducts.length !== 1 ? 's' : ''}
              </span>
            </div>

            <div className="text-orange-300 text-sm leading-relaxed mb-4">
              <p className="mb-2">
                Some of your products have images that can't be displayed properly in the web interface. 
                This usually happens when images were uploaded from a mobile device using local file paths.
              </p>
              
              <div className="bg-orange-500/20 rounded-xl p-4 border border-orange-400/30">
                <p className="font-semibold mb-2">Affected products:</p>
                <ul className="space-y-1">
                  {problematicProducts.slice(0, 3).map((product, index) => (
                    <li key={product.id || index} className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                      <span className="font-medium">{product.name}</span>
                      <span className="text-orange-400/80 text-xs">
                        ({product.categoryName || 'No category'})
                      </span>
                    </li>
                  ))}
                  {problematicProducts.length > 3 && (
                    <li className="text-orange-400/80 text-xs italic">
                      ...and {problematicProducts.length - 3} more
                    </li>
                  )}
                </ul>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center gap-2 px-4 py-2 bg-orange-500/30 text-orange-200 rounded-xl border border-orange-400/50 text-sm font-semibold"
              >
                <RefreshCw size={16} />
                <span>Fix: Edit products and re-upload images</span>
              </motion.div>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleDismiss}
                className="flex items-center gap-2 px-4 py-2 bg-white/10 text-white/80 rounded-xl border border-white/20 hover:bg-white/20 transition-all text-sm font-semibold"
              >
                <X size={16} />
                Dismiss
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ImageIssueNotification;
