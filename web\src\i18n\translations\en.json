{"common": {"welcome": "Welcome", "back": "Back", "next": "Next", "previous": "Previous", "continue": "Continue", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "retry": "Retry", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "submit": "Submit", "reset": "Reset", "send": "Send", "verify": "Verify", "done": "Done", "ok": "OK", "yes": "Yes", "no": "No", "allow": "Allow", "sending": "Sending...", "verifying": "Verifying...", "allFieldsRequired": "* All fields are required"}, "errors": {"networkError": "Connection problem. Please check your internet.", "serverError": "Service temporarily unavailable. Please try again.", "authError": "Please log in again to continue.", "validationError": "Please check your input and try again.", "notFoundError": "The requested information was not found.", "conflictError": "This item already exists. Please try something different.", "forbiddenError": "You don't have permission to access this.", "unknownError": "Something went wrong. Please try again.", "tryAgain": "Try Again"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "logout": "Logout", "logoutConfirmation": "Are you sure you want to logout?", "welcomeBack": "Welcome Back!", "signInToContinue": "Sign in to continue your journey", "usernameOrEmail": "Username or Email", "enterUsernameOrEmail": "Enter your username or email", "password": "Password", "enterPassword": "Enter your password", "forgotPassword": "Forgot Password? Click Here", "loginFailed": "Login Failed", "invalidCredentials": "Invalid email or password.", "networkError": "Network error occurred", "firstName": "First Name", "enterFirstName": "Enter your first name", "lastName": "Last Name", "enterLastName": "Enter your last name", "emailAddress": "Email Address", "enterEmailAddress": "Enter your email address", "phone": "Phone Number", "enterPhone": "Enter your phone number", "confirmPassword": "Confirm Password", "enterConfirmPassword": "Confirm your password", "username": "Username", "enterUsername": "Enter your username", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "address": "Address", "enterAddress": "Enter your address", "city": "City", "enterCity": "Enter your city", "country": "Country", "enterCountry": "Enter your country", "userType": "User Type", "customer": "Customer", "supplier": "Supplier", "customerDescription": "I want to order from suppliers", "supplierDescription": "I want to sell products/services", "storeName": "Store Name", "enterStoreName": "Enter your store name", "businessType": "Business Type", "enterBusinessType": "Enter your business type", "changePassword": "Change Password", "emailVerified": "<PERSON><PERSON>", "openHours": "Opening Hours", "enterOpenHours": "Enter your opening hours", "notifications": "Enable Notifications", "terms": "I accept the terms and conditions", "termsRequired": "Please accept the terms and conditions to continue.", "accountCreated": "Your account has been created successfully. Welcome to BolTalab!", "resetPassword": "Reset Password", "resetYourPassword": "Reset Your Password", "identityVerified": "Your identity has been verified. Enter your new password below.", "newPassword": "New Password", "enterNewPassword": "Enter your new password", "passwordResetSuccess": "Password reset successfully! Redirecting to login...", "forgotPasswordTitle": "Forgot Password", "forgotPasswordDescription": "Enter your email address and we'll send you a verification code to reset your password.", "enterEmail": "Enter your email address", "sendCode": "Send Verification Code", "verificationCode": "Verification Code", "enterVerificationCode": "Enter the 6-digit code sent to your email", "codeNotReceived": "Didn't receive the code?", "resendCode": "Resend Code", "verifyEmail": "<PERSON><PERSON><PERSON>", "emailVerificationTitle": "Email Verification", "emailVerificationDescription": "We've sent a verification code to your email address. Please enter it below.", "emailVerifiedSuccess": "Email verified successfully! You can now login to your account.", "signingIn": "Signing In...", "signIn": "Sign In", "createAccount": "Create Account", "newToBolTalab": "New to BolTalab?", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "invalidEmail": "Invalid email address", "phoneRequired": "Phone number is required", "confirmPasswordRequired": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "usernameRequired": "Username is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "dateOfBirthRequired": "Date of birth is required", "genderRequired": "Gender is required", "selectGender": "Select gender", "addressRequired": "Address is required", "cityRequired": "City is required", "countryRequired": "Country is required", "storeNameRequired": "Store name is required for suppliers", "businessTypeRequired": "Business type is required for suppliers", "selectBusinessType": "Select business type", "restaurant": "Restaurant", "groceryStore": "Grocery Store", "pharmacy": "Pharmacy", "electronics": "Electronics", "clothing": "Clothing", "openHoursRequired": "Opening hours are required for suppliers", "passwordRecovery": "Password Recovery", "passwordRecoveryDescription": "Don't worry! We'll help you regain access to your account", "initializingSecurity": "Initializing Security...", "enterEmailForReset": "Enter your email to receive a secure reset code", "resetCodeSent": "Reset code sent! Please check your email.", "failedToSendCode": "Failed to send reset code. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again.", "sendingResetCode": "Sending Reset Code...", "sendResetCode": "Send Reset Code", "rememberPassword": "Remember your password?", "backToLogin": "Back to Login", "securingAccount": "Securing Your Account...", "securePasswordReset": "Secure Password Reset", "almostThere": "Almost there! Let's create your new secure password", "enterCodeAndPassword": "Enter verification code and create your new password", "enterSixDigitCode": "Enter the 6-digit code sent to your email", "confirmNewPassword": "Confirm New Password", "confirmNewPasswordPlaceholder": "Confirm your new password", "passwordSecurity": "Password Security", "sixPlusCharacters": "6+ characters", "uppercaseLetter": "Uppercase letter", "number": "Number", "specialCharacter": "Special character", "completeSixDigitCode": "Please enter the complete 6-digit code", "invalidResetCode": "Invalid reset code", "failedToResetPassword": "Failed to reset password. Please try again.", "resettingPassword": "Resetting Password...", "resetPasswordButton": "Reset Password", "needToGoBack": "Need to go back?", "backToEmailVerification": "Back to Email Verification", "preparingVerification": "Preparing verification...", "emailVerificationReady": "Email Verification Ready! 📧", "verificationCodeWaiting": "Your verification code is waiting for you!", "verifyYourEmail": "Verify Your Email", "pleaseVerifyEmail": "Please verify your email address to continue logging in. We've sent a 6-digit verification code to", "weSentCodeTo": "We've sent a 6-digit verification code to", "verificationCodeLabel": "Verification Code", "enterSixDigitCodeSent": "Enter the 6-digit code sent to your email", "invalidVerificationCode": "Invalid verification code. Please try again.", "emailVerifiedSuccessRedirect": "Email verified successfully! Redirecting to login...", "verifyingEmail": "Verifying...", "verifyEmailButton": "<PERSON><PERSON><PERSON>", "didntReceiveCode": "Didn't receive the code?", "sendingCode": "Sending...", "resendCodeButton": "Resend Code", "verificationCodeSent": "Verification code sent! Please check your email.", "failedToResendCode": "Failed to resend code. Please try again.", "backToSignup": "Back to Signup"}, "navigation": {"home": "Home", "orders": "Orders", "packages": "Packages", "profile": "Profile", "suppliers": "Suppliers", "analytics": "Analytics", "notifications": "Notifications", "products": "Products", "settings": "Settings"}, "signup": {"personalInformation": "Personal Information", "personalInformationDesc": "Let's start with the basics about you", "firstNamePlaceholder": "<PERSON>", "lastNamePlaceholder": "<PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "phonePlaceholder": "+962 7X XXX XXXX", "usernamePlaceholder": "johndoe", "securitySetup": "Security Setup", "securitySetupDesc": "Create your secure login credentials", "personalDetails": "Personal Details", "personalDetailsDesc": "Tell us more about yourself", "businessInformation": "Business Information", "businessInformationDesc": "Tell us about your business", "chooseRole": "Choose Your Role", "chooseRoleDesc": "Select how you want to use BolTalab", "orderAndReceive": "Order and receive deliveries", "provideProducts": "Provide products and services", "enableLocationServices": "Enable Location Services 📍", "enableLocationServicesDesc": "Help us provide lightning-fast delivery services by sharing your location. This is optional but highly recommended for the best experience.", "locationDetected": "Location detected successfully! 🎉", "coordinates": "Coordinates", "readyForDeliveries": "Ready for ultra-fast deliveries!", "allowLocationAccess": "Allow Location Access", "gettingLocation": "Getting Location...", "skipForNow": "Skip for Now", "setStoreLocation": "Set Your Store Location 🏪", "setStoreLocationDesc": "Help customers find your store easily by sharing your business location. This will boost your visibility and attract more customers.", "storeLocationSet": "Store location set successfully! 🎯", "readyToAttract": "Ready to attract customers!", "setStoreLocationButton": "Set Store Location", "businessTips": "Business Tips", "businessTipsDesc1": "• Choose a clear, memorable store name", "businessTipsDesc2": "• Select the most accurate business category", "businessTipsDesc3": "• Provide accurate opening hours for better customer experience", "passwordSecurity": "Password Security", "characters": "6+ characters", "uppercaseLetter": "Uppercase letter", "number": "Number", "specialCharacter": "Special character", "stepOf": "Step {{current}} of {{total}} - Your journey to excellence", "createAccountTitle": "Create Account", "creatingAccount": "Creating Account...", "alreadyHaveAccount": "Already have an account?", "preparingJourney": "Preparing Your Journey...", "settingUpExperience": "Setting up the ultimate signup experience", "joinTheElite": "Join the Elite! 🚀", "unlockDeliveries": "Create your account and unlock lightning-fast deliveries", "nextStep": "Next Step", "previous": "Previous", "addressPlaceholder": "Street address", "cityPlaceholder": "Amman", "countryPlaceholder": "Jordan", "storeNamePlaceholder": "Your business name", "openHoursPlaceholder": "e.g., 9:00 AM - 10:00 PM"}, "homepage": {"welcomeBack": "Welcome back", "premiumDeliveryExperience": "Premium Delivery Experience", "nablusPalestine": "Nablus, Palestine", "online": "Online", "available": "Available", "delivery": "Delivery", "service": "Service", "fastDelivery": "Fast Delivery", "secureService": "Secure Service", "scrollToExplore": "Scroll to explore services", "searchServicesSuppliers": "Search services, suppliers, or items…", "searchServicesSuppliersCompact": "Search services, suppliers...", "searchSuggestions": {"fastDelivery": "Fast Delivery", "aiChat": "AI Chat", "trackOrders": "Track Orders", "localSuppliers": "Local Suppliers"}, "searchingUniverse": "Searching the universe...", "searchResults": "Search Results", "noResultsFound": "No results found", "noResultsDescription": "Try searching for something else or browse our premium services below", "premiumServices": "Premium Services", "premiumServicesDescription": "Experience the future of delivery with our cutting-edge services designed for your convenience", "services": {"orderFromSupplier": "Order from Supplier", "orderFromSupplierSubtitle": "Browse & order from local suppliers", "sendPackage": "Send a Package", "sendPackageSubtitle": "Quick & reliable package delivery", "requestPickup": "Request Pickup", "requestPickupSubtitle": "Schedule convenient pickups", "aiChatSystem": "AI Chat System", "aiChatSystemSubtitle": "Smart assistance powered by AI", "trackMyOrders": "Track My Orders", "trackMyOrdersSubtitle": "Real-time order tracking", "trackMyPackages": "Track My Packages", "trackMyPackagesSubtitle": "Monitor package delivery status"}, "badges": {"popular": "Popular", "fast": "Fast", "flexible": "Flexible", "aiPowered": "AI Powered", "live": "Live", "secure": "Secure", "new": "New"}, "getStarted": "Get Started", "premiumMember": "Premium Member", "enjoyExclusiveBenefits": "Enjoy exclusive benefits", "aiChatModal": {"title": "AI Chat System", "description": "Experience the future of customer service with our advanced AI assistant. Get instant, intelligent help 24/7.", "features": {"instantResponse": "Instant Response", "securePrivate": "Secure & Private", "premiumAI": "Premium AI", "smartSolutions": "Smart Solutions"}, "startAIChat": "Start AI Chat", "maybeLater": "Maybe Later"}, "searchResultsSections": {"services": "Services", "categories": "Categories", "suppliers": "Suppliers", "products": "Products", "browseSuppliers": "Browse suppliers in this category", "viewSupplierDetails": "View supplier details", "viewProductDetails": "View product details", "tapToAccess": "Tap to access"}}, "profile": {"storeProfile": "Store Profile", "personalInformation": "Personal Information", "personalInformationDesc": "Manage your personal details and preferences", "storeInformation": "Store Information", "failedToLoad": "Failed to load profile"}, "language": {"english": "English", "arabic": "العربية", "changeLanguage": "Change Language", "selectLanguage": "Select Language"}, "supplierCategories": {"title": "Choose Your Category", "subtitle": "Discover amazing suppliers in Palestine", "backToHome": "Back to Home", "categories": "Categories", "chooseYourCategory": "Choose your category", "searchCategories": "Search categories...", "searchCategoriesSuppliers": "Search categories, suppliers, or products…", "premiumCategories": "Premium Categories", "premiumCategoriesDescription": "Explore our curated selection of top-quality suppliers across different categories", "loadingCategories": "Loading Categories", "loadingCategoriesDescription": "Please wait while we fetch the latest categories...", "errorTitle": "Oops! Something went wrong", "tryAgain": "Try Again", "goBack": "Go Back", "searchResults": "Search Results", "searchResultsFor": "Search Results for", "foundResults": "Found results", "noResultsFound": "No results found", "noResultsDescription": "Try searching for something else or browse our available categories", "clearSearch": "Clear Search", "noCategoriesAvailable": "No categories available", "noCategoriesDescription": "Categories will appear here once they are added to the system", "searching": "Searching...", "exploreSuppliers": "Explore Suppliers", "mostPopular": "Most Popular", "trustedPlatform": "Trusted Platform", "verifiedSuppliersOnly": "Verified suppliers only", "stats": {"categories": "Categories", "averageRating": "Average Rating", "fastDelivery": "Fast Delivery", "suppliers": "Suppliers", "rating": "Rating", "delivery": "Delivery"}, "searchSuggestions": {"restaurants": "Restaurants", "clothings": "Clothing", "pharmacies": "Pharmacies", "supermarkets": "Supermarkets"}, "categoryDescriptions": {"restaurants": "Delicious meals from local restaurants", "clothings": "Fashion & style from top brands", "pharmacies": "Health & wellness products", "supermarkets": "Fresh groceries & daily essentials"}, "badges": {"popular": "Popular", "trending": "Trending", "essential": "Essential", "fresh": "Fresh", "new": "New"}}, "productOptions": {"manageOptions": "Manage Options", "manageProductOptions": "Configure options for", "productInfo": "Product Information", "productName": "Product Name", "enterProductName": "Enter product name", "price": "Price", "restaurantOptions": "Restaurant Options", "additions": "Additions", "withoutOptions": "Without Options", "sides": "Sides", "clothingOptions": "Clothing Options", "sizes": "Sizes", "colors": "Colors", "gallery": "Gallery", "customOptions": "Custom Options", "addCustomOption": "Add Custom Option", "optionTitle": "Option Title", "enterOptionTitle": "Enter option title", "optionType": "Option Type", "optionValues": "Option Values (comma-separated)", "enterOptionValues": "Enter option values separated by commas", "text": "Text", "number": "Number", "select": "Select", "multiSelect": "Multi-Select", "additionName": "Addition name", "withoutOption": "Without option", "sideName": "Side name", "size": "Size", "color": "Color", "imageUrl": "Image URL", "add": "Add", "remove": "Remove", "saveChanges": "Save Changes"}, "suppliers": {"allSuppliers": "All Suppliers", "chooseSupplier": "Choose your supplier", "location": "Nablus, Palestine", "description": "Discover amazing suppliers and get your orders delivered fast", "searchPlaceholder": "Search suppliers...", "sortBy": {"popular": "Most Popular", "rating": "Highest Rated", "delivery": "Fastest Delivery", "distance": "Nearest"}, "viewMode": {"grid": "Grid", "list": "List"}, "status": {"open": "Open Now", "closed": "Closed"}, "more": "more", "noSuppliersFound": "No suppliers found", "noSuppliersMessage": "We couldn't find any suppliers matching your criteria. Try adjusting your search or filters.", "clearSearch": "Clear Search", "hotPromotions": "Hot Promotions", "limitedTime": "Limited Time", "availableFor": "Available for", "suppliersFound": "Suppliers Found", "sortedBy": "Sorted by", "popularity": "popularity", "failedToLoad": "Failed to load suppliers", "pleaseTryAgain": "Please try again", "off": "Off"}}