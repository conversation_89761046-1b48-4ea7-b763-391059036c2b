import { useState } from 'react';
import { Dimensions, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  YStack,
  XStack,
  Text,
  H2,
  H3,
  H4,
  <PERSON>,
  <PERSON><PERSON>,
  Select,
  View,
} from 'tamagui';
import { MotiView, AnimatePresence } from 'moti';
import { LinearGradient } from 'expo-linear-gradient';
import { useSupplierNotifications, Notification } from './useSupplierNotifications';

// Custom Badge component since Tamagui doesn't export Badge
const Badge = ({ children, bg, color, fontSize, px, py, br, ...props }: any) => (
  <View
    bg={bg}
    br={br}
    px={px}
    py={py}
    {...props}
  >
    <Text color={color} fontSize={fontSize} fontWeight="600">
      {children}
    </Text>
  </View>
);

export default function SupplierNotificationsGUI() {
  const windowWidth = Dimensions.get('window').width;
  const {
    notifications,
    unreadCount,
    mark<PERSON><PERSON><PERSON>,
    markAllAsRead,
    deleteNotification,
    clearAll,
    getNotificationsByType
  } = useSupplierNotifications();

  const [selectedFilter, setSelectedFilter] = useState<'all' | Notification['type']>('all');
  const [selectOpen, setSelectOpen] = useState(false);

  const filteredNotifications = selectedFilter === 'all' 
    ? notifications 
    : getNotificationsByType(selectedFilter);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'order': return 'receipt-outline';
      case 'payment': return 'card-outline';
      case 'system': return 'settings-outline';
      case 'review': return 'star-outline';
      case 'promotion': return 'megaphone-outline';
      default: return 'notifications-outline';
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'order': return '#16a34a';
      case 'payment': return '#2563eb';
      case 'system': return '#ea580c';
      case 'review': return '#7c3aed';
      case 'promotion': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const handleNotificationPress = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    // Handle navigation based on notification type
    if (notification.orderId) {
      // Navigate to order details
      console.log('Navigate to order:', notification.orderId);
    }
  };

  const handleDeleteNotification = (id: string) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteNotification(id) }
      ]
    );
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear All', style: 'destructive', onPress: clearAll }
      ]
    );
  };

  const filterOptions = [
    { value: 'all', label: 'All Notifications', count: notifications.length },
    { value: 'order', label: 'Orders', count: getNotificationsByType('order').length },
    { value: 'payment', label: 'Payments', count: getNotificationsByType('payment').length },
    { value: 'review', label: 'Reviews', count: getNotificationsByType('review').length },
    { value: 'system', label: 'System', count: getNotificationsByType('system').length },
    { value: 'promotion', label: 'Promotions', count: getNotificationsByType('promotion').length },
  ];

  return (
    <ScrollView 
      contentContainerStyle={{ padding: 16, paddingBottom: 120, width: windowWidth }} 
      showsVerticalScrollIndicator={false}
    >
      <YStack gap="$5">
        {/* Ultra Enhanced Notifications Header */}
        <MotiView
          from={{ opacity: 0, translateY: -40, scale: 0.9 }}
          animate={{ opacity: 1, translateY: 0, scale: 1 }}
          transition={{ type: 'spring', damping: 20, stiffness: 300 }}
        >
          <Card
            elevate
            br="$10"
            overflow="hidden"
            borderWidth={0}
            shadowColor="$primary"
            shadowOffset={{ width: 0, height: 12 }}
            shadowOpacity={0.4}
            shadowRadius={24}
          >
            <LinearGradient
              colors={["#f59e0b", "#f97316", "#ea580c"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{ padding: 28 }}
            >
              {/* Decorative Background Elements */}
              <View
                style={{
                  position: 'absolute',
                  top: -40,
                  right: -40,
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                  backgroundColor: 'rgba(255,255,255,0.1)',
                }}
              />
              <View
                style={{
                  position: 'absolute',
                  bottom: -20,
                  left: -20,
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  backgroundColor: 'rgba(255,255,255,0.08)',
                }}
              />

              <YStack gap="$5">
                {/* Main Header Content */}
                <XStack ai="center" gap="$4">
                  <MotiView
                    from={{ scale: 0, rotate: '-180deg' }}
                    animate={{ scale: 1, rotate: '0deg' }}
                    transition={{ delay: 400, type: 'spring', damping: 15 }}
                  >
                    <View style={{ position: 'relative' }}>
                      <LinearGradient
                        colors={['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']}
                        style={{
                          borderRadius: 20,
                          padding: 16,
                          borderWidth: 2,
                          borderColor: 'rgba(255,255,255,0.2)',
                        }}
                      >
                        <Ionicons name="notifications" size={36} color="white" />
                      </LinearGradient>

                      {unreadCount > 0 && (
                        <MotiView
                          from={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 600, type: 'spring', damping: 10 }}
                          style={{
                            position: 'absolute',
                            top: -8,
                            right: -8,
                          }}
                        >
                          <LinearGradient
                            colors={['#ef4444', '#dc2626']}
                            style={{
                              borderRadius: 16,
                              paddingHorizontal: 8,
                              paddingVertical: 4,
                              borderWidth: 2,
                              borderColor: 'white',
                            }}
                          >
                            <Text color="white" fontSize="$2" fontWeight="800">
                              {unreadCount > 99 ? '99+' : unreadCount}
                            </Text>
                          </LinearGradient>
                        </MotiView>
                      )}
                    </View>
                  </MotiView>

                  <YStack flex={1} gap="$2">
                    <MotiView
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 600, duration: 600 }}
                    >
                      <H2 color="white" fontWeight="900" fontSize="$8">
                        <Text>Notifications</Text>
                      </H2>
                    </MotiView>

                    <MotiView
                      from={{ opacity: 0, translateX: -20 }}
                      animate={{ opacity: 1, translateX: 0 }}
                      transition={{ delay: 700, duration: 600 }}
                    >
                      <XStack ai="center" gap="$2">
                        {unreadCount > 0 ? (
                          <>
                            <View
                              style={{
                                backgroundColor: '#ef4444',
                                borderRadius: 8,
                                paddingHorizontal: 8,
                                paddingVertical: 4,
                              }}
                            >
                              <Text color="white" fontSize="$2" fontWeight="600">
                                {unreadCount} NEW
                              </Text>
                            </View>
                            <Text color="white" opacity={0.9} fontSize="$4" fontWeight="500">
                              You have unread messages
                            </Text>
                          </>
                        ) : (
                          <>
                            <View
                              style={{
                                backgroundColor: 'rgba(255,255,255,0.2)',
                                borderRadius: 8,
                                paddingHorizontal: 8,
                                paddingVertical: 4,
                              }}
                            >
                              <Text color="white" fontSize="$2" fontWeight="600">
                                ✓ ALL READ
                              </Text>
                            </View>
                            <Text color="white" opacity={0.9} fontSize="$4" fontWeight="500">
                              You're all caught up!
                            </Text>
                          </>
                        )}
                      </XStack>
                    </MotiView>
                  </YStack>
                </XStack>

                {/* Enhanced Stats Preview */}
                <MotiView
                  from={{ opacity: 0, translateY: 20 }}
                  animate={{ opacity: 1, translateY: 0 }}
                  transition={{ delay: 800, duration: 600 }}
                >
                  <XStack gap="$4" jc="space-between">
                    <YStack ai="center" gap="$1">
                      <Text color="white" opacity={0.8} fontSize="$2" fontWeight="500">
                        TOTAL
                      </Text>
                      <Text color="white" fontSize="$5" fontWeight="800">
                        {notifications.length}
                      </Text>
                    </YStack>

                    <View
                      style={{
                        width: 1,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                      }}
                    />

                    <YStack ai="center" gap="$1">
                      <Text color="white" opacity={0.8} fontSize="$2" fontWeight="500">
                        UNREAD
                      </Text>
                      <Text color="white" fontSize="$5" fontWeight="800">
                        {unreadCount}
                      </Text>
                    </YStack>

                    <View
                      style={{
                        width: 1,
                        backgroundColor: 'rgba(255,255,255,0.3)',
                      }}
                    />

                    <YStack ai="center" gap="$1">
                      <Text color="white" opacity={0.8} fontSize="$2" fontWeight="500">
                        TODAY
                      </Text>
                      <Text color="white" fontSize="$5" fontWeight="800">
                        {notifications.filter(n => {
                          const today = new Date();
                          const notifDate = new Date(n.timestamp);
                          return notifDate.toDateString() === today.toDateString();
                        }).length}
                      </Text>
                    </YStack>
                  </XStack>
                </MotiView>

                {/* Enhanced Action Buttons */}
                <MotiView
                  from={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 900, duration: 500 }}
                >
                  <XStack gap="$3" jc="center">
                    {unreadCount > 0 && (
                      <Button
                        size="$4"
                        bg="rgba(255,255,255,0.15)"
                        color="white"
                        borderColor="rgba(255,255,255,0.3)"
                        borderWidth={2}
                        br="$8"
                        onPress={markAllAsRead}
                        icon={<Ionicons name="checkmark-done" size={18} color="white" />}
                        fontWeight="700"
                        pressStyle={{ scale: 0.95, bg: "rgba(255,255,255,0.25)" }}
                      >
                        <Text>Mark All Read</Text>
                      </Button>
                    )}

                    {notifications.length > 0 && (
                      <Button
                        size="$4"
                        bg="rgba(255,255,255,0.15)"
                        color="white"
                        borderColor="rgba(255,255,255,0.3)"
                        borderWidth={2}
                        br="$8"
                        onPress={handleClearAll}
                        icon={<Ionicons name="trash" size={18} color="white" />}
                        fontWeight="700"
                        pressStyle={{ scale: 0.95, bg: "rgba(255,255,255,0.25)" }}
                      >
                        <Text>Clear All</Text>
                      </Button>
                    )}
                  </XStack>
                </MotiView>
              </YStack>
            </LinearGradient>
          </Card>
        </MotiView>

        {/* Filter Section */}
        <MotiView
          from={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 200, duration: 500 }}
        >
          <Card
            p="$3"
            br="$6"
            bg="white"
            borderWidth={1}
            borderColor="$gray4"
          >
            <XStack ai="center" gap="$3" flexWrap="wrap">
              <Text color="$gray12" fontSize="$4" fontWeight="700" flex={1} minWidth={120}>
                Filter Notifications
              </Text>

              <Select
                open={selectOpen}
                onOpenChange={setSelectOpen}
                value={selectedFilter}
                onValueChange={(value) => setSelectedFilter(value as any)}
              >
                <Select.Trigger
                  iconAfter={<Ionicons name="chevron-down" size={14} color="#7529B3" />}
                  style={{
                    borderColor: "#e5e7eb",
                    borderRadius: 8,
                    paddingVertical: 8,
                    paddingHorizontal: 12,
                    maxWidth: windowWidth * 0.5,
                    minWidth: 120,
                  }}
                >
                  <Text color="$gray12" fontSize="$2" fontWeight="600" numberOfLines={1}>
                    {filterOptions.find(opt => opt.value === selectedFilter)?.label}
                  </Text>
                </Select.Trigger>

                <Select.Content zIndex={100}>
                  <YStack bg="white" br="$4" p="$1" maxWidth={windowWidth * 0.8}>
                    {filterOptions.map((option, index) => (
                      <Select.Item
                        key={option.value}
                        index={index}
                        value={option.value}
                        style={{
                          backgroundColor: selectedFilter === option.value ? "#7529B3" : "transparent",
                          paddingVertical: 8,
                          paddingHorizontal: 12,
                          borderRadius: 6,
                          marginBottom: 2,
                        }}
                      >
                        <XStack ai="center" jc="space-between" width="100%" gap="$2">
                          <Select.ItemText
                            style={{
                              color: selectedFilter === option.value ? "white" : "#374151",
                              fontWeight: selectedFilter === option.value ? "600" : "normal",
                              fontSize: 14,
                              flex: 1,
                            }}
                            numberOfLines={1}
                          >
                            {option.label}
                          </Select.ItemText>
                          <Badge
                            bg={selectedFilter === option.value ? "rgba(255,255,255,0.2)" : "$gray4"}
                            color={selectedFilter === option.value ? "white" : "$gray11"}
                            fontSize="$1"
                            px="$1.5"
                            py="$0.5"
                            br="$3"
                          >
                            {option.count}
                          </Badge>
                        </XStack>
                      </Select.Item>
                    ))}
                  </YStack>
                </Select.Content>
              </Select>
            </XStack>
          </Card>
        </MotiView>

        {/* Notifications List */}
        <YStack gap="$3">
          <AnimatePresence>
            {filteredNotifications.length > 0 ? (
              filteredNotifications.map((notification, index) => (
                <MotiView
                  key={notification.id}
                  from={{ opacity: 0, translateX: -20 }}
                  animate={{ opacity: 1, translateX: 0 }}
                  exit={{ opacity: 0, translateX: 20 }}
                  transition={{ delay: index * 100, duration: 400 }}
                >
                  <Card
                    elevate
                    p="$5"
                    br="$8"
                    bg="white"
                    borderWidth={notification.read ? 1 : 3}
                    borderColor={notification.read ? "$gray4" : getNotificationColor(notification.type)}
                    shadowColor={notification.read ? "$gray6" : getNotificationColor(notification.type)}
                    shadowOffset={{ width: 0, height: notification.read ? 4 : 8 }}
                    shadowOpacity={notification.read ? 0.1 : 0.2}
                    shadowRadius={notification.read ? 8 : 16}
                    pressStyle={{ scale: 0.98 }}
                    onPress={() => handleNotificationPress(notification)}
                  >
                    <XStack gap="$3" ai="flex-start">
                      {/* Enhanced Icon */}
                      <MotiView
                        from={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: index * 50, type: 'spring', damping: 15 }}
                      >
                        <LinearGradient
                          colors={notification.read
                            ? ['rgba(107, 114, 128, 0.8)', 'rgba(107, 114, 128, 0.6)']
                            : [getNotificationColor(notification.type), `${getNotificationColor(notification.type)}CC`]
                          }
                          style={{
                            borderRadius: 16,
                            padding: 12,
                            borderWidth: 2,
                            borderColor: notification.read ? 'rgba(107, 114, 128, 0.2)' : 'rgba(255,255,255,0.3)',
                          }}
                        >
                          <Ionicons
                            name={getNotificationIcon(notification.type) as any}
                            size={22}
                            color="white"
                          />
                        </LinearGradient>
                      </MotiView>

                      {/* Content */}
                      <YStack flex={1} gap="$2">
                        <XStack ai="center" jc="space-between">
                          <Text
                            color={notification.read ? "$gray11" : "$gray12"}
                            fontSize="$4"
                            fontWeight={notification.read ? "normal" : "700"}
                            flex={1}
                          >
                            {notification.title}
                          </Text>

                          <XStack ai="center" gap="$2">
                            {/* Priority Indicator */}
                            <View
                              style={{
                                width: 8,
                                height: 8,
                                borderRadius: 4,
                                backgroundColor: getPriorityColor(notification.priority),
                              }}
                            />

                            {/* Delete Button */}
                            <Button
                              size="$2"
                              chromeless
                              circular
                              icon={<Ionicons name="close" size={16} color="#6b7280" />}
                              onPress={(e) => {
                                e.stopPropagation();
                                handleDeleteNotification(notification.id);
                              }}
                            />
                          </XStack>
                        </XStack>

                        <Text
                          color={notification.read ? "$gray9" : "$gray11"}
                          fontSize="$3"
                          numberOfLines={2}
                        >
                          {notification.message}
                        </Text>

                        <XStack ai="center" jc="space-between">
                          <Text color="$gray8" fontSize="$2">
                            {formatTimeAgo(notification.timestamp)}
                          </Text>

                          {!notification.read && (
                            <Badge
                              bg="$blue9"
                              color="white"
                              fontSize="$1"
                              px="$2"
                              py="$1"
                              br="$4"
                            >
                              <Text>New</Text>
                            </Badge>
                          )}
                        </XStack>
                      </YStack>
                    </XStack>
                  </Card>
                </MotiView>
              ))
            ) : (
              <MotiView
                from={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 500 }}
              >
                <Card
                  p="$6"
                  br="$8"
                  bg="$gray1"
                  borderWidth={1}
                  borderColor="$gray4"
                >
                  <YStack ai="center" gap="$3">
                    <View
                      style={{
                        backgroundColor: '#f3f4f6',
                        borderRadius: 20,
                        padding: 16,
                      }}
                    >
                      <Ionicons name="notifications-off-outline" size={32} color="#6b7280" />
                    </View>
                    <YStack ai="center" gap="$1">
                      <H4 color="$gray11"><Text>No Notifications</Text></H4>
                      <Text color="$gray9" fontSize="$3" textAlign="center">
                        {selectedFilter === 'all'
                          ? "You're all caught up! No new notifications."
                          : `No ${selectedFilter} notifications found.`
                        }
                      </Text>
                    </YStack>
                  </YStack>
                </Card>
              </MotiView>
            )}
          </AnimatePresence>
        </YStack>
      </YStack>
    </ScrollView>
  );
}
