import React from 'react';
import { useNavigate } from 'react-router-dom';
import { X, Plus, Minus, Trash2, ShoppingCart, Sparkles, Star, Gift } from 'lucide-react';
import { SafeImage } from '../common/SafeImage';
import { useCartStore } from '../../stores/cartStore';
import { motion, AnimatePresence } from 'framer-motion';

interface CartModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CartModal: React.FC<CartModalProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { items, removeItem, updateQty, getItemsBySupplier, totalPrice, getTotalItems, getTotalPrice } = useCartStore();
  const itemsBySupplier = getItemsBySupplier();
  const supplierIds = Object.keys(itemsBySupplier);

  const handleCheckout = () => {
    if (getTotalItems() > 0) {
      onClose();
      navigate('/customer/order-checkout', {
        state: { 
          itemsBySupplier,
          totalWithoutFee: getTotalPrice()
        }
      });
    }
  };

  const formatAdditions = (item: any) => {
    const additions = [];
    if (item.selectedAdditions?.length > 0) {
      additions.push(`+${item.selectedAdditions.map((a: any) => a.name).join(', ')}`);
    }
    if (item.selectedSides?.length > 0) {
      additions.push(`Sides: ${item.selectedSides.map((s: any) => s.name).join(', ')}`);
    }
    if (item.without?.length > 0) {
      additions.push(`Without: ${item.without.join(', ')}`);
    }
    if (item.selectedSize) {
      additions.push(`Size: ${item.selectedSize}`);
    }
    if (item.selectedColor) {
      additions.push(`Color: ${item.selectedColor}`);
    }
    return additions.join(' • ');
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Enhanced Backdrop with Blur */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-gradient-to-br from-black/60 via-primary-900/40 to-black/60 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Extreme Modal Container */}
          <motion.div
            initial={{ x: '100%', scale: 0.95, opacity: 0 }}
            animate={{ x: 0, scale: 1, opacity: 1 }}
            exit={{ x: '100%', scale: 0.95, opacity: 0 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 200,
              opacity: { duration: 0.2 }
            }}
            className="fixed right-0 top-0 h-full w-full max-w-md z-50 flex flex-col"
          >
            {/* Gradient Background with Glass Effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-primary-50 backdrop-blur-xl border-l border-primary-200/50 shadow-2xl shadow-primary-500/20" />

            {/* Content Container */}
            <div className="relative h-full flex flex-col">
              {/* Extreme Header */}
              <div className="relative p-6 border-b border-gradient-to-r from-primary-200/30 via-primary-300/50 to-primary-200/30">
                {/* Background Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-primary-600/10 to-primary-500/5" />

                {/* Header Content */}
                <div className="relative flex items-center justify-between">
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                    className="flex items-center gap-3"
                  >
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl blur-lg opacity-30" />
                      <div className="relative bg-gradient-to-br from-primary-500 to-primary-700 p-3 rounded-xl shadow-lg">
                        <ShoppingCart className="w-6 h-6 text-white" />
                      </div>
                      {getTotalItems() > 0 && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-lg"
                        >
                          {getTotalItems()}
                        </motion.div>
                      )}
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-primary-800 to-gray-900 bg-clip-text text-transparent">
                        Your Cart
                      </h2>
                      <p className="text-sm text-gray-600 font-medium">
                        {getTotalItems()} {getTotalItems() === 1 ? 'item' : 'items'} • ₪{getTotalPrice().toFixed(2)}
                      </p>
                    </div>
                  </motion.div>

                  <motion.button
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                    onClick={onClose}
                    className="relative group p-3 hover:bg-gradient-to-br hover:from-red-50 hover:to-red-100 rounded-xl transition-all duration-300 hover:shadow-lg"
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-red-600/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <X className="relative w-6 h-6 text-gray-600 group-hover:text-red-600 transition-colors duration-300" />
                  </motion.button>
                </div>
              </div>

              {/* Enhanced Content */}
              <div className="flex-1 overflow-y-auto scrollbar-hide">
                {supplierIds.length === 0 ? (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="flex flex-col items-center justify-center h-full text-center p-8"
                  >
                    {/* Animated Empty State */}
                    <div className="relative mb-8">
                      <motion.div
                        animate={{
                          rotate: [0, 5, -5, 0],
                          scale: [1, 1.05, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="relative"
                      >
                        <div className="absolute inset-0 bg-gradient-to-br from-primary-400/20 to-primary-600/20 rounded-full blur-2xl" />
                        <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 p-8 rounded-full shadow-xl">
                          <ShoppingCart className="w-16 h-16 text-gray-400" />
                        </div>
                      </motion.div>

                      {/* Floating Sparkles */}
                      <motion.div
                        animate={{
                          y: [-10, 10, -10],
                          opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="absolute -top-2 -right-2"
                      >
                        <Sparkles className="w-6 h-6 text-primary-400" />
                      </motion.div>

                      <motion.div
                        animate={{
                          y: [10, -10, 10],
                          opacity: [0.3, 0.8, 0.3]
                        }}
                        transition={{
                          duration: 2.5,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: 0.5
                        }}
                        className="absolute -bottom-2 -left-2"
                      >
                        <Star className="w-5 h-5 text-secondary-400" />
                      </motion.div>
                    </div>

                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                      className="space-y-4"
                    >
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-800 via-primary-700 to-gray-800 bg-clip-text text-transparent">
                        Your cart is empty
                      </h3>
                      <p className="text-gray-600 max-w-sm leading-relaxed">
                        Discover amazing products from our suppliers and start building your perfect order!
                      </p>

                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.6, type: "spring" }}
                        className="mt-6"
                      >
                        <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary-50 to-primary-100 rounded-full border border-primary-200">
                          <Gift className="w-4 h-4 text-primary-600" />
                          <span className="text-sm font-medium text-primary-700">Start shopping to unlock rewards!</span>
                        </div>
                      </motion.div>
                    </motion.div>
                  </motion.div>
                ) : (
                  <div className="p-6 space-y-8">
                    {supplierIds.map((supplierId, supplierIndex) => {
                      const supplierItems = itemsBySupplier[supplierId];
                      const supplierName = supplierItems[0]?.supplierName || 'Supplier';

                      return (
                        <motion.div
                          key={supplierId}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 * supplierIndex }}
                          className="space-y-4"
                        >
                          {/* Enhanced Supplier Header */}
                          <div className="relative">
                            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-primary-600/5 to-primary-500/10 rounded-xl" />
                            <div className="relative flex items-center gap-3 p-4 rounded-xl border border-primary-200/50 bg-white/80 backdrop-blur-sm">
                              <div className="w-3 h-3 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full shadow-lg" />
                              <h3 className="font-bold text-lg bg-gradient-to-r from-gray-800 to-primary-700 bg-clip-text text-transparent">
                                From: {supplierName}
                              </h3>
                              <div className="ml-auto text-xs font-medium text-primary-600 bg-primary-50 px-2 py-1 rounded-full">
                                {supplierItems.length} {supplierItems.length === 1 ? 'item' : 'items'}
                              </div>
                            </div>
                          </div>

                          {/* Enhanced Cart Items */}
                          <div className="space-y-4">
                            {supplierItems.map((item, itemIndex) => (
                              <motion.div
                                key={item.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.1 * itemIndex }}
                                className="group relative"
                              >
                                {/* Item Background with Gradient */}
                                <div className="absolute inset-0 bg-gradient-to-r from-white via-gray-50/80 to-white rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300" />
                                <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-secondary-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                                <div className="relative p-5 rounded-2xl border border-gray-200/50 group-hover:border-primary-300/50 transition-all duration-300">
                                  <div className="flex gap-4">
                                    {/* Enhanced Product Image */}
                                    <div className="relative">
                                      <div className="absolute inset-0 bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                                      <SafeImage
                                        src={item.product.image}
                                        alt={item.product.name}
                                        className="relative w-20 h-20 object-cover rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300 border-2 border-white"
                                        fallbackIcon={
                                          <div className="flex flex-col items-center justify-center text-gray-400 w-20 h-20">
                                            <ShoppingCart size={24} />
                                            <span className="text-xs mt-1">No Image</span>
                                          </div>
                                        }
                                      />
                                      <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-xl" />
                                    </div>

                                    <div className="flex-1 min-w-0">
                                      {/* Product Name */}
                                      <h4 className="font-bold text-gray-900 truncate text-lg group-hover:text-primary-700 transition-colors duration-300">
                                        {item.product.name}
                                      </h4>

                                      {/* Product Additions */}
                                      {formatAdditions(item) && (
                                        <div className="mt-2 p-2 bg-gradient-to-r from-gray-100/80 to-gray-50/80 rounded-lg border border-gray-200/50">
                                          <p className="text-sm text-gray-700 font-medium">
                                            {formatAdditions(item)}
                                          </p>
                                        </div>
                                      )}

                                      {/* Price and Controls */}
                                      <div className="flex items-center justify-between mt-4">
                                        <div className="flex flex-col">
                                          <span className="text-lg font-bold bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent">
                                            ₪{(item.finalPrice * item.qty).toFixed(2)}
                                          </span>
                                          <span className="text-xs text-gray-500">
                                            ₪{item.finalPrice.toFixed(2)} each
                                          </span>
                                        </div>

                                        <div className="flex items-center gap-3">
                                          {/* Quantity Controls */}
                                          <div className="flex items-center gap-1 bg-white rounded-full p-1 shadow-lg border border-gray-200">
                                            <motion.button
                                              whileHover={{ scale: 1.1 }}
                                              whileTap={{ scale: 0.95 }}
                                              onClick={() => updateQty(item.id, item.qty - 1)}
                                              className="w-8 h-8 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 hover:from-red-100 hover:to-red-200 flex items-center justify-center transition-all duration-300 shadow-sm hover:shadow-md"
                                            >
                                              <Minus className="w-4 h-4 text-gray-600 hover:text-red-600 transition-colors" />
                                            </motion.button>

                                            <div className="w-10 text-center">
                                              <span className="font-bold text-gray-900 text-lg">
                                                {item.qty}
                                              </span>
                                            </div>

                                            <motion.button
                                              whileHover={{ scale: 1.1 }}
                                              whileTap={{ scale: 0.95 }}
                                              onClick={() => updateQty(item.id, item.qty + 1)}
                                              className="w-8 h-8 rounded-full bg-gradient-to-br from-primary-100 to-primary-200 hover:from-primary-200 hover:to-primary-300 flex items-center justify-center transition-all duration-300 shadow-sm hover:shadow-md"
                                            >
                                              <Plus className="w-4 h-4 text-primary-600 hover:text-primary-700 transition-colors" />
                                            </motion.button>
                                          </div>

                                          {/* Remove Button */}
                                          <motion.button
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.95 }}
                                            onClick={() => removeItem(item.id)}
                                            className="p-2 bg-gradient-to-br from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md border border-red-200/50"
                                          >
                                            <Trash2 className="w-5 h-5 text-red-600 hover:text-red-700 transition-colors" />
                                          </motion.button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            ))}
                          </div>

                          {/* Enhanced Supplier Subtotal */}
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.3 }}
                            className="relative mt-6"
                          >
                            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-primary-600/5 to-primary-500/10 rounded-xl" />
                            <div className="relative flex justify-between items-center p-4 border-t-2 border-gradient-to-r from-primary-200 via-primary-300 to-primary-200 bg-white/80 backdrop-blur-sm rounded-xl">
                              <span className="font-bold text-gray-900 text-lg">Subtotal:</span>
                              <div className="text-right">
                                <span className="font-bold text-2xl bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent">
                                  ₪{totalPrice(supplierId).toFixed(2)}
                                </span>
                                <p className="text-xs text-gray-500 mt-1">
                                  {supplierItems.length} {supplierItems.length === 1 ? 'item' : 'items'}
                                </p>
                              </div>
                            </div>
                          </motion.div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Extreme Footer */}
              {supplierIds.length > 0 && (
                <motion.div
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="relative border-t border-gradient-to-r from-primary-200/50 via-primary-300/70 to-primary-200/50"
                >
                  {/* Background Gradient */}
                  <div className="absolute inset-0 bg-gradient-to-t from-primary-50/80 via-white/90 to-white/95 backdrop-blur-sm" />

                  <div className="relative p-6 space-y-6">
                    {/* Total Section */}
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-primary-600/5 to-primary-500/10 rounded-2xl" />
                      <div className="relative flex justify-between items-center p-5 bg-white/80 backdrop-blur-sm rounded-2xl border border-primary-200/50 shadow-lg">
                        <div>
                          <span className="text-xl font-bold text-gray-900">Total:</span>
                          <p className="text-sm text-gray-600 mt-1">
                            {getTotalItems()} {getTotalItems() === 1 ? 'item' : 'items'} from {supplierIds.length} {supplierIds.length === 1 ? 'supplier' : 'suppliers'}
                          </p>
                        </div>
                        <div className="text-right">
                          <span className="text-3xl font-bold bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 bg-clip-text text-transparent">
                            ₪{getTotalPrice().toFixed(2)}
                          </span>
                          <p className="text-xs text-gray-500 mt-1">
                            + delivery fees
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Checkout Button */}
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleCheckout}
                      className="relative w-full group overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300"
                    >
                      {/* Button Background */}
                      <div className="absolute inset-0 bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800" />
                      <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 via-transparent to-secondary-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                      {/* Button Content */}
                      <div className="relative flex items-center justify-center gap-3 py-4 px-6">
                        <motion.div
                          animate={{
                            rotate: [0, 10, -10, 0],
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        >
                          <ShoppingCart className="w-6 h-6 text-white" />
                        </motion.div>
                        <span className="text-lg font-bold text-white">
                          Proceed to Checkout
                        </span>
                        <motion.div
                          animate={{ x: [0, 5, 0] }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          className="text-white"
                        >
                          →
                        </motion.div>
                      </div>

                      {/* Shine Effect */}
                      <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
                    </motion.button>

                    {/* Security Badge */}
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                      className="flex items-center justify-center gap-2 text-sm text-gray-600"
                    >
                      <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                      <span>Secure checkout with end-to-end encryption</span>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default CartModal;
