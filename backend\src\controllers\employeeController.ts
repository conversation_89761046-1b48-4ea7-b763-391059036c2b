import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse } from '../types';
import User from '../models/User';
import { CryptoUtils } from '../utils/crypto';

export class EmployeeController {
  // Get all employees for a supplier admin
  static async getEmployees(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const currentUser = req.user;

      if (!currentUser) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Only supplier admins can access this endpoint
      if (currentUser.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Only supplier admins can manage employees.',
        });
        return;
      }

      // Find all employees for this supplier admin
      const employees = await User.find({
        adminId: currentUser._id,
        role: 'supplier-employee',
        isActive: true
      }).select('-password -refreshTokens -emailVerificationCode -passwordResetToken');

      res.status(200).json({
        success: true,
        message: 'Employees retrieved successfully',
        data: { employees },
      });
    } catch (error) {
      console.error('Error getting employees:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Add a new employee
  static async addEmployee(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const currentUser = req.user;
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        username,
        dateOfBirth,
        gender,
        address,
        city,
        country,
        location,
        notifications = true
      } = req.body;

      if (!currentUser) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Only supplier admins can add employees
      if (currentUser.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Only supplier admins can add employees.',
        });
        return;
      }

      // Validate required fields
      if (!firstName || !lastName || !email || !phoneNumber || !password || !username || !address || !city || !country) {
        const missingFields = [];
        if (!firstName) missingFields.push('firstName');
        if (!lastName) missingFields.push('lastName');
        if (!email) missingFields.push('email');
        if (!phoneNumber) missingFields.push('phoneNumber');
        if (!password) missingFields.push('password');
        if (!username) missingFields.push('username');
        if (!address) missingFields.push('address');
        if (!city) missingFields.push('city');
        if (!country) missingFields.push('country');

        res.status(400).json({
          success: false,
          message: `Missing required fields: ${missingFields.join(', ')}`,
        });
        return;
      }

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });
      if (existingUser) {
        const field = existingUser.email === email ? 'email' : 'username';
        res.status(400).json({
          success: false,
          message: `User with this ${field} already exists`,
        });
        return;
      }

      // Create employee data
      const employeeData: any = {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        username,
        dateOfBirth,
        gender,
        address,
        city,
        country,
        role: 'supplier-employee',
        supplierId: currentUser.supplierId,
        adminId: currentUser._id,
        notifications,
        isEmailVerified: true, // Admin-created employees are automatically verified
        emailVerificationCode: CryptoUtils.generateOTP(6),
        emailVerificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      };

      // Include location if provided
      if (location && Array.isArray(location) && location.length === 2) {
        employeeData.location = {
          type: 'Point',
          coordinates: location // [longitude, latitude]
        };
      }

      const newEmployee = new User(employeeData);
      await newEmployee.save();

      res.status(201).json({
        success: true,
        message: 'Employee added successfully',
        data: { employee: newEmployee.toJSON() },
      });
    } catch (error) {
      console.error('Error adding employee:', error);

      // Handle MongoDB validation errors specifically
      if (error instanceof Error && error.name === 'ValidationError') {
        res.status(400).json({
          success: false,
          message: 'Validation error: ' + error.message,
        });
        return;
      }

      // Handle duplicate key errors
      if (error instanceof Error && (error as any).code === 11000) {
        const field = Object.keys((error as any).keyPattern || {})[0] || 'field';
        res.status(400).json({
          success: false,
          message: `A user with this ${field} already exists`,
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Update an employee
  static async updateEmployee(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const currentUser = req.user;
      const { employeeId } = req.params;
      const updateData = req.body;

      if (!currentUser) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Only supplier admins can update employees
      if (currentUser.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Only supplier admins can update employees.',
        });
        return;
      }

      // Find the employee
      const employee = await User.findOne({
        _id: employeeId,
        adminId: currentUser._id,
        role: 'supplier-employee'
      });

      if (!employee) {
        res.status(404).json({
          success: false,
          message: 'Employee not found',
        });
        return;
      }

      // Remove sensitive fields that shouldn't be updated directly
      delete updateData.role;
      delete updateData.adminId;
      delete updateData.supplierId;
      delete updateData._id;
      delete updateData.refreshTokens;
      delete updateData.emailVerificationCode;
      delete updateData.passwordResetToken;

      // If password is being updated, it will be hashed by the pre-save middleware
      const updatedEmployee = await User.findByIdAndUpdate(
        employeeId,
        { $set: updateData },
        { new: true, runValidators: true }
      ).select('-password -refreshTokens -emailVerificationCode -passwordResetToken');

      if (!updatedEmployee) {
        res.status(404).json({
          success: false,
          message: 'Employee not found',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Employee updated successfully',
        data: { employee: updatedEmployee.toJSON() },
      });
    } catch (error) {
      console.error('Error updating employee:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Delete an employee
  static async deleteEmployee(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const currentUser = req.user;
      const { employeeId } = req.params;

      if (!currentUser) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Only supplier admins can delete employees
      if (currentUser.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Only supplier admins can delete employees.',
        });
        return;
      }

      // Find and delete the employee
      const employee = await User.findOneAndUpdate(
        {
          _id: employeeId,
          adminId: currentUser._id,
          role: 'supplier-employee'
        },
        { isActive: false }, // Soft delete
        { new: true }
      );

      if (!employee) {
        res.status(404).json({
          success: false,
          message: 'Employee not found',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Employee deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting employee:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Get a specific employee
  static async getEmployee(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const currentUser = req.user;
      const { employeeId } = req.params;

      if (!currentUser) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Only supplier admins can view employee details
      if (currentUser.role !== 'supplier-admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied. Only supplier admins can view employee details.',
        });
        return;
      }

      // Find the employee
      const employee = await User.findOne({
        _id: employeeId,
        adminId: currentUser._id,
        role: 'supplier-employee',
        isActive: true
      }).select('-password -refreshTokens -emailVerificationCode -passwordResetToken');

      if (!employee) {
        res.status(404).json({
          success: false,
          message: 'Employee not found',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Employee retrieved successfully',
        data: { employee: employee.toJSON() },
      });
    } catch (error) {
      console.error('Error getting employee:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
