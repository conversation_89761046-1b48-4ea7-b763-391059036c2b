// Smart product categorization script
// This will assign proper categories like Shawarma, Sandwiches, Grills, etc.
// based on product names (supports Arabic and English)

const API_BASE_URL = 'http://***********:3000/api';

async function fixMissingCategories() {
  try {
    console.log('🔧 Starting smart product categorization...');
    console.log('📋 Will categorize products into:');
    console.log('   • <PERSON><PERSON> (شاورما)');
    console.log('   • Sandwiches (ساندويش)');
    console.log('   • Grills (مشاوي)');
    console.log('   • Beverages (عصير)');
    console.log('   • Appetizers (سلطة/حمص)');
    console.log('   • Desserts (حلويات)');
    console.log('   • Main Dishes (default)');
    console.log('');

    const response = await fetch(`${API_BASE_URL}/suppliers/fix-missing-categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ SUCCESS!');
      console.log(`📦 Categorized ${result.data.productsFixed} products`);
      console.log(`🏪 Updated ${result.data.suppliersUpdated} suppliers`);
      console.log('');
      console.log('🎉 All done! Your products now have proper categories like:');
      console.log('   - Shawarma products → "Shawarma" category');
      console.log('   - Sandwich products → "Sandwiches" category');
      console.log('   - Grill products → "Grills" category');
      console.log('   - And more...');
    } else {
      console.error('❌ FAILED:', result.message);
    }
  } catch (error) {
    console.error('❌ ERROR:', error.message);
    console.log('');
    console.log('💡 Make sure your backend server is running on http://***********:3000');
  }
}

// Run the fix
fixMissingCategories();
