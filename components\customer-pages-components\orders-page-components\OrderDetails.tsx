import { ScrollView, Dimensions, Linking, StyleSheet } from 'react-native'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { View, Text, YStack, XStack, Card, Button, Separator, H2, H3, H4, Image, Progress } from 'tamagui'
import { Ionicons, MaterialIcons } from '@expo/vector-icons'
import { useMyOrdersStore } from './useMyOrdersStore'
import { MotiView } from 'moti'
import { LinearGradient } from 'expo-linear-gradient'
import { useState, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

// Responsive design hook
const useResponsive = () => {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });
    return () => subscription?.remove();
  }, []);

  const isTablet = screenData.width >= 768;
  const isDesktop = screenData.width >= 1024;

  const getResponsiveValue = (mobile: number, tablet: number, desktop: number) => {
    if (isDesktop) return desktop;
    if (isTablet) return tablet;
    return mobile;
  };

  return { isTablet, isDesktop, getResponsiveValue, screenWidth: screenData.width };
};

export function OrderDetails() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>()
  const router = useRouter()
  const { isTablet, isDesktop, getResponsiveValue } = useResponsive();

  const order = useMyOrdersStore((s) =>
    s.orders.find((o) => o.id.toString() === id)
  )

  // Enhanced status configuration
  const statusConfig = useMemo(() => ({
    'Pending': {
      color: '#FFA500',
      icon: 'time-outline',
      bgColor: 'rgba(255, 165, 0, 0.1)',
      progress: 25,
      message: t('orderDetails.status.pendingMessage', { defaultValue: 'Your order is being confirmed' })
    },
    'Preparing': {
      color: '#FF6B6B',
      icon: 'restaurant-outline',
      bgColor: 'rgba(255, 107, 107, 0.1)',
      progress: 50,
      message: t('orderDetails.status.preparingMessage', { defaultValue: 'Your order is being prepared' })
    },
    'On the Way': {
      color: '#4ECDC4',
      icon: 'car-outline',
      bgColor: 'rgba(78, 205, 196, 0.1)',
      progress: 75,
      message: t('orderDetails.status.onTheWayMessage', { defaultValue: 'Your order is on the way' })
    },
    'Delivered': {
      color: '#45B7D1',
      icon: 'checkmark-circle-outline',
      bgColor: 'rgba(69, 183, 209, 0.1)',
      progress: 100,
      message: t('orderDetails.status.deliveredMessage', { defaultValue: 'Your order has been delivered' })
    }
  }), [t]);

  if (!order) {
    return (
      <YStack flex={1} justifyContent="center" alignItems="center" padding="$4" gap="$4">
        <MotiView
          from={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', damping: 15, stiffness: 200 }}
        >
          <View
            backgroundColor="$color4"
            borderRadius="$12"
            padding="$4"
            marginBottom="$4"
          >
            <Ionicons name="receipt-outline" size={64} color="#667eea" />
          </View>
        </MotiView>
        <H3 color="$gray12" textAlign="center">
          {t('orderDetails.orderNotFound', { defaultValue: 'Order not found' })}
        </H3>
        <Text color="$gray10" textAlign="center" fontSize="$3">
          {t('orderDetails.orderNotFoundMessage', { defaultValue: 'The order you are looking for does not exist or has been removed.' })}
        </Text>
        <Button
          marginTop="$4"
          backgroundColor="#667eea"
          borderRadius="$6"
          onPress={() => router.back()}
          pressStyle={{ scale: 0.95 }}
        >
          <XStack alignItems="center" gap="$2">
            <Ionicons name="arrow-back-outline" size={20} color="white" />
            <Text color="white" fontWeight="600">
              {t('common.goBack', { defaultValue: 'Go Back' })}
            </Text>
          </XStack>
        </Button>
      </YStack>
    )
  }

  const config = statusConfig[order.status as keyof typeof statusConfig] || statusConfig['Pending'];
  const isActive = order.status !== 'Delivered';

  return (
    <ScrollView
      contentContainerStyle={{
        paddingBottom: getResponsiveValue(100, 120, 140)
      }}
      showsVerticalScrollIndicator={false}
    >
      {/* Enhanced Professional Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          paddingTop: getResponsiveValue(50, 60, 70),
          paddingBottom: getResponsiveValue(30, 40, 50),
          paddingHorizontal: getResponsiveValue(20, 30, 40),
          borderBottomLeftRadius: getResponsiveValue(25, 30, 35),
          borderBottomRightRadius: getResponsiveValue(25, 30, 35),
        }}
      >
        <MotiView
          from={{ opacity: 0, translateY: -30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 800 }}
        >
          {/* Back Button and Title */}
          <XStack alignItems="center" justifyContent="space-between" marginBottom="$4">
            <Button
              size="$3"
              backgroundColor="rgba(255,255,255,0.15)"
              borderColor="rgba(255,255,255,0.3)"
              borderRadius="$6"
              onPress={() => router.back()}
              pressStyle={{ scale: 0.95 }}
            >
              <XStack alignItems="center" gap="$2">
                <Ionicons name="arrow-back-outline" size={20} color="white" />
                <Text color="white" fontWeight="600">
                  {t('common.back', { defaultValue: 'Back' })}
                </Text>
              </XStack>
            </Button>

            <YStack alignItems="center">
              <H3 color="white" fontWeight="800" fontSize={getResponsiveValue(24, 28, 32)}>
                {t('orderDetails.orderTitle', { defaultValue: 'Order' })}<Text>#{order.id}</Text> 
              </H3>
              <Text color="rgba(255,255,255,0.8)" fontSize="$2">
                {new Date(order.createdAt).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Text>
            </YStack>

            <View width={getResponsiveValue(80, 90, 100)} />
          </XStack>

          {/* Status Progress */}
          <Card
            backgroundColor="rgba(255,255,255,0.15)"
            borderColor="rgba(255,255,255,0.2)"
            borderRadius="$6"
            padding="$4"
            backdropFilter="blur(10px)"
          >
            <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
              <XStack alignItems="center" gap="$2">
                <View
                  backgroundColor={config.color}
                  borderRadius="$6"
                  padding="$2"
                >
                  <Ionicons name={config.icon as any} size={20} color="white" />
                </View>
                <YStack>
                  <Text color="white" fontSize="$4" fontWeight="700">
                    {t(`orders.statuses.${order.status.toLowerCase().replace(/\s+/g, '_')}`, {
                      defaultValue: order.status
                    })}
                  </Text>
                  <Text color="rgba(255,255,255,0.8)" fontSize="$2">
                    {config.message}
                  </Text>
                </YStack>
              </XStack>

              <Text color="white" fontSize="$3" fontWeight="600">
                {config.progress}%
              </Text>
            </XStack>

            {/* Progress Bar */}
            <View
              backgroundColor="rgba(255,255,255,0.2)"
              borderRadius="$4"
              height={8}
              overflow="hidden"
            >
              <MotiView
                from={{ width: '0%' }}
                animate={{ width: `${config.progress}%` }}
                transition={{ type: 'timing', duration: 1000, delay: 500 }}
                style={{
                  height: '100%',
                  backgroundColor: config.color,
                  borderRadius: 4,
                }}
              />
            </View>
          </Card>
        </MotiView>
      </LinearGradient>

      {/* Enhanced Main Content */}
      <YStack
        padding={getResponsiveValue(16, 20, 24)}
        gap="$4"
        width={getResponsiveValue('100%', '95%', '90%')}
        alignSelf='center'
        maxWidth={isDesktop ? 1200 : undefined}
        marginTop="$4"
      >
        {/* Order Items Card */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600, delay: 200 }}
        >
          <Card
            backgroundColor="$backgroundStrong"
            borderRadius="$6"
            padding={getResponsiveValue(16, 20, 24)}
            shadowColor="#000"
            shadowOffset={{ width: 0, height: 6 }}
            shadowOpacity={0.12}
            shadowRadius={16}
            elevation={6}
          >
            <XStack alignItems="center" gap="$3" marginBottom="$4">
              <View
                backgroundColor="$color4"
                borderRadius="$4"
                padding="$2"
              >
                <Ionicons name="bag-outline" size={24} color="#667eea" />
              </View>
              <YStack flex={1}>
                <H3 color="$gray12" fontWeight="800">
                  {t('orderDetails.orderItems', { defaultValue: 'Order Items' })}
                </H3>
                <Text color="$gray10" fontSize="$2">
                  {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                </Text>
              </YStack>
            </XStack>

            <YStack gap="$3">
              {order.items.map((item, index) => (
                <MotiView
                  key={index}
                  from={{ opacity: 0, translateX: -20 }}
                  animate={{ opacity: 1, translateX: 0 }}
                  transition={{ type: 'timing', duration: 400, delay: 300 + (index * 100) }}
                >
                  <XStack
                    alignItems="center"
                    justifyContent="space-between"
                    padding="$3"
                    backgroundColor="$color3"
                    borderRadius="$4"
                    borderWidth={1}
                    borderColor="$color6"
                  >
                    <XStack alignItems="center" gap="$3" flex={1}>
                      {/* Item Image Placeholder */}
                      <View
                        width={50}
                        height={50}
                        backgroundColor="$color5"
                        borderRadius="$3"
                        alignItems="center"
                        justifyContent="center"
                      >
                        {item.product.image ? (
                          <Image
                            source={{ uri: item.product.image }}
                            width={50}
                            height={50}
                            borderRadius="$3"
                          />
                        ) : (
                          <Ionicons name="image-outline" size={24} color="#667eea" />
                        )}
                      </View>

                      <YStack flex={1}>
                        <Text fontWeight="700" color="$gray12" fontSize="$4">
                          {item.product.name}
                        </Text>
                        <Text color="$gray10" fontSize="$2">
                          ₪{item.product.price.toFixed(2)} × {item.qty}
                        </Text>
                      </YStack>
                    </XStack>

                    <YStack alignItems="flex-end">
                      <Text fontWeight="800" color="$gray12" fontSize="$4">
                        ₪{(item.finalPrice).toFixed(2)}
                      </Text>
                      <View
                        backgroundColor="#667eea"
                        borderRadius="$2"
                        paddingHorizontal="$2"
                        paddingVertical="$0.5"
                      >
                        <Text color="white" fontSize="$1" fontWeight="600">
                          Qty: {item.qty}
                        </Text>
                      </View>
                    </YStack>
                  </XStack>
                </MotiView>
              ))}
            </YStack>
          </Card>
        </MotiView>

        {/* Delivery Information Card */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600, delay: 400 }}
        >
          <Card
            backgroundColor="$backgroundStrong"
            borderRadius="$6"
            padding={getResponsiveValue(16, 20, 24)}
            shadowColor="#000"
            shadowOffset={{ width: 0, height: 6 }}
            shadowOpacity={0.12}
            shadowRadius={16}
            elevation={6}
          >
            <XStack alignItems="center" gap="$3" marginBottom="$4">
              <View
                backgroundColor="$color4"
                borderRadius="$4"
                padding="$2"
              >
                <Ionicons name="location-outline" size={24} color="#667eea" />
              </View>
              <H3 color="$gray12" fontWeight="800">
                {t('orderDetails.deliveryInfo', { defaultValue: 'Delivery Information' })}
              </H3>
            </XStack>

            <YStack gap="$3">
              {/* Address */}
              <XStack alignItems="flex-start" gap="$3">
                <View
                  backgroundColor="rgba(102, 126, 234, 0.1)"
                  borderRadius="$3"
                  padding="$2"
                  marginTop="$1"
                >
                  <Ionicons name="home-outline" size={16} color="#667eea" />
                </View>
                <YStack flex={1}>
                  <Text color="$gray11" fontSize="$2" fontWeight="600" marginBottom="$1">
                    {t('orderDetails.deliveryAddress', { defaultValue: 'Delivery Address' })}
                  </Text>
                  <Text color="$gray12" fontSize="$3" lineHeight="$4">
                    {order.address?.address || t('orderDetails.noAddress', { defaultValue: 'No address provided' })}
                  </Text>
                </YStack>
              </XStack>

              <Separator backgroundColor="$color6" />

              {/* Phone */}
              <XStack alignItems="center" gap="$3">
                <View
                  backgroundColor="rgba(102, 126, 234, 0.1)"
                  borderRadius="$3"
                  padding="$2"
                >
                  <Ionicons name="call-outline" size={16} color="#667eea" />
                </View>
                <YStack flex={1}>
                  <Text color="$gray11" fontSize="$2" fontWeight="600" marginBottom="$1">
                    {t('orderDetails.phoneNumber', { defaultValue: 'Phone Number' })}
                  </Text>
                  <Text color="$gray12" fontSize="$3">
                    {order.phone || t('orderDetails.noPhone', { defaultValue: 'No phone provided' })}
                  </Text>
                </YStack>
                {order.phone && (
                  <Button
                    size="$2"
                    backgroundColor="#4CAF50"
                    borderRadius="$4"
                    onPress={() => Linking.openURL(`tel:${order.phone}`)}
                    pressStyle={{ scale: 0.95 }}
                  >
                    <Ionicons name="call" size={16} color="white" />
                  </Button>
                )}
              </XStack>

              {/* Notes */}
              {order.note && (
                <>
                  <Separator backgroundColor="$color6" />
                  <XStack alignItems="flex-start" gap="$3">
                    <View
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="$3"
                      padding="$2"
                      marginTop="$1"
                    >
                      <Ionicons name="document-text-outline" size={16} color="#667eea" />
                    </View>
                    <YStack flex={1}>
                      <Text color="$gray11" fontSize="$2" fontWeight="600" marginBottom="$1">
                        {t('orderDetails.specialNotes', { defaultValue: 'Special Notes' })}
                      </Text>
                      <Text color="$gray12" fontSize="$3" lineHeight="$4">
                        {order.note}
                      </Text>
                    </YStack>
                  </XStack>
                </>
              )}

              {/* Estimated Delivery Time */}
              <Separator backgroundColor="$color6" />
              <XStack alignItems="center" gap="$3">
                <View
                  backgroundColor="rgba(102, 126, 234, 0.1)"
                  borderRadius="$3"
                  padding="$2"
                >
                  <Ionicons name="time-outline" size={16} color="#667eea" />
                </View>
                <YStack flex={1}>
                  <Text color="$gray11" fontSize="$2" fontWeight="600" marginBottom="$1">
                    {t('orderDetails.estimatedDelivery', { defaultValue: 'Estimated Delivery' })}
                  </Text>
                  <Text color="$gray12" fontSize="$3" fontWeight="600">
                    {order.estimatedTime}
                  </Text>
                </YStack>
              </XStack>
            </YStack>
          </Card>
        </MotiView>

        {/* Payment & Summary Card */}
        <MotiView
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600, delay: 600 }}
        >
          <Card
            backgroundColor="$backgroundStrong"
            borderRadius="$6"
            padding={getResponsiveValue(16, 20, 24)}
            shadowColor="#000"
            shadowOffset={{ width: 0, height: 6 }}
            shadowOpacity={0.12}
            shadowRadius={16}
            elevation={6}
          >
            {/* Payment Method Section */}
            <XStack alignItems="center" gap="$3" marginBottom="$4">
              <View
                backgroundColor="$color4"
                borderRadius="$4"
                padding="$2"
              >
                <Ionicons
                  name={order.paymentMethod === 'cash' ? 'cash-outline' : 'card-outline'}
                  size={24}
                  color="#667eea"
                />
              </View>
              <H3 color="$gray12" fontWeight="800">
                {t('orderDetails.paymentSummary', { defaultValue: 'Payment & Summary' })}
              </H3>
            </XStack>

            {/* Payment Method */}
            <XStack alignItems="center" justifyContent="space-between" marginBottom="$4">
              <XStack alignItems="center" gap="$2">
                <Ionicons
                  name={order.paymentMethod === 'cash' ? 'cash-outline' : 'card-outline'}
                  size={16}
                  color={order.paymentMethod === 'cash' ? '#4CAF50' : '#2196F3'}
                />
                <Text color="$gray11" fontSize="$3" fontWeight="600">
                  {t('orderDetails.paymentMethod', { defaultValue: 'Payment Method' })}
                </Text>
              </XStack>
              <XStack alignItems="center" gap="$2">
                <View
                  backgroundColor={order.paymentMethod === 'cash' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(33, 150, 243, 0.1)'}
                  borderRadius="$3"
                  paddingHorizontal="$2"
                  paddingVertical="$1"
                >
                  <Text
                    color={order.paymentMethod === 'cash' ? '#4CAF50' : '#2196F3'}
                    fontSize="$2"
                    fontWeight="700"
                    textTransform="capitalize"
                  >
                    {t(`orderDetails.payment.${order.paymentMethod}`, { defaultValue: order.paymentMethod })}
                  </Text>
                </View>
                {order.supplierRecievedMoney && (
                  <View
                    backgroundColor="rgba(76, 175, 80, 0.1)"
                    borderRadius="$3"
                    paddingHorizontal="$2"
                    paddingVertical="$1"
                  >
                    <Text color="#4CAF50" fontSize="$1" fontWeight="600">
                      {t('orderDetails.paid', { defaultValue: 'PAID' })}
                    </Text>
                  </View>
                )}
              </XStack>
            </XStack>

            <Separator backgroundColor="$color6" marginBottom="$4" />

            {/* Order Summary */}
            <YStack gap="$3">
              <XStack justifyContent="space-between" alignItems="center">
                <Text color="$gray10" fontSize="$3">
                  {t('orderDetails.subtotal', { defaultValue: 'Subtotal' })}
                </Text>
                <Text color="$gray12" fontSize="$3" fontWeight="600">
                  ₪{order.subTotal.toFixed(2)}
                </Text>
              </XStack>

              <XStack justifyContent="space-between" alignItems="center">
                <Text color="$gray10" fontSize="$3">
                  {t('orderDetails.deliveryFee', { defaultValue: 'Delivery Fee' })}
                </Text>
                <Text color="$gray12" fontSize="$3" fontWeight="600">
                  ₪{order.deliveryFee.toFixed(2)}
                </Text>
              </XStack>

              {order.promo && (
                <XStack justifyContent="space-between" alignItems="center">
                  <Text color="$gray10" fontSize="$3">
                    {t('orderDetails.discount', { defaultValue: 'Discount' })}
                  </Text>
                  <Text color="#4CAF50" fontSize="$3" fontWeight="600">
                    -₪{order.promo}
                  </Text>
                </XStack>
              )}

              <Separator backgroundColor="$color6" marginVertical="$2" />

              <XStack justifyContent="space-between" alignItems="center">
                <Text color="$gray12" fontSize="$5" fontWeight="800">
                  {t('orderDetails.total', { defaultValue: 'Total' })}
                </Text>
                <Text color="#667eea" fontSize="$6" fontWeight="800">
                  ₪{order.total.toFixed(2)}
                </Text>
              </XStack>
            </YStack>
          </Card>
        </MotiView>

        {/* Enhanced Action Buttons */}
        <MotiView
          from={{ opacity: 0, translateY: 30 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing', duration: 600, delay: 800 }}
        >
          <YStack gap="$3" marginTop="$4">
            {/* Primary Actions */}
            <XStack gap="$3">
              {isActive && (
                <Button
                  flex={1}
                  size="$4"
                  backgroundColor="#667eea"
                  borderRadius="$6"
                  onPress={() => router.push({
                    pathname: "/(customer-pages)/orders/order-tracking",
                    params: { id: id }
                  })}
                  pressStyle={{ scale: 0.95 }}
                >
                  <XStack alignItems="center" gap="$2">
                    <Ionicons name="location-outline" size={20} color="white" />
                    <Text color="white" fontWeight="700" fontSize="$4">
                      {t('orderDetails.trackOrder', { defaultValue: 'Track Order' })}
                    </Text>
                  </XStack>
                </Button>
              )}

              {/* Contact Support */}
              <Button
                flex={isActive ? 1 : undefined}
                size="$4"
                backgroundColor="$color4"
                borderColor="$color6"
                borderWidth={1}
                borderRadius="$6"
                onPress={() => {
                  // You can implement contact support functionality here
                  console.log('Contact support for order:', order.id);
                }}
                pressStyle={{ scale: 0.95 }}
              >
                <XStack alignItems="center" gap="$2">
                  <Ionicons name="headset-outline" size={20} color="#667eea" />
                  <Text color="#667eea" fontWeight="600" fontSize="$3">
                    {t('orderDetails.support', { defaultValue: 'Support' })}
                  </Text>
                </XStack>
              </Button>
            </XStack>

            {/* Secondary Actions */}
            <XStack gap="$3">
              {/* Reorder Button */}
              <Button
                flex={1}
                size="$4"
                backgroundColor="$color4"
                borderColor="$color6"
                borderWidth={1}
                borderRadius="$6"
                onPress={() => {
                  // Implement reorder functionality
                  console.log('Reorder items:', order.items);
                }}
                pressStyle={{ scale: 0.95 }}
              >
                <XStack alignItems="center" gap="$2">
                  <Ionicons name="refresh-outline" size={18} color="#667eea" />
                  <Text color="#667eea" fontWeight="600" fontSize="$3">
                    {t('orderDetails.reorder', { defaultValue: 'Reorder' })}
                  </Text>
                </XStack>
              </Button>

              {/* Back Button */}
              <Button
                flex={1}
                size="$4"
                backgroundColor="$color3"
                borderRadius="$6"
                onPress={() => router.back()}
                pressStyle={{ scale: 0.95 }}
              >
                <XStack alignItems="center" gap="$2">
                  <Ionicons name="arrow-back-outline" size={18} color="$gray11" />
                  <Text color="$gray11" fontWeight="600" fontSize="$3">
                    {t('orderDetails.backToOrders', { defaultValue: 'Back to Orders' })}
                  </Text>
                </XStack>
              </Button>
            </XStack>
          </YStack>
        </MotiView>
      </YStack>
    </ScrollView>
  );
}
