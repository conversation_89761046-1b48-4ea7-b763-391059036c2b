import { useEffect, useState } from 'react';
import { ScrollView, Dimensions, Linking } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { View, Text, Button, YStack, XStack } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useSendPackageStore } from './useSendPackageStore';
import { useRequestPickupStore } from './useRequestPickupStore';
// Use react-native-web-maps directly for web
import MapView, { <PERSON><PERSON>, <PERSON>yline } from 'react-native-web-maps';

export default function PackageTracking() {
  const { id, type } = useLocalSearchParams<{ id: string; type: 'sent' | 'pickup' }>();
  const router = useRouter();
  const { width, height } = Dimensions.get('window');

  const sendRequests = useSendPackageStore((s) => s.sendRequests);
  const pickupRequests = useRequestPickupStore((s) => s.pickupRequests);
  const request = type === 'sent' ? sendRequests[Number(id)] : pickupRequests[Number(id)];

  const pickupLoc = request?.pickup;
  const dropoffLoc = type === 'sent' && 'dropoff' in request ? request?.dropoff : null;

  const customerLocation = {
    latitude: pickupLoc?.lat || 31.95,
    longitude: pickupLoc?.lng || 35.23,
  };

  const [driverLocation, setDriverLocation] = useState<[number, number]>([35.9106, 31.9539]);
  const [estimatedTime, setEstimatedTime] = useState(15);

  if (!request) {
    return (
      <View flex={1} justifyContent="center" alignItems="center" padding="$4">
        <Text>Request not found</Text>
        <Button onPress={() => router.back()} mt="$4">
          <Text>Go Back</Text>
        </Button>
      </View>
    );
  }

  useEffect(() => {
    // Simulate driver movement
    const interval = setInterval(() => {
      setDriverLocation(prev => [
        prev[0] + (Math.random() - 0.5) * 0.001,
        prev[1] + (Math.random() - 0.5) * 0.001
      ]);
      setEstimatedTime(prev => Math.max(1, prev - 1));
    }, 3000);

    return () => clearInterval(interval);
  }, []);



  const getMapRegion = () => {
    if (!pickupLoc || !pickupLoc.lat || !pickupLoc.lng) {
      return {
        latitude: 31.9539,
        longitude: 35.9106,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
    }

    return {
      latitude: pickupLoc.lat,
      longitude: pickupLoc.lng,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
  };

  const getPolylineCoordinates = () => {
    if (!pickupLoc) return [];

    const pickup = pickupLoc;
    const delivery = dropoffLoc || pickup;

    // Ensure we have valid coordinates
    if (!pickup.lat || !pickup.lng || !delivery.lat || !delivery.lng) return [];

    return [
      { latitude: pickup.lat, longitude: pickup.lng },
      { latitude: driverLocation[1], longitude: driverLocation[0] },
      { latitude: delivery.lat, longitude: delivery.lng },
    ];
  };

  return (
    <ScrollView style={{ flex: 1 }}>
      {/* Map Section */}
      <View style={{ height: height * 0.4 }}>
        <MapView
          style={{ width, height: height * 0.4 }}
          region={getMapRegion()}
          showsUserLocation={true}
        >
          {/* Pickup Marker */}
          {pickupLoc && pickupLoc.lat && pickupLoc.lng && (
            <Marker
              coordinate={{
                latitude: pickupLoc.lat,
                longitude: pickupLoc.lng,
              }}
              pinColor="green"
              title="Pickup Location"
            />
          )}

          {/* Delivery Marker */}
          {dropoffLoc && dropoffLoc.lat && dropoffLoc.lng && (
            <Marker
              coordinate={{
                latitude: dropoffLoc.lat,
                longitude: dropoffLoc.lng,
              }}
              pinColor="red"
              title="Delivery Location"
            />
          )}
          
          {/* Driver Marker */}
          <Marker
            coordinate={{
              latitude: driverLocation[1],
              longitude: driverLocation[0],
            }}
            pinColor="blue"
            title="Driver Location"
          />
          
          {/* Route Polyline */}
          <Polyline
            coordinates={getPolylineCoordinates()}
            strokeColor="#007AFF"
            strokeWidth={3}
          />
        </MapView>
      </View>

      {/* Tracking Info */}
      <View p="$4">
        <YStack gap="$4">
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500 }}
          >
            <YStack gap="$2" p="$4" bg="$blue2" br="$4">
              <Text fontSize="$6" fontWeight="bold" color="$blue11">
                Package in Transit
              </Text>
              <Text fontSize="$4" color="$blue10">
                Estimated arrival: {estimatedTime} minutes
              </Text>
            </YStack>
          </MotiView>

          {/* Driver Info */}
          {'driverName' in request && request.driverName && (
            <YStack gap="$3" p="$4" bg="white" br="$4" bw={1} bc="$gray5">
              <Text fontSize="$5" fontWeight="600">Driver Information</Text>
              <XStack gap="$3" alignItems="center">
                <View w={50} h={50} br={25} bg="$gray5" />
                <YStack flex={1}>
                  <Text fontSize="$4" fontWeight="500">{request.driverName}</Text>
                  <Text fontSize="$3" color="$gray10">★ 4.8 • Toyota Corolla</Text>
                </YStack>
                <Button
                  size="$3"
                  bg="$green9"
                  color="white"
                  onPress={() => Linking.openURL(`tel:${request.driverPhone}`)}
                  icon={<Ionicons name="call" size={16} color="white" />}
                >
                  <Text>Call</Text>
                </Button>
              </XStack>
            </YStack>
          )}

          {/* Package Details */}
          {request && (
            <YStack gap="$3" p="$4" bg="white" br="$4" bw={1} bc="$gray5">
              <Text fontSize="$5" fontWeight="600">Package Details</Text>
              <YStack gap="$2">
                {type === 'sent' && 'packageType' in request && (
                  <XStack justifyContent="space-between">
                    <Text color="$gray10">Type:</Text>
                    <Text fontWeight="500">{request.packageType || 'Standard'}</Text>
                  </XStack>
                )}
                {type === 'pickup' && 'itemDescription' in request && (
                  <XStack justifyContent="space-between">
                    <Text color="$gray10">Item:</Text>
                    <Text fontWeight="500">{request.itemDescription || 'Not specified'}</Text>
                  </XStack>
                )}
                {'notes' in request && request.notes && (
                  <XStack justifyContent="space-between">
                    <Text color="$gray10">Notes:</Text>
                    <Text fontWeight="500">{request.notes}</Text>
                  </XStack>
                )}
              </YStack>
            </YStack>
          )}

          <Button
            size="$4"
            variant="outlined"
            onPress={() => router.back()}
          >
            <Text>Back to Home</Text>
          </Button>
        </YStack>
      </View>
    </ScrollView>
  );
}
